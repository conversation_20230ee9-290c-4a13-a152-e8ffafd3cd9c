﻿// Dans DraftSurvey.Application/Extensions/ServiceExtensions.cs
using Microsoft.Extensions.DependencyInjection;
using MediatR;
using System.Reflection;
using DraftSurvey.Application.Features.Users.Queries;
using DraftSurvey.Application.Interfaces;
using DraftSurvey.Infrastructure.Services.Auth;
using DraftSurvey.Domain.Interfaces;
using DraftSurvey.Infrastructure.Repositories;
using DraftSurvey.Application.DTOs;

namespace DraftSurvey.Application
{
    public static class ServiceExtensions
    {
        public static void AddApplicationServices(this IServiceCollection services)
        {
            // Enregistrez MediatR avec l'assembly actuel
            //services.AddMediatR(cfg => cfg.RegisterServicesFromAssembly(Assembly.GetExecutingAssembly()));

            // Enregistrez manuellement le handler si nécessaire
            //services.AddScoped<IRequestHandler<AuthenticateUserQuery, AuthResponseDto>, AuthenticateUserQueryHandler>();

            // Ajoutez d'autres services d'application ici
        }
    }
}