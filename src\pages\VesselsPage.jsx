import React, { useState, useEffect } from "react";
import {
  Box, Typography, Button, CircularProgress, Alert, useTheme
} from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import Navbar from "../components/Navbar/Navbar";
import VesselList from "../components/Vessels/VesselList";
import VesselForm from "../components/Vessels/VesselForm";
import { getAllVessels, deleteVessel } from "../api/vesselsApi";

const VesselsPage = () => {
  const theme = useTheme();
  const [vessels, setVessels] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [openForm, setOpenForm] = useState(false);
  const [selectedVesselId, setSelectedVesselId] = useState(null);
  const [searchResults, setSearchResults] = useState(null);

  const fetchVessels = async () => {
    setLoading(true);
    setError(null);
    try {
      const data = await getAllVessels();
      setVessels(data);
      setSearchResults(null);
    } catch (err) {
      setError(err.message || "Failed to load vessels.");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchVessels();
  }, []);

  const handleOpenCreateForm = () => {
    setSelectedVesselId(null);
    setOpenForm(true);
  };

  const handleEditVessel = (vessel) => {
    setSelectedVesselId(vessel.id);
    setOpenForm(true);
  };

  const handleSaveVessel = () => {
    setOpenForm(false);
    fetchVessels();
  };

  const handleDeleteVessel = async (id) => {
    setLoading(true);
    try {
      await deleteVessel(id);
      await fetchVessels();
    } catch (err) {
      setError(err.message || "Failed to delete vessel.");
    } finally {
      setLoading(false);
    }
  };

  const handleSearchResults = (results) => {
    setSearchResults(results);
  };

  const handleClearSearch = () => {
    fetchVessels();
  };

  return (
    <>
      <Navbar />
      <Box sx={{
        pt: '80px',
        pb: 4,
        px: { xs: 2, sm: 4 },
        bgcolor: theme.palette.grey[100],
        minHeight: "100vh"
      }}>
        <Box sx={{
          width: '100%',
          maxWidth: 1400,
          margin: '0 auto',
          p: { xs: 2, sm: 4 },
          borderRadius: 2,
          bgcolor: "white",
          boxShadow: 3
        }}>
          <Box sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            mb: 3,
            pb: 2,
            borderBottom: `1px solid ${theme.palette.divider}`
          }}>
            <Typography variant="h4" color="black" fontWeight="bold">
              Gestion des Navires
            </Typography>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={handleOpenCreateForm}
              color="success"
            >
              Ajouter un Navire
            </Button>
          </Box>

          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
              <CircularProgress />
            </Box>
          ) : error ? (
            <Alert severity="error" sx={{ mb: 3 }}>{error}</Alert>
          ) : (
            <>
              <VesselList
                vessels={searchResults || vessels}
                onEdit={handleEditVessel}
                onDelete={handleDeleteVessel}
                onSearchResults={handleSearchResults}
              />
              {searchResults && (
                <Button onClick={handleClearSearch} sx={{ mt: 2 }}>
                  Annuler la recherche
                </Button>
              )}
            </>
          )}
        </Box>

        <VesselForm
          open={openForm}
          onClose={() => setOpenForm(false)}
          onSubmit={handleSaveVessel}
          vesselId={selectedVesselId}
        />
      </Box>
    </>
  );
};

export default VesselsPage;