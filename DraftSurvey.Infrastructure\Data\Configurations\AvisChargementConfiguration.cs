﻿// Dans DraftSurvey.Infrastructure/Data/Configurations/AvisChargementConfiguration.cs
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using DraftSurvey.Domain.Entities;

namespace DraftSurvey.Infrastructure.Data.Configurations
{
    public class AvisChargementConfiguration : IEntityTypeConfiguration<AvisChargement>
    {
        public void Configure(EntityTypeBuilder<AvisChargement> builder)
        {
            builder.HasKey(ac => ac.Id);

            builder.Property(ac => ac.CargoName)
                .IsRequired()
                .HasMaxLength(250);

            builder.Property(ac => ac.Unit)
                .HasMaxLength(50);

            builder.Property(ac => ac.LoadingPort)
                .HasMaxLength(100);

            builder.Property(ac => ac.DischargingPort)
                .HasMaxLength(100);

            builder.Property(ac => ac.Status)
                .IsRequired()
                .HasMaxLength(50);

            builder.Property(ac => ac.Notes)
                .HasMaxLength(1000);

            // Types de colonnes
            builder.Property(ac => ac.QuantityExpected).HasColumnType("float");

            // Relations
            builder.HasOne(ac => ac.Escale)
                .WithMany(e => e.AvisChargements) // Assurez-vous d'avoir ICollection<AvisChargement> dans Escale
                .HasForeignKey(ac => ac.EscaleId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.HasOne(ac => ac.CreatedByUser)
                .WithMany()
                .HasForeignKey(ac => ac.CreatedByUserId)
                .OnDelete(DeleteBehavior.Restrict);
        }
    }
}