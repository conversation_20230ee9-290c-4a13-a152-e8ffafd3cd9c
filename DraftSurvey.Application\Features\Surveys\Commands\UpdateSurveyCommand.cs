using MediatR;
using DraftSurvey.Application.DTOs;
using DraftSurvey.Domain.Interfaces;
using AutoMapper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using DraftSurvey.Application.DTOs;

namespace DraftSurvey.Application.Features.Surveys.Commands
{
    public class UpdateSurveyCommand : IRequest<Unit>
    {
        public Guid SurveyId { get; set; }
        public List<DraughtMarkReadingDto> MarkReadings { get; set; }
        public List<BallastDto> Ballasts { get; set; }
        public Guid UpdatedById { get; set; }
    }

    public class UpdateSurveyCommandHandler : IRequestHandler<UpdateSurveyCommand, Unit>
    {
        private readonly IDraftSurveyRepository _repository;
        private readonly IMapper _mapper;

        public UpdateSurveyCommandHandler(IDraftSurveyRepository repository, IMapper mapper)
        {
            _repository = repository;
            _mapper = mapper;
        }

        public async Task<Unit> Handle(UpdateSurveyCommand request, CancellationToken cancellationToken)
        {
            var survey = await _repository.GetByIdAsync(request.SurveyId);

            survey.DraughtMarkReading = _mapper.Map<Domain.Entities.DraughtMarkReading>(request.MarkReadings.First());
            survey.Ballasts = _mapper.Map<List<Domain.Entities.Ballast>>(request.Ballasts);
            survey.UpdatedDate = DateTime.UtcNow;
            survey.Status = Domain.Entities.DraftSurveyStatus.Intermediate;

            await _repository.UpdateAsync(survey);
            return Unit.Value;
        }
    }
}