import React, { useState, useEffect } from "react";
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  TextField,
  Button,
  Typography,
  MenuItem,
  IconButton,
  Box,
  FormControl,
  InputLabel,
  Select,
  CircularProgress,
  Alert, // Added Alert for consistency
  useTheme // Import useTheme
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import { createUser } from "../../api/usersApi"; // Only createUser here as this is for adding
import { getRoles } from "../../api/rolesApi";
import { getPorts } from "../../api/portsApi";

const UserForm = ({ user, onSave, onClose }) => {
  const theme = useTheme(); // Access the theme

  const [formData, setFormData] = useState({
    username: user?.username || '',
    email: user?.email || '',
    password: '',
    fullName: user?.fullName || '',
    roleId: user?.role?.id || user?.roleId || '',
    portId: user?.port?.id || user?.portId || ''
  });

  const [roles, setRoles] = useState([]);
  const [ports, setPorts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState(null); // Added error state

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [rolesData, portsData] = await Promise.all([
          getRoles(),
          getPorts()
        ]);
        setRoles(rolesData);
        setPorts(portsData);
      } catch (err) {
        console.error("Error loading data:", err);
        setError("Failed to load necessary data.");
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, []);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setSubmitting(true);
    setError(null); // Clear previous errors

    try {
      const createdUser = await createUser(formData); // Use formData directly for creation
      onSave(createdUser);
      onClose();
    } catch (err) {
      console.error("Error saving user:", err);
      setError(err.response?.data?.message || "Error creating user."); // More specific error
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <Dialog open onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle sx={{
        display: "flex",
        justifyContent: "space-between",
        alignItems: "center",
        bgcolor: "black", // Use theme primary color
        color: "white",
        py: 2,
        borderBottom: `1px solid ${theme.palette.divider}`,
        boxShadow: theme.shadows[1]
      }}>
        <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
          {user ? "Modifier l'utilisateur" : "Ajouter un utilisateur"}
        </Typography>
        <IconButton onClick={onClose} sx={{ color: "white" }}>
          <CloseIcon />
        </IconButton>
      </DialogTitle>
      <DialogContent sx={{ p: 3, bgcolor: theme.palette.background.paper }}>
        {loading ? (
          <Box display="flex" justifyContent="center" py={4}>
            <CircularProgress color="primary" />
          </Box>
        ) : (
          <form onSubmit={handleSubmit}>
            {error && (
              <Alert severity="error" sx={{ mb: 2 }}>
                {error}
              </Alert>
            )}

            <TextField
              label="Nom d'utilisateur"
              fullWidth
              name="username"
              value={formData.username}
              onChange={handleChange}
              required
              margin="normal"
              variant="outlined"
              disabled={submitting}
            />

            <TextField
              label="Email"
              fullWidth
              name="email"
              type="email"
              value={formData.email}
              onChange={handleChange}
              required
              margin="normal"
              variant="outlined"
              disabled={submitting}
            />

            {!user && ( // Only show password field for new user creation
              <TextField
                label="Mot de passe"
                fullWidth
                name="password"
                type="password"
                value={formData.password}
                onChange={handleChange}
                required
                margin="normal"
                variant="outlined"
                disabled={submitting}
              />
            )}

            <TextField
              label="Nom complet"
              fullWidth
              name="fullName"
              value={formData.fullName}
              onChange={handleChange}
              required
              margin="normal"
              variant="outlined"
              disabled={submitting}
            />

            <FormControl fullWidth margin="normal" required disabled={submitting}>
              <InputLabel>Rôle</InputLabel>
              <Select
                name="roleId"
                value={formData.roleId}
                onChange={handleChange}
                label="Rôle"
                variant="outlined"
              >
                {roles.map(role => (
                  <MenuItem key={role.id} value={role.id}>
                    {role.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <FormControl fullWidth margin="normal" disabled={submitting}>
              <InputLabel>Port (optionnel)</InputLabel>
              <Select
                name="portId"
                value={formData.portId || ''}
                onChange={handleChange}
                label="Port (optionnel)"
                variant="outlined"
              >
                <MenuItem value="">Aucun port</MenuItem>
                {ports.map(port => (
                  <MenuItem key={port.id} value={port.id}>
                    {port.name} ({port.code})
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <Box display="flex" justifyContent="flex-end" gap={2} mt={3}>
              <Button
                onClick={onClose}
                variant="outlined"
                color="error"
                disabled={submitting}
                sx={{
                    '&:hover': {
                        borderColor: theme.palette.error.dark,
                        backgroundColor: theme.palette.error.light + '10'
                    }
                }}
              >
                Annuler
              </Button>
              <Button
                type="submit"
                variant="contained"
                color="black"
                disabled={submitting}
                startIcon={submitting ? <CircularProgress size={20} color="inherit" /> : null}
                sx={{
                    '&:hover': {
                        backgroundColor: theme.palette.primary.dark,
                    }
                }}
              >
                {submitting ? "Enregistrement..." : "Enregistrer"}
              </Button>
            </Box>
          </form>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default UserForm;