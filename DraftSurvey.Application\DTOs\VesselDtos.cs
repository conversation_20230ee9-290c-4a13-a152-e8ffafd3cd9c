using System;
using System.Collections.Generic;

namespace DraftSurvey.Application.DTOs
{
    public class VesselDto
    {
        public Guid Id { get; set; }
        public string Name { get; set; }
        public string IMONumber { get; set; }
        public string Type { get; set; }
        public double Length { get; set; }
        public double Width { get; set; }
        public double Draft { get; set; }
        public double DeadweightTonnage { get; set; }
    }

    public class VesselDetailsDto : VesselDto
    {
        public List<SurveyDto> RecentSurveys { get; set; }
        public VesselParametersDto Parameters { get; set; }
        public HydrostaticDataDto HydrostaticData { get; set; }
    }

    public class VesselParametersDto
    {
        public double LengthBetweenPerpendiculars { get; set; }
        public double LongitudinalCenterOfFloatation { get; set; }
        public double MomentToChangeTrim { get; set; }
    }

    public class HydrostaticDataDto
    {
        public double DisplacementFactor { get; set; }
        public double TPC { get; set; }
    }

    public class VesselCreateDto
    {
        public string Name { get; set; }
        public string IMONumber { get; set; }
        public string Type { get; set; }
        public double Length { get; set; }
        public double Width { get; set; }
        public double Draft { get; set; }
        public double DeadweightTonnage { get; set; }
    }

    public class VesselUpdateDto
    {
        public string Name { get; set; }
        public string Type { get; set; }
        public double Length { get; set; }
        public double Width { get; set; }
        public double Draft { get; set; }
        public double DeadweightTonnage { get; set; }
    }
}