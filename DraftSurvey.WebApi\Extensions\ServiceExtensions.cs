﻿// Dans DraftSurvey.Infrastructure/Extensions/ServiceExtensions.cs
using Microsoft.Extensions.DependencyInjection;
using DraftSurvey.Infrastructure.Services.Auth;
using DraftSurvey.Application.Interfaces;
using DraftSurvey.Domain.Interfaces;
using DraftSurvey.Infrastructure.Repositories;

namespace DraftSurvey.Infrastructure
{
    public static class ServiceExtensions
    {
        public static void AddInfrastructureServices(this IServiceCollection services)
        {
            // Enregistrez les services d'infrastructure
            //services.AddScoped<IAuthService, AuthService>();
            //services.AddScoped<IUserRepository, UserRepository>();

            // Ajoutez d'autres services d'infrastructure ici
        }
    }
}