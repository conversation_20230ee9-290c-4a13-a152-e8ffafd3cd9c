import instance from "./axiosConfig";

const API_URL = "/ports";

export const getPorts = async () => {
  const response = await instance.get(API_URL);
  return response.data;
};

export const getPortById = async (id) => {
  const response = await instance.get(`${API_URL}/${id}`);
  return response.data;
};

export const searchPorts = async (term) => {
  const response = await instance.get(`${API_URL}/search?term=${term}`);
  return response.data;
};

export const createPort = async (portData) => {
  const response = await instance.post(API_URL, portData);
  return response.data;
};

export const updatePort = async (id, portData) => {
  await instance.put(`${API_URL}/${id}`, portData);
};

export const deletePort = async (id) => {
  await instance.delete(`${API_URL}/${id}`);
};

export const updateWaterDensity = async (id, density) => {
  await instance.put(`${API_URL}/${id}/water-density`, { density });
};

export const assignPortManager = async (portId, userId) => {
  await instance.put(`${API_URL}/${portId}/assign-manager/${userId}`);
};

export const getPortStaff = async (portId) => {
  const response = await instance.get(`${API_URL}/${portId}/staff`);
  return response.data;
};