﻿// DraftSurvey.Application/DTOs/FreshWaterTankDtos.cs
using System;
using System.ComponentModel.DataAnnotations;

namespace DraftSurvey.Application.DTOs
{
    // DTO pour lecture
    public class FreshWaterTankDto
    {
        public Guid Id { get; set; }

        public string TankName { get; set; }

        public double Volume { get; set; }

        public double Density { get; set; } = 1.0;

        public double TotalWeight { get; set; }

        public Guid DraftSurveyId { get; set; } // ✅ nom corrigé
        public string Notes { get; set; } = string.Empty; // 👈 Ajouté

    }

    // DTO pour création
    public class FreshWaterTankCreateDto
    {
        [Required]
        public string TankName { get; set; }

        [Required]
        public double Volume { get; set; }

        public double? Density { get; set; }

        [Required]
        public Guid DraftSurveyId { get; set; } // ✅ nom corrigé
        public string Notes { get; set; } = string.Empty; // 👈 Ajouté

    }

    // DTO pour mise à jour
    public class FreshWaterTankUpdateDto
    {
        [Required]
        public string TankName { get; set; }

        [Required]
        public double Volume { get; set; }

        public double? Density { get; set; }
    }
}
