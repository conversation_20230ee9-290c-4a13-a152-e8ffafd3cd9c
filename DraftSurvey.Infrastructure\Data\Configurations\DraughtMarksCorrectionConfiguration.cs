﻿// Dans DraftSurvey.Infrastructure/Data/Configurations/DraughtMarksCorrectionConfiguration.cs
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using DraftSurvey.Domain.Entities;

namespace DraftSurvey.Infrastructure.Data.Configurations
{
    public class DraughtMarksCorrectionConfiguration : IEntityTypeConfiguration<DraughtMarksCorrection>
    {
        public void Configure(EntityTypeBuilder<DraughtMarksCorrection> builder)
        {
            builder.HasKey(dmc => dmc.Id);

            builder.Property(dmc => dmc.CorrectionType)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(dmc => dmc.Unit)
                .HasMaxLength(20);

            builder.Property(dmc => dmc.AppliedToLocation)
                .HasMaxLength(50);

            builder.Property(dmc => dmc.Notes)
                .HasMaxLength(500);

            // Relation
            builder.HasOne(dmc => dmc.DraftSurvey)
                .WithMany(ds => ds.DraughtMarksCorrections) // Assurez-vous d'avoir ICollection<DraughtMarksCorrection> dans DraftSurvey
                .HasForeignKey(dmc => dmc.DraftSurveyId)
                .OnDelete(DeleteBehavior.Cascade); // Si le survey est supprimé, les corrections aussi

            // Types de colonnes pour la précision
            builder.Property(dmc => dmc.Value).HasColumnType("float");
        }
    }
}