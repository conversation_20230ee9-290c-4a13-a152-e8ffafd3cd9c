using AutoMapper;
using DraftSurvey.Application.DTOs;
using DraftSurvey.Domain.Entities;
using DraftSurvey.Domain.Interfaces;
using DraftSurvey.Infrastructure.Data;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace DraftSurvey.WebApi.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Produces("application/json")]
    public class PortsController : ControllerBase
    {
        private readonly IPortRepository _portRepository;
        private readonly IMapper _mapper;
        private readonly DraftSurveyDbContext _context;

        public PortsController(
            IPortRepository portRepository,
            IMapper mapper,
            DraftSurveyDbContext context)
        {
            _portRepository = portRepository;
            _mapper = mapper;
            _context = context;
        }

        [HttpGet]
        [ProducesResponseType(typeof(IEnumerable<PortDto>), 200)]
        public async Task<ActionResult<IEnumerable<PortDto>>> GetAll()
        {
            var ports = await _portRepository.GetAllAsync();
            return Ok(_mapper.Map<IEnumerable<PortDto>>(ports));
        }

        [HttpGet("{id}")]
        [ProducesResponseType(typeof(PortDetailsDto), 200)]
        [ProducesResponseType(404)]
        public async Task<ActionResult<PortDetailsDto>> GetById(Guid id)
        {
            var port = await _portRepository.GetByIdAsync(id);
            if (port == null) return NotFound();

            var result = _mapper.Map<PortDetailsDto>(port);
            result.EscalesCount = await _context.Escales.CountAsync(e => e.PortId == id);
            result.UsersCount = await _context.Users.CountAsync(u => u.PortId == id);

            return Ok(result);
        }

        [HttpPost]
        [Authorize(Roles = "Admin,TeamLead")]
        [ProducesResponseType(typeof(PortDto), 201)]
        [ProducesResponseType(400)]
        public async Task<ActionResult<PortDto>> Create([FromBody] PortCreateDto dto)
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            var port = _mapper.Map<Port>(dto);
            await _portRepository.AddAsync(port);

            try
            {
                await _context.SaveChangesAsync();
                return CreatedAtAction(nameof(GetById),
                    new { id = port.Id },
                    _mapper.Map<PortDto>(port));
            }
            catch (DbUpdateException ex)
            {
                return BadRequest(new
                {
                    Error = "Database error",
                    Message = ex.InnerException?.Message ?? ex.Message
                });
            }
        }

        [HttpPut("{id}")]
        [Authorize(Roles = "Admin,TeamLead")]
        [ProducesResponseType(204)]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        public async Task<IActionResult> Update(Guid id, [FromBody] PortUpdateDto dto)
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            var port = await _portRepository.GetByIdAsync(id);
            if (port == null) return NotFound();

            _mapper.Map(dto, port);
            _portRepository.UpdateAsync(port);

            try
            {
                await _context.SaveChangesAsync();
                return NoContent();
            }
            catch (DbUpdateException ex)
            {
                return BadRequest(new
                {
                    Error = "Database error",
                    Message = ex.InnerException?.Message ?? ex.Message
                });
            }
        }

        [HttpDelete("{id}")]
        [Authorize(Roles = "Admin,TeamLead")]
        [ProducesResponseType(204)]
        [ProducesResponseType(404)]
        public async Task<IActionResult> Delete(Guid id)
        {
            var port = await _portRepository.GetByIdAsync(id);
            if (port == null) return NotFound();

            _portRepository.DeleteAsync(id);

            try
            {
                await _context.SaveChangesAsync();
                return NoContent();
            }
            catch (DbUpdateException ex)
            {
                return BadRequest(new
                {
                    Error = "Database error",
                    Message = ex.InnerException?.Message ?? ex.Message
                });
            }
        }

        [HttpGet("search")]
        [ProducesResponseType(typeof(IEnumerable<PortDto>), 200)]
        public async Task<ActionResult<IEnumerable<PortDto>>> Search([FromQuery] string term)
        {
            if (string.IsNullOrWhiteSpace(term))
                return BadRequest("Search term cannot be empty");

            var ports = await _portRepository.SearchAsync(term);
            return Ok(_mapper.Map<IEnumerable<PortDto>>(ports));
        }
    }
}