import React, { useState, useEffect } from 'react';
import {
  Paper, Typography, Grid, Divider,
  Chip, Stack, Box, CircularProgress, Alert,
  Card, CardContent, useTheme // Import Card, CardContent, useTheme
} from '@mui/material';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';
import { getVesselById, getPortById } from '../../api/escaleApi';

const EscaleDetails = ({ escale }) => {
  const theme = useTheme(); // Access the theme

  const [vesselDetails, setVesselDetails] = useState(null);
  const [portDetails, setPortDetails] = useState(null);
  const [loading, setLoading] = useState(true); // Combined loading for details
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchDetails = async () => {
      if (!escale) return;

      setLoading(true);
      setError(null);
      try {
        const [vessel, port] = await Promise.all([
          getVesselById(escale.vesselId),
          getPortById(escale.portId)
        ]);
        setVesselDetails(vessel);
        setPortDetails(port);
      } catch (err) {
        setError(err.response?.data?.message || err.message || "Impossible de charger les détails associés.");
        console.error("Fetch Escale Details Error:", err);
      } finally {
        setLoading(false);
      }
    };

    fetchDetails();
  }, [escale]); // Re-fetch when the escale prop changes

  if (!escale) {
    return (
      <Typography variant="body1" color="textSecondary" sx={{ textAlign: 'center', py: 3 }}>
        Aucune escale sélectionnée pour afficher les détails.
      </Typography>
    );
  }

  return (
    <Box>
      <Card sx={{
        mb: 3,
        boxShadow: theme.shadows[2], // Subtle shadow for the main card
        borderRadius: theme.shape.borderRadius * 1.5,
        border: `1px solid ${theme.palette.grey[200]}`
      }}>
        <CardContent>
          <Typography variant="h5" sx={{
            fontWeight: "bold",
            mb: 2,
            color:"black",
            borderBottom: `2px solid ${theme.palette.primary.light}`,
            pb: 1,
          }}>
            Informations de l'Escale
          </Typography>

          {loading ? (
            <Box sx={{ textAlign: 'center', my: 2 }}>
              <CircularProgress color="primary" />
              <Typography variant="body2" color="textSecondary" sx={{ mt: 1 }}>Chargement des détails...</Typography>
            </Box>
          ) : error ? (
            <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>
          ) : (
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Typography variant="body1" sx={{ mb: 1.5 }}>
                  <strong>Référence:</strong> <Chip label={escale.reference} color="primary" size="small" sx={{ ml: 1, fontWeight: 'bold' }} />
                </Typography>
                <Typography variant="body1" sx={{ mb: 1.5 }}>
                  <strong>Navire:</strong> {vesselDetails?.name || 'N/A'}
                  {vesselDetails && ` (IMO: ${vesselDetails.imoNumber})`}
                </Typography>
                <Typography variant="body1" sx={{ mb: 1.5 }}>
                  <strong>Port:</strong> {portDetails?.name || 'N/A'}
                  {portDetails && ` (${portDetails.country})`}
                </Typography>
              </Grid>
              <Grid item xs={12} md={6}>
                <Typography variant="body1" sx={{ mb: 1.5 }}>
                  <strong>Date d'arrivée:</strong> {escale.arrivalDate ? format(new Date(escale.arrivalDate), 'dd MMMM yyyy à HH:mm', { locale: fr }) : 'N/A'}
                </Typography>
                {escale.departureDate && (
                  <Typography variant="body1" sx={{ mb: 1.5 }}>
                    <strong>Date de départ:</strong> {format(new Date(escale.departureDate), 'dd MMMM yyyy à HH:mm', { locale: fr })}
                  </Typography>
                )}
                <Stack direction="row" spacing={1} alignItems="center" sx={{ mt: 1.5 }}>
                  <Typography variant="body1"><strong>Statut:</strong></Typography>
                  <Chip
                    label={escale.departureDate ? "Terminée" : "Active"}
                    color={escale.departureDate ? "default" : "success"}
                    variant="filled" // Use filled for more emphasis
                    size="small"
                  />
                </Stack>
              </Grid>
            </Grid>
          )}
        </CardContent>
      </Card>

      {/* Detailed Vessel Information Card */}
      {vesselDetails && (
        <Card sx={{
          mb: 3,
          boxShadow: theme.shadows[2],
          borderRadius: theme.shape.borderRadius * 1.5,
          border: `1px solid ${theme.palette.grey[200]}`
        }}>
          <CardContent>
            <Typography variant="h6" sx={{
              fontWeight: 'bold',
              color: "black", // Use a different theme color
              mb: 2,
              borderBottom: `1px solid ${theme.palette.secondary.light}`,
              pb: 0.5,
            }}>
              Détails du Navire
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6} md={3}>
                <Typography variant="body2"><strong>Type:</strong> {vesselDetails.type || 'N/A'}</Typography>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Typography variant="body2"><strong>Longueur:</strong> {vesselDetails.length ? `${vesselDetails.length} m` : 'N/A'}</Typography>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Typography variant="body2"><strong>Largeur:</strong> {vesselDetails.width ? `${vesselDetails.width} m` : 'N/A'}</Typography>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Typography variant="body2"><strong>Tirant d'eau:</strong> {vesselDetails.draft ? `${vesselDetails.draft} m` : 'N/A'}</Typography>
              </Grid>
              {/* Add more vessel details as needed */}
            </Grid>
          </CardContent>
        </Card>
      )}

      {/* Detailed Port Information Card */}
      {portDetails && (
        <Card sx={{
          boxShadow: theme.shadows[2],
          borderRadius: theme.shape.borderRadius * 1.5,
          border: `1px solid ${theme.palette.grey[200]}`
        }}>
          <CardContent>
            <Typography variant="h6" sx={{
              fontWeight: 'bold',
              color: "black", // Use another theme color
              mb: 2,
              borderBottom: `1px solid ${theme.palette.info.light}`,
              pb: 0.5,
            }}>
              Détails du Port
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6} md={3}>
                <Typography variant="body2"><strong>Pays:</strong> {portDetails.country || 'N/A'}</Typography>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Typography variant="body2"><strong>Code:</strong> <Chip label={portDetails.code} size="small" variant="outlined" sx={{ ml: 0.5 }} /></Typography>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Typography variant="body2"><strong>Densité de l'eau:</strong> {portDetails.standardWaterDensity ? `${portDetails.standardWaterDensity} t/m³` : 'N/A'}</Typography>
              </Grid>
              {/* Add more port details as needed */}
            </Grid>
          </CardContent>
        </Card>
      )}
    </Box>
  );
};

export default EscaleDetails;