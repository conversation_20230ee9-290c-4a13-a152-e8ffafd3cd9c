﻿// In C:\d\DraftSurveySolution\DraftSurvey.Infrastructure\Data\Configurations\DocumentConfiguration.cs

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using DraftSurvey.Domain.Entities; // Adjust namespace if your Document entity is elsewhere

namespace DraftSurvey.Infrastructure.Data.Configurations
{
    public class DocumentConfiguration : IEntityTypeConfiguration<Document>
    {
        public void Configure(EntityTypeBuilder<Document> builder)
        {
            builder.HasKey(d => d.Id);

            builder.Property(d => d.FileName)
                .IsRequired()
                .HasMaxLength(255);

            builder.Property(d => d.FilePath)
                .IsRequired()
                .HasMaxLength(500); // Path where the document is stored

            builder.Property(d => d.DocumentType)
                .HasMaxLength(50)
                .IsRequired(); // e.g., "PDF", "Image", "Report"

            builder.Property(d => d.UploadDate)
                .IsRequired();

            // Add any other properties from your Document entity here with their configurations
        }
    }
}