using DraftSurvey.Domain.Entities;
using Microsoft.EntityFrameworkCore;

namespace DraftSurvey.Infrastructure.Data
{
    public class DraftSurveyDbContext : DbContext
    {
        public DraftSurveyDbContext(DbContextOptions<DraftSurveyDbContext> options)
            : base(options) { }

        public DbSet<Domain.Entities.DraftSurvey> DraftSurveys { get; set; }
        public DbSet<User> Users { get; set; }
        public DbSet<Vessel> Vessels { get; set; }
        public DbSet<Escale> Escales { get; set; }
        public DbSet<Port> Ports { get; set; }
        public DbSet<Role> Roles { get; set; }
        public DbSet<Ballast> Ballasts { get; set; }
        public DbSet<DraughtMarkReading> DraughtMarkReadings { get; set; }
        public DbSet<LiquidStatement> LiquidStatements { get; set; }
        public DbSet<TaskAssignment> TaskAssignments { get; set; }
        public DbSet<DraughtSurveyGlobalCalculation> DraughtSurveyGlobalCalculations { get; set; }
        public DbSet<DraughtMarksCorrection> DraughtMarksCorrections { get; set; }
        public DbSet<FreshWaterTank> FreshWaterTanks { get; set; }
        public DbSet<SeaWaterDensityStatement> SeaWaterDensityStatements { get; set; }
        public DbSet<Liquid> Liquids { get; set; }
        public DbSet<Document> Documents { get; set; }
        public DbSet<DraughtSurveyDocument> DraughtSurveyDocuments { get; set; }
        public DbSet<HoldInspection> HoldInspections { get; set; }
        public DbSet<InspectionReport> InspectionReports { get; set; }
        public DbSet<AvisChargement> AvisChargements { get; set; }
        public DbSet<Inspection> Inspections { get; set; } // Add this for Inspection

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);
            modelBuilder.ApplyConfigurationsFromAssembly(typeof(DraftSurveyDbContext).Assembly);
        }

    }
}