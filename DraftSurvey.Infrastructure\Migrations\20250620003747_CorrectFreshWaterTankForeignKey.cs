﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace DraftSurvey.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class CorrectFreshWaterTankForeignKey : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_FreshWaterTanks_DraftSurveys_SurveyId",
                table: "FreshWaterTanks");

            migrationBuilder.DropForeignKey(
                name: "FK_HoldInspections_Inspection_InspectionId",
                table: "HoldInspections");

            migrationBuilder.DropForeignKey(
                name: "FK_Inspection_Escales_EscaleId",
                table: "Inspection");

            migrationBuilder.DropForeignKey(
                name: "FK_InspectionReports_Inspection_InspectionId",
                table: "InspectionReports");

            migrationBuilder.DropPrimaryKey(
                name: "PK_Inspection",
                table: "Inspection");

            migrationBuilder.RenameTable(
                name: "Inspection",
                newName: "Inspections");

            migrationBuilder.RenameColumn(
                name: "SurveyId",
                table: "FreshWaterTanks",
                newName: "DraftSurveyId");

            migrationBuilder.RenameIndex(
                name: "IX_FreshWaterTanks_SurveyId",
                table: "FreshWaterTanks",
                newName: "IX_FreshWaterTanks_DraftSurveyId");

            migrationBuilder.RenameIndex(
                name: "IX_Inspection_EscaleId",
                table: "Inspections",
                newName: "IX_Inspections_EscaleId");

            migrationBuilder.AddPrimaryKey(
                name: "PK_Inspections",
                table: "Inspections",
                column: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_FreshWaterTanks_DraftSurveys_DraftSurveyId",
                table: "FreshWaterTanks",
                column: "DraftSurveyId",
                principalTable: "DraftSurveys",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_HoldInspections_Inspections_InspectionId",
                table: "HoldInspections",
                column: "InspectionId",
                principalTable: "Inspections",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_InspectionReports_Inspections_InspectionId",
                table: "InspectionReports",
                column: "InspectionId",
                principalTable: "Inspections",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_Inspections_Escales_EscaleId",
                table: "Inspections",
                column: "EscaleId",
                principalTable: "Escales",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_FreshWaterTanks_DraftSurveys_DraftSurveyId",
                table: "FreshWaterTanks");

            migrationBuilder.DropForeignKey(
                name: "FK_HoldInspections_Inspections_InspectionId",
                table: "HoldInspections");

            migrationBuilder.DropForeignKey(
                name: "FK_InspectionReports_Inspections_InspectionId",
                table: "InspectionReports");

            migrationBuilder.DropForeignKey(
                name: "FK_Inspections_Escales_EscaleId",
                table: "Inspections");

            migrationBuilder.DropPrimaryKey(
                name: "PK_Inspections",
                table: "Inspections");

            migrationBuilder.RenameTable(
                name: "Inspections",
                newName: "Inspection");

            migrationBuilder.RenameColumn(
                name: "DraftSurveyId",
                table: "FreshWaterTanks",
                newName: "SurveyId");

            migrationBuilder.RenameIndex(
                name: "IX_FreshWaterTanks_DraftSurveyId",
                table: "FreshWaterTanks",
                newName: "IX_FreshWaterTanks_SurveyId");

            migrationBuilder.RenameIndex(
                name: "IX_Inspections_EscaleId",
                table: "Inspection",
                newName: "IX_Inspection_EscaleId");

            migrationBuilder.AddPrimaryKey(
                name: "PK_Inspection",
                table: "Inspection",
                column: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_FreshWaterTanks_DraftSurveys_SurveyId",
                table: "FreshWaterTanks",
                column: "SurveyId",
                principalTable: "DraftSurveys",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_HoldInspections_Inspection_InspectionId",
                table: "HoldInspections",
                column: "InspectionId",
                principalTable: "Inspection",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_Inspection_Escales_EscaleId",
                table: "Inspection",
                column: "EscaleId",
                principalTable: "Escales",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_InspectionReports_Inspection_InspectionId",
                table: "InspectionReports",
                column: "InspectionId",
                principalTable: "Inspection",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
