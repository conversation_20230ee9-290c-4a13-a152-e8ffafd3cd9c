import React, { useState, useEffect } from "react";
import {
  Grid,
  Box,
  Typography,
  Button,
  CircularProgress,
  useTheme, // Import useTheme for consistent styling
  useMediaQuery, // Import useMediaQuery for responsive adjustments
} from "@mui/material";
import UserList from "../components/Users/<USER>";
import UserEditForm from "../components/Users/<USER>";
import UserForm from "../components/Users/<USER>";
import Navbar from "../components/Navbar/Navbar"; // Your Navbar

import { getUsers, createUser, updateUser, deleteUser } from "../api/usersApi";

const UserManagementPage = () => {
  const [users, setUsers] = useState([]);
  const [openForm, setOpenForm] = useState(false); // For UserForm (Add)
  const [editUser, setEditUser] = useState(null); // For UserEditForm (Edit)
  const [loading, setLoading] = useState(true);

  const theme = useTheme(); // Access the theme
  const isMobile = useMediaQuery(theme.breakpoints.down("sm")); // Check for mobile screens

  // Fetch users on component mount
  useEffect(() => {
    const fetchUsers = async () => {
      try {
        const data = await getUsers();
        setUsers(data);
      } catch (error) {
        console.error("Error fetching users:", error);
        // Potentially show an error message to the user
      } finally {
        setLoading(false); // Ensure loading is set to false even on error
      }
    };
    fetchUsers();
  }, []);

  const handleAddUser = () => {
    setEditUser(null); // Ensure edit form is closed
    setOpenForm(true); // Open the add user form
  };

  const handleEditUser = (user) => {
    setOpenForm(false); // Ensure add form is closed
    setEditUser(user); // Set user for edit form
  };

  const handleDeleteUser = async (id) => {
    try {
      await deleteUser(id);
      setUsers(users.filter((user) => user.id !== id));
      // Optionally show a success notification
    } catch (error) {
      console.error("Error deleting user:", error);
      // Optionally show an error notification
    }
  };

  const handleSaveUser = async (userData) => {
    try {
      if (userData.id) {
        // Update existing user
        await updateUser(userData.id, userData);
        setUsers(users.map((u) => (u.id === userData.id ? userData : u)));
        setEditUser(null); // Close edit form after saving
      } else {
        // Create new user
        const createdUser = await createUser(userData);
        setUsers([...users, createdUser]);
        setOpenForm(false); // Close add form after saving
      }
      // Optionally show a success notification
    } catch (error) {
      console.error("Error saving user:", error);
      // Optionally show an error notification
    }
  };

  // Loading state render
  if (loading) {
    return (
      <Box
        sx={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          minHeight: "100vh", // Full viewport height
          bgcolor: theme.palette.background.default, // Use theme background color (should be white now)
        }}
      >
        <CircularProgress sx={{ color: theme.palette.primary.main }} />{" "}
        {/* Primary color loading spinner */}
      </Box>
    );
  }

  return (
    <Box sx={{ display: "flex", flexDirection: "column", minHeight: "100vh", bgcolor: theme.palette.background.default }}>
      <Navbar /> {/* Navbar is fixed and dark, contrasting nicely */}

      <Box
        sx={{
          flexGrow: 1, // Allows content to take available space
          pt: { xs: '72px', sm: '80px', md: '88px' }, // Padding top to clear fixed Navbar (adjust as needed)
          pb: 4, // Padding bottom
          px: { xs: 2, sm: 4, md: 6 }, // Responsive horizontal padding
          display: "flex",
          justifyContent: "center",
          alignItems: "flex-start", // Align items to the top to prevent excessive vertical centering
          bgcolor: theme.palette.grey[100], // A very light grey background for the overall page section
          // For a pure hard white background, you could use: bgcolor: 'white',
        }}
      >
        <Box
          sx={{
            width: isMobile ? "100%" : 1200, // Full width on mobile, max 1200px on desktop
            maxWidth: "100%", // Ensure it doesn't overflow on very small screens
            padding: isMobile ? 2 : 4, // More padding on desktop
            borderRadius: theme.shape.borderRadius * 2, // More pronounced rounded corners
            boxShadow: theme.shadows[8], // A strong, crisp shadow for a "floating" effect
            bgcolor: "white", // Hard white background for the main content card
            color: theme.palette.text.primary, // Default text color (dark)
            border: `1px solid ${theme.palette.grey[300]}`, // Subtle light grey border for crispness
          }}
        >
          <Typography
            variant={isMobile ? "h5" : "h4"} // Smaller heading on mobile
            sx={{
              textAlign: "left",
              color: theme.palette.text.primary, // Dark text for title
              fontWeight: "bold",
              mb: 3, // Margin bottom for spacing
            }}
          >
            Manage Users
          </Typography>

          <Button
            variant="contained"
            onClick={handleAddUser}
            sx={{
              bgcolor: theme.palette.success.main, // Green for 'Add'
              color: "white",
              fontWeight: "bold",
              "&:hover": {
                bgcolor: theme.palette.success.dark, // Darker green on hover
                transform: "translateY(-2px)", // Subtle lift effect
                boxShadow: theme.shadows[4],
              },
              mb: 3,
              px: isMobile ? 2 : 3, // Responsive padding
              py: isMobile ? 1 : 1.5, // Responsive padding
              borderRadius: theme.shape.borderRadius,
            }}
          >
            Add New User
          </Button>

          {/* User List and Forms */}
          <Box sx={{ mt: 3 }}>
            <UserList users={users} onEdit={handleEditUser} onDelete={handleDeleteUser} />
          </Box>

          {openForm && (
            <UserForm
              user={null} // For adding, user is null
              onSave={handleSaveUser}
              onClose={() => setOpenForm(false)}
            />
          )}
          {editUser && (
            <UserEditForm
              user={editUser}
              onSave={handleSaveUser} // Use handleSaveUser for both create/update
              onClose={() => setEditUser(null)}
            />
          )}
        </Box>
      </Box>
    </Box>
  );
};

export default UserManagementPage;