import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Typography, 
  Grid,
  Card,
  CardContent
} from '@mui/material';
import { getBallastsBySurvey } from '../api/ballastsApi';
import { getReadingsBySurvey } from '../api/draughtMarkReadingsApi';
import { getTanksBySurvey } from '../api/freshWaterTankApi';
import { getDensityBySurvey } from '../api/seaWaterDensityApi';
import { getLiquidStatementsBySurvey } from '../api/liquidStatementApi';
import { getCorrectionsBySurvey } from '../api/draughtMarksCorrectionApi';

const SurveyCalculationPage = ({ surveyId }) => {
  const [ballasts, setBallasts] = useState([]);
  const [readings, setReadings] = useState(null);
  const [freshWaterTanks, setFreshWaterTanks] = useState([]);
  const [density, setDensity] = useState(null);
  const [liquidStatements, setLiquidStatements] = useState([]);
  const [corrections, setCorrections] = useState([]);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [
          ballastsData, 
          readingsData, 
          tanksData, 
          densityData,
          liquidsData,
          correctionsData
        ] = await Promise.all([
          getBallastsBySurvey(surveyId),
          getReadingsBySurvey(surveyId),
          getTanksBySurvey(surveyId),
          getDensityBySurvey(surveyId),
          getLiquidStatementsBySurvey(surveyId),
          getCorrectionsBySurvey(surveyId)
        ]);
        
        setBallasts(ballastsData);
        setReadings(readingsData);
        setFreshWaterTanks(tanksData);
        setDensity(densityData);
        setLiquidStatements(liquidsData);
        setCorrections(correctionsData);
      } catch (error) {
        console.error('Error fetching data:', error);
      }
    };
    
    fetchData();
  }, [surveyId]);

  // Fonctions de calcul
  const calculateTotalWeight = (items) => items.reduce((sum, item) => sum + (item.volume * item.density), 0);

  const calculateMeanDraft = () => {
    if (!readings) return 0;
    
    const forward = (readings.portForward + readings.starboardForward) / 2;
    const midship = (readings.portMidship + readings.starboardMidship) / 2;
    const aft = (readings.portAft + readings.starboardAft) / 2;
    
    return ((forward + (6 * midship) + aft) / 8).toFixed(2);
  };

  const totalBallastWeight = calculateTotalWeight(ballasts);
  const totalFreshWaterWeight = calculateTotalWeight(freshWaterTanks);
  const totalLiquidsWeight = calculateTotalWeight(liquidStatements);

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>Calcul Final du Survey</Typography>
      
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>Résumé des Poids</Typography>
              <Typography>Ballasts: {totalBallastWeight.toFixed(2)} t</Typography>
              <Typography>Eau douce: {totalFreshWaterWeight.toFixed(2)} t</Typography>
              <Typography>Liquides: {totalLiquidsWeight.toFixed(2)} t</Typography>
              <Typography variant="h6" sx={{ mt: 2 }}>
                Poids total: {(totalBallastWeight + totalFreshWaterWeight + totalLiquidsWeight).toFixed(2)} t
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>Données Techniques</Typography>
              <Typography>Tirant d'eau moyen: {calculateMeanDraft()} m</Typography>
              {density && (
                <Typography>Densité eau de mer: {density.densityValue} t/m³</Typography>
              )}
              <Typography>Corrections TE: {corrections.length}</Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default SurveyCalculationPage;