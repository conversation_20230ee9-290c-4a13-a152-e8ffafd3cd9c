﻿namespace DraftSurvey.Application.DTOs
{
    public class RoleDto
    {
        public Guid Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public bool CanManageUsers { get; set; }
        public bool CanManageAllPorts { get; set; }
    }

    public class RoleCreateDto
    {
        public string Name { get; set; }
        public string Description { get; set; }
        public bool CanManageUsers { get; set; }
        public bool CanManageAllPorts { get; set; }
    }

    public class RoleUpdateDto
    {
        public string Name { get; set; }
        public string Description { get; set; }
        public bool CanManageUsers { get; set; }
        public bool CanManageAllPorts { get; set; }
    }
}