import React, { useState, useEffect } from 'react';
import EntityPageLayout from '../../components/Shared/EntityPageLayout';
import { Droplets, Gauge, ThermometerSun, CheckCircle, AlertTriangle } from 'lucide-react';

const FreshWaterTanksPage = () => {
  const [freshWaterTanks, setFreshWaterTanks] = useState([]);
  const [loading, setLoading] = useState(true);

  // Mock data - replace with actual API call
  useEffect(() => {
    const mockFreshWaterTanks = [
      {
        id: 1,
        tankName: "Fresh Water Tank #1",
        vesselName: "MSC OSCAR",
        capacity: 500.0,
        currentLevel: 425.5,
        temperature: 18.5,
        quality: "Potable",
        status: "Normal",
        lastRefill: "2024-01-10T14:30:00Z",
        location: "Forward Port",
        consumption: 45.2,
        dailyUsage: 12.5,
        notes: "Primary drinking water supply"
      },
      {
        id: 2,
        tankName: "Fresh Water Tank #2",
        vesselName: "MSC OSCAR",
        capacity: 500.0,
        currentLevel: 125.0,
        temperature: 19.2,
        quality: "Potable",
        status: "Low",
        lastRefill: "2024-01-08T09:15:00Z",
        location: "Forward Starboard",
        consumption: 38.7,
        dailyUsage: 15.2,
        notes: "Secondary water supply - needs refill"
      },
      {
        id: 3,
        tankName: "Fresh Water Tank #3",
        vesselName: "EVER GIVEN",
        capacity: 750.0,
        currentLevel: 680.3,
        temperature: 17.8,
        quality: "Potable",
        status: "Full",
        lastRefill: "2024-01-12T11:45:00Z",
        location: "Midship Center",
        consumption: 52.1,
        dailyUsage: 18.7,
        notes: "Main water storage tank"
      },
      {
        id: 4,
        tankName: "Technical Water Tank",
        vesselName: "MAERSK MADRID",
        capacity: 300.0,
        currentLevel: 180.5,
        temperature: 22.1,
        quality: "Technical",
        status: "Normal",
        lastRefill: "2024-01-11T16:20:00Z",
        location: "Engine Room",
        consumption: 25.8,
        dailyUsage: 8.3,
        notes: "Non-potable water for technical use"
      },
      {
        id: 5,
        tankName: "Emergency Water Tank",
        vesselName: "EVER GIVEN",
        capacity: 200.0,
        currentLevel: 15.2,
        temperature: 16.5,
        quality: "Potable",
        status: "Critical",
        lastRefill: "2023-12-28T10:00:00Z",
        location: "Aft Deck",
        consumption: 2.1,
        dailyUsage: 0.5,
        notes: "Emergency reserve - immediate refill required"
      }
    ];

    setTimeout(() => {
      setFreshWaterTanks(mockFreshWaterTanks);
      setLoading(false);
    }, 1000);
  }, []);

  const columns = [
    {
      header: 'Tank Information',
      key: 'tankName',
      render: (tank) => (
        <div className="flex items-center">
          <div className="flex-shrink-0 h-10 w-10">
            <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
              <Droplets className="h-5 w-5 text-blue-600" />
            </div>
          </div>
          <div className="ml-4">
            <div className="text-sm font-medium text-gray-900">{tank.tankName}</div>
            <div className="text-sm text-gray-500">{tank.vesselName}</div>
          </div>
        </div>
      )
    },
    {
      header: 'Water Level',
      key: 'level',
      render: (tank) => {
        const fillPercentage = (tank.currentLevel / tank.capacity) * 100;
        const levelColor = fillPercentage > 75 ? 'bg-green-600' : 
                          fillPercentage > 25 ? 'bg-yellow-600' : 'bg-red-600';
        
        return (
          <div>
            <div className="text-sm text-gray-900">
              {tank.currentLevel.toFixed(1)} / {tank.capacity.toFixed(1)} L
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2 mt-1">
              <div 
                className={`${levelColor} h-2 rounded-full transition-all duration-300`}
                style={{ width: `${fillPercentage}%` }}
              ></div>
            </div>
            <div className="text-xs text-gray-500 mt-1">{fillPercentage.toFixed(1)}% filled</div>
          </div>
        );
      }
    },
    {
      header: 'Quality & Temperature',
      key: 'quality',
      render: (tank) => (
        <div className="text-sm">
          <div className="text-gray-900 flex items-center">
            {tank.quality === 'Potable' ? 
              <CheckCircle className="h-4 w-4 text-green-500 mr-1" /> :
              <AlertTriangle className="h-4 w-4 text-yellow-500 mr-1" />
            }
            {tank.quality}
          </div>
          <div className="text-gray-500 flex items-center">
            <ThermometerSun className="h-3 w-3 mr-1" />
            {tank.temperature.toFixed(1)}°C
          </div>
        </div>
      )
    },
    {
      header: 'Status',
      key: 'status',
      render: (tank) => {
        const statusColors = {
          'Full': 'bg-green-100 text-green-800',
          'Normal': 'bg-blue-100 text-blue-800',
          'Low': 'bg-yellow-100 text-yellow-800',
          'Critical': 'bg-red-100 text-red-800',
          'Empty': 'bg-gray-100 text-gray-800'
        };
        return (
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusColors[tank.status] || 'bg-gray-100 text-gray-800'}`}>
            {tank.status}
          </span>
        );
      }
    },
    {
      header: 'Consumption',
      key: 'consumption',
      render: (tank) => (
        <div className="text-sm">
          <div className="text-gray-900">Total: {tank.consumption.toFixed(1)} L</div>
          <div className="text-gray-500">Daily: {tank.dailyUsage.toFixed(1)} L/day</div>
        </div>
      )
    },
    {
      header: 'Location',
      key: 'location',
      render: (tank) => (
        <div className="text-sm text-gray-900">{tank.location}</div>
      )
    },
    {
      header: 'Last Refill',
      key: 'lastRefill',
      render: (tank) => {
        const refillDate = new Date(tank.lastRefill);
        const daysSinceRefill = Math.floor((new Date() - refillDate) / (1000 * 60 * 60 * 24));
        
        return (
          <div className="text-sm">
            <div className="text-gray-900">{refillDate.toLocaleDateString()}</div>
            <div className="text-gray-500">{daysSinceRefill} days ago</div>
          </div>
        );
      }
    }
  ];

  const handleAdd = () => {
    console.log('Add new fresh water tank');
  };

  const handleEdit = (tank) => {
    console.log('Edit fresh water tank:', tank);
  };

  const handleDelete = (tank) => {
    console.log('Delete fresh water tank:', tank);
  };

  const handleView = (tank) => {
    console.log('View fresh water tank:', tank);
  };

  const totalCapacity = freshWaterTanks.reduce((sum, tank) => sum + tank.capacity, 0);
  const totalCurrentLevel = freshWaterTanks.reduce((sum, tank) => sum + tank.currentLevel, 0);
  const averageFillPercentage = (totalCurrentLevel / totalCapacity) * 100;
  const criticalTanks = freshWaterTanks.filter(t => t.status === 'Critical').length;
  const lowTanks = freshWaterTanks.filter(t => t.status === 'Low').length;
  const totalDailyConsumption = freshWaterTanks.reduce((sum, tank) => sum + tank.dailyUsage, 0);

  return (
    <EntityPageLayout
      title="Fresh Water Tanks Management"
      description="Monitor fresh water supplies, consumption patterns, and ensure adequate water reserves for vessel operations."
      data={freshWaterTanks}
      columns={columns}
      onAdd={handleAdd}
      onEdit={handleEdit}
      onDelete={handleDelete}
      onView={handleView}
      loading={loading}
      searchPlaceholder="Search fresh water tanks by name, vessel, location..."
    >
      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Droplets className="h-8 w-8 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Total Tanks</p>
              <p className="text-2xl font-semibold text-gray-900">{freshWaterTanks.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Gauge className="h-8 w-8 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Average Fill</p>
              <p className="text-2xl font-semibold text-gray-900">{averageFillPercentage.toFixed(1)}%</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <AlertTriangle className="h-8 w-8 text-yellow-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Low/Critical</p>
              <p className="text-2xl font-semibold text-gray-900">{lowTanks + criticalTanks}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <ThermometerSun className="h-8 w-8 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Daily Usage</p>
              <p className="text-2xl font-semibold text-gray-900">{totalDailyConsumption.toFixed(1)} L</p>
            </div>
          </div>
        </div>
      </div>
    </EntityPageLayout>
  );
};

export default FreshWaterTanksPage;
