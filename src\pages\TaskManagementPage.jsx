import React, { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';
import { 
  getAllTaskAssignments, 
  createTaskAssignment,
  approveTask,
  rejectTask,
  getTasksBySurveyId
} from '../api/taskAssignmentsApi';
import { getSurveys } from '../api/draftApi';
import { getUsers } from '../api/usersApi';
import { 
  Table, 
  Button, 
  Modal, 
  Form, 
  Select, 
  message,
  Tag,
  Divider,
  Popconfirm,
  Tabs,
  Card,
  Row,
  Col,
  Descriptions,
  Spin,
  Input
} from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons';

const { TabPane } = Tabs;
const { Option } = Select;

const TASK_TYPES = [
  { value: 'Ballast', label: 'Ballast Management' },
  { value: 'DraughtMarkReading', label: 'Draught Marks Reading' },
  { value: 'DraughtMarksCorrection', label: 'Draught Marks Correction' },
  { value: 'DraughtSurveyGlobalCalculation', label: 'Global Calculation (Survey)' },
  { value: 'FreshWaterTank', label: 'Fresh Water Tank' },
  { value: 'GlobalCalculation', label: 'Global Calculation' },
  { value: 'Inspection', label: 'Hold Inspection' },
  { value: 'LiquidStatement', label: 'Liquid Statement' },
  { value: 'SeaWaterDensityStatement', label: 'Sea Water Density' }
];

const TaskManagementPage = () => {
  const { user } = useAuth();
  const [surveys, setSurveys] = useState([]);
  const [allTasks, setAllTasks] = useState([]);
  const [surveyTasks, setSurveyTasks] = useState([]);
  const [users, setUsers] = useState([]);
  const [selectedSurvey, setSelectedSurvey] = useState(null);
  const [selectedTask, setSelectedTask] = useState(null);
  const [isAssignModalVisible, setIsAssignModalVisible] = useState(false);
  const [isTaskDetailModalVisible, setIsTaskDetailModalVisible] = useState(false);
  const [activeTab, setActiveTab] = useState('all');
  const [form] = Form.useForm();
  const [loading, setLoading] = useState({
    main: false,
    surveys: false,
    tasks: false,
    users: false,
    actions: false
  });

  useEffect(() => {
    fetchInitialData();
  }, []);

  const fetchInitialData = async () => {
    try {
      setLoading(prev => ({ ...prev, main: true }));
      
      const [surveysData, tasksData, usersData] = await Promise.all([
        getSurveys(),
        getAllTaskAssignments('user,survey'),
        getUsers()
      ]);

      setSurveys(surveysData);
      setAllTasks(tasksData);
      setUsers(usersData);
    } catch (error) {
      console.error('Initial data loading error:', error);
      message.error('Failed to load initial data');
    } finally {
      setLoading(prev => ({ ...prev, main: false }));
    }
  };

  const handleSurveySelect = async (surveyId) => {
    try {
      setLoading(prev => ({ ...prev, tasks: true }));
      const survey = surveys.find(s => s.id === surveyId);
      setSelectedSurvey(survey);
      
      const tasks = await getTasksBySurveyId(surveyId, 'user');
      setSurveyTasks(tasks);
    } catch (error) {
      console.error('Error loading survey tasks:', error);
      message.error('Failed to load tasks for selected survey');
    } finally {
      setLoading(prev => ({ ...prev, tasks: false }));
    }
  };

  const showTaskDetails = (task) => {
    setSelectedTask(task);
    setIsTaskDetailModalVisible(true);
  };

  const showAssignModal = () => {
    if (!selectedSurvey) {
      message.warning('Please select a survey first');
      return;
    }
    form.resetFields();
    setIsAssignModalVisible(true);
  };

  const handleAssignTask = async (values) => {
    try {
      setLoading(prev => ({ ...prev, actions: true }));
      
      const taskData = {
        draftSurveyId: selectedSurvey.id,
        userId: values.userId,
        taskType: values.taskType,
        notes: values.notes || '',
        status: 'Assigned',
        assignedById: user.id
      };

      const newTask = await createTaskAssignment(taskData);
      
      message.success('Task assigned successfully');
      setIsAssignModalVisible(false);
      
      // Update local state
      setAllTasks(prev => [...prev, newTask]);
      setSurveyTasks(prev => [...prev, newTask]);
    } catch (error) {
      console.error('Task assignment error:', error);
      message.error('Failed to assign task');
    } finally {
      setLoading(prev => ({ ...prev, actions: false }));
    }
  };

  const handleApprove = async (taskId) => {
    try {
      setLoading(prev => ({ ...prev, actions: true }));
      await approveTask(taskId, 'Approved by team lead');
      message.success('Task approved');
      
      // Update local state
      const updateTaskStatus = (tasks) => tasks.map(task => 
        task.id === taskId ? { ...task, status: 'Approved' } : task
      );
      
      setAllTasks(updateTaskStatus);
      setSurveyTasks(updateTaskStatus);
      setIsTaskDetailModalVisible(false);
    } catch (error) {
      console.error('Task approval error:', error);
      message.error('Failed to approve task');
    } finally {
      setLoading(prev => ({ ...prev, actions: false }));
    }
  };

  const handleReject = async (taskId) => {
    try {
      setLoading(prev => ({ ...prev, actions: true }));
      await rejectTask(taskId, { 
        reason: 'Corrections needed', 
        requiresResubmission: true 
      });
      message.success('Task rejected');
      
      // Update local state
      const updateTaskStatus = (tasks) => tasks.map(task => 
        task.id === taskId ? { ...task, status: 'Rejected' } : task
      );
      
      setAllTasks(updateTaskStatus);
      setSurveyTasks(updateTaskStatus);
      setIsTaskDetailModalVisible(false);
    } catch (error) {
      console.error('Task rejection error:', error);
      message.error('Failed to reject task');
    } finally {
      setLoading(prev => ({ ...prev, actions: false }));
    }
  };

  const getStatusTag = (status) => {
    const statusColors = {
      Assigned: 'blue',
      InProgress: 'orange',
      Completed: 'green',
      PendingValidation: 'gold',
      Approved: 'green',
      Rejected: 'red',
      Canceled: 'gray'
    };
    return <Tag color={statusColors[status]}>{status}</Tag>;
  };

  const taskColumns = [
    {
      title: 'Survey Number',
      dataIndex: ['survey', 'surveyNumber'],
      key: 'surveyNumber',
      render: (text, record) => (
        <Button type="link" onClick={() => showTaskDetails(record)}>
          {text || 'N/A'}
        </Button>
      )
    },
    {
      title: 'Task Type',
      dataIndex: 'taskType',
      key: 'taskType',
      render: (text) => {
        const taskType = TASK_TYPES.find(t => t.value === text);
        return taskType ? taskType.label : text;
      }
    },
    {
      title: 'Assigned To',
      dataIndex: ['user', 'fullName'],
      key: 'userName',
      render: (text) => text || 'N/A'
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (text) => getStatusTag(text)
    },
    {
      title: 'Assignment Date',
      dataIndex: 'assignmentDate',
      key: 'assignmentDate',
      render: (date) => date ? new Date(date).toLocaleString() : 'N/A'
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <>
          {record.status === 'PendingValidation' && (
            <>
              <Button 
                type="link" 
                onClick={() => handleApprove(record.id)}
                style={{ color: 'green' }}
                loading={loading.actions}
              >
                Approve
              </Button>
              <Popconfirm
                title="Are you sure to reject this task?"
                icon={<QuestionCircleOutlined style={{ color: 'red' }} />}
                onConfirm={() => handleReject(record.id)}
                okText="Yes"
                cancelText="No"
              >
                <Button type="link" danger loading={loading.actions}>Reject</Button>
              </Popconfirm>
            </>
          )}
        </>
      )
    }
  ];

  return (
    <div style={{ padding: '24px' }}>
      <h1>Task Management</h1>
      <Divider />

      <Row gutter={16}>
        <Col span={8}>
          <Card 
            title="Available Surveys" 
            loading={loading.surveys}
            extra={
              <Button 
                type="primary" 
                onClick={showAssignModal}
                disabled={!selectedSurvey}
                loading={loading.actions}
              >
                Assign Task
              </Button>
            }
          >
            <Select
              style={{ width: '100%' }}
              placeholder="Select a survey"
              onChange={handleSurveySelect}
              loading={loading.surveys}
              value={selectedSurvey?.id}
              showSearch
              optionFilterProp="children"
              filterOption={(input, option) =>
                option.children.toLowerCase().includes(input.toLowerCase())
              }
            >
              {surveys.map(survey => (
                <Option key={survey.id} value={survey.id}>
                  {survey.surveyNumber} - {survey.vessel?.name || 'No vessel'} 
                  ({survey.port?.name || 'No port'})
                </Option>
              ))}
            </Select>
          </Card>

          {selectedSurvey && (
            <Card title="Survey Details" style={{ marginTop: 16 }}>
              <Descriptions column={1} size="small">
                <Descriptions.Item label="Vessel">
                  {selectedSurvey.vessel?.name || 'N/A'}
                </Descriptions.Item>
                <Descriptions.Item label="Port">
                  {selectedSurvey.port?.name || 'N/A'}
                </Descriptions.Item>
                <Descriptions.Item label="Survey Date">
                  {selectedSurvey.surveyDate ? 
                    new Date(selectedSurvey.surveyDate).toLocaleDateString() : 'N/A'}
                </Descriptions.Item>
              </Descriptions>
            </Card>
          )}
        </Col>
        
        <Col span={16}>
          <Tabs 
            activeKey={activeTab} 
            onChange={(key) => setActiveTab(key)}
          >
            <TabPane tab="All Tasks" key="all">
              <Table
                columns={taskColumns}
                dataSource={allTasks}
                rowKey="id"
                loading={loading.tasks}
                scroll={{ x: true }}
                pagination={{ pageSize: 10 }}
              />
            </TabPane>
            <TabPane tab="Selected Survey Tasks" key="survey" disabled={!selectedSurvey}>
              {selectedSurvey ? (
                <Table
                  columns={taskColumns}
                  dataSource={surveyTasks}
                  rowKey="id"
                  loading={loading.tasks}
                  scroll={{ x: true }}
                  pagination={{ pageSize: 10 }}
                />
              ) : (
                <Card>Please select a survey to view its tasks</Card>
              )}
            </TabPane>
          </Tabs>
        </Col>
      </Row>

      {/* Assign Task Modal */}
      <Modal
        title={`Assign New Task - Survey ${selectedSurvey?.surveyNumber || ''}`}
        visible={isAssignModalVisible}
        onCancel={() => setIsAssignModalVisible(false)}
        footer={null}
        destroyOnClose
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleAssignTask}
        >
          <Form.Item
            name="taskType"
            label="Task Type"
            rules={[{ required: true, message: 'Please select task type' }]}
          >
            <Select 
              placeholder="Select task type"
              showSearch
              optionFilterProp="children"
              filterOption={(input, option) =>
                option.children.toLowerCase().includes(input.toLowerCase())
              }
            >
              {TASK_TYPES.map(taskType => (
                <Option key={taskType.value} value={taskType.value}>
                  {taskType.label}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="userId"
            label="Assign To User"
            rules={[{ required: true, message: 'Please select user' }]}
          >
            <Select 
              placeholder="Select user"
              showSearch
              optionFilterProp="children"
              filterOption={(input, option) =>
                option.children.toLowerCase().includes(input.toLowerCase())
              }
              loading={loading.users}
            >
              {users.map(user => (
                <Option key={user.id} value={user.id}>
                  {user.fullName} ({user.email})
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="notes"
            label="Notes"
          >
            <Input.TextArea rows={4} placeholder="Enter any additional notes..." />
          </Form.Item>

          <Form.Item>
            <Button 
              type="primary" 
              htmlType="submit" 
              loading={loading.actions} 
              block
            >
              Assign Task
            </Button>
          </Form.Item>
        </Form>
      </Modal>

      {/* Task Details Modal */}
      <Modal
        title="Task Details"
        visible={isTaskDetailModalVisible}
        onCancel={() => setIsTaskDetailModalVisible(false)}
        footer={null}
        width={800}
      >
        {selectedTask ? (
          <>
            <Descriptions bordered column={2}>
              <Descriptions.Item label="Survey Number" span={2}>
                {selectedTask.survey?.surveyNumber || 'N/A'}
              </Descriptions.Item>
              <Descriptions.Item label="Task Type">
                {TASK_TYPES.find(t => t.value === selectedTask.taskType)?.label || selectedTask.taskType}
              </Descriptions.Item>
              <Descriptions.Item label="Status">
                {getStatusTag(selectedTask.status)}
              </Descriptions.Item>
              <Descriptions.Item label="Assigned To">
                {selectedTask.user?.fullName || 'N/A'} ({selectedTask.user?.email || 'N/A'})
              </Descriptions.Item>
              <Descriptions.Item label="Assignment Date">
                {selectedTask.assignmentDate ? new Date(selectedTask.assignmentDate).toLocaleString() : 'N/A'}
              </Descriptions.Item>
              <Descriptions.Item label="Completion Date">
                {selectedTask.completionDate 
                  ? new Date(selectedTask.completionDate).toLocaleString() 
                  : 'Not completed'}
              </Descriptions.Item>
              <Descriptions.Item label="Notes" span={2}>
                {selectedTask.notes || 'No notes'}
              </Descriptions.Item>
            </Descriptions>

            {selectedTask.status === 'PendingValidation' && (
              <div style={{ marginTop: 24, textAlign: 'right' }}>
                <Button 
                  type="primary" 
                  onClick={() => handleApprove(selectedTask.id)}
                  style={{ marginRight: 8 }}
                  loading={loading.actions}
                >
                  Approve Task
                </Button>
                <Popconfirm
                  title="Are you sure to reject this task?"
                  icon={<QuestionCircleOutlined style={{ color: 'red' }} />}
                  onConfirm={() => handleReject(selectedTask.id)}
                  okText="Yes"
                  cancelText="No"
                >
                  <Button type="primary" danger loading={loading.actions}>Reject Task</Button>
                </Popconfirm>
              </div>
            )}
          </>
        ) : (
          <Spin spinning={true} />
        )}
      </Modal>
    </div>
  );
};

export default TaskManagementPage;