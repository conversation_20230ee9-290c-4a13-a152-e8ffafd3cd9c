﻿using System.ComponentModel.DataAnnotations;

namespace DraftSurvey.Application.DTOs
{
    public class UpdateTaskCompletionDto
    {
        [Required]
        public Guid TaskAssignmentId { get; set; }  // Identifiant de la tâche à mettre à jour

        [Required]
        public bool IsCompleted { get; set; }  // État d'avancement

        [MaxLength(500)]
        public string CompletionNotes { get; set; }  // Notes optionnelles
    }
}