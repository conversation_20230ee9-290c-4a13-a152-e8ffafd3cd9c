﻿// In DraftSurvey.WebApi/Middleware/ExceptionHandlingMiddleware.cs
using System.Net;
using System.Text.Json;
using DraftSurvey.Application.Exceptions;

namespace DraftSurvey.WebApi.Middleware
{
    public class ExceptionHandlingMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<ExceptionHandlingMiddleware> _logger;

        public ExceptionHandlingMiddleware(
            RequestDelegate next,
            ILogger<ExceptionHandlingMiddleware> logger)
        {
            _next = next;
            _logger = logger;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            try
            {
                await _next(context);
            }
            catch (Exception ex)
            {
                await HandleExceptionAsync(context, ex);
            }
        }

        private async Task HandleExceptionAsync(HttpContext context, Exception exception)
        {
            context.Response.ContentType = "application/json";
            var response = context.Response;

            var errorResponse = new
            {
                Message = exception.Message,
                StatusCode = (int)HttpStatusCode.InternalServerError
            };

            switch (exception)
            {
                case ValidationException:
                    response.StatusCode = (int)HttpStatusCode.BadRequest;
                    errorResponse = new
                    {
                        Message = exception.Message,
                        StatusCode = (int)HttpStatusCode.BadRequest
                    };
                    break;
                case NotFoundException:
                    response.StatusCode = (int)HttpStatusCode.NotFound;
                    errorResponse = new
                    {
                        Message = exception.Message,
                        StatusCode = (int)HttpStatusCode.NotFound
                    };
                    break;
                case UnauthorizedAccessException:
                    response.StatusCode = (int)HttpStatusCode.Unauthorized;
                    errorResponse = new
                    {
                        Message = exception.Message,
                        StatusCode = (int)HttpStatusCode.Unauthorized
                    };
                    break;
                default:
                    _logger.LogError(exception, "An unhandled exception occurred");
                    break;
            }

            await context.Response.WriteAsync(JsonSerializer.Serialize(errorResponse));
        }
    }
}