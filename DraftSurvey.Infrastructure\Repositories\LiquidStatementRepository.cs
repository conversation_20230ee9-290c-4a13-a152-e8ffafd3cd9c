﻿// In DraftSurvey.Infrastructure/Repositories/LiquidStatementRepository.cs
using DraftSurvey.Domain.Entities;
using DraftSurvey.Domain.Interfaces;
using DraftSurvey.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace DraftSurvey.Infrastructure.Repositories
{
    public class LiquidStatementRepository : ILiquidStatementRepository
    {
        private readonly DraftSurveyDbContext _context;

        public LiquidStatementRepository(DraftSurveyDbContext context)
        {
            _context = context;
        }

        public async Task<LiquidStatement> GetByIdAsync(Guid id)
        {
            return await _context.LiquidStatements.FindAsync(id);
        }

        public async Task<IEnumerable<LiquidStatement>> GetBySurveyIdAsync(Guid surveyId)
        {
            return await _context.LiquidStatements
                .Where(ls => ls.DraftSurveyId == surveyId)
                .ToListAsync();
        }

        public async Task AddAsync(LiquidStatement liquidStatement)
        {
            await _context.LiquidStatements.AddAsync(liquidStatement);
            // No SaveChangesAsync() here
        }

        public async Task UpdateAsync(LiquidStatement liquidStatement)
        {
            _context.LiquidStatements.Update(liquidStatement);
            // No SaveChangesAsync() here
        }

        public async Task DeleteAsync(Guid id)
        {
            var liquidStatement = await GetByIdAsync(id);
            if (liquidStatement != null)
            {
                _context.LiquidStatements.Remove(liquidStatement);
                // No SaveChangesAsync() here
            }
        }

        public async Task SaveChangesAsync()
        {
            await _context.SaveChangesAsync();
        }
    }
}