﻿using System;
using System.ComponentModel.DataAnnotations;

namespace DraftSurvey.Application.DTOs
{
    public class TaskAssignmentUpdateDto
    {
        [MaxLength(50)]
        public string Status { get; set; } // "Assigned", "InProgress", "Completed", "Canceled"

        [MaxLength(1000)]
        public string Notes { get; set; }

        public DateTime? CompletionDate { get; set; }

        public bool? IsCompleted { get; set; } // Nouveau champ pour gérer l'état d'avancement

        // Optionnel : données spécifiques mises à jour
        public string TaskData { get; set; }
    }
}