using Microsoft.AspNetCore.Http;
using System.Security.Claims;
using DraftSurvey.Domain.Interfaces;

namespace DraftSurvey.WebApi.Services
{
    public class CurrentUserService : ICurrentUserService
    {
        private readonly IHttpContextAccessor _httpContextAccessor;

        public CurrentUserService(IHttpContextAccessor httpContextAccessor)
        {
            _httpContextAccessor = httpContextAccessor;
        }

        public Guid? UserId
        {
            get
            {
                var userId = _httpContextAccessor.HttpContext?.User?.FindFirstValue(ClaimTypes.NameIdentifier);
                return Guid.TryParse(userId, out var guid) ? guid : null;
            }
        }

        public string Username =>
            _httpContextAccessor.HttpContext?.User?.Identity?.Name;

        public string Role =>
            _httpContextAccessor.HttpContext?.User?.FindFirstValue(ClaimTypes.Role);

        public bool IsAdmin =>
            string.Equals(Role, "Admin", StringComparison.OrdinalIgnoreCase);

        public bool IsTeamLead =>
            string.Equals(Role, "TeamLead", StringComparison.OrdinalIgnoreCase);
    }
}
