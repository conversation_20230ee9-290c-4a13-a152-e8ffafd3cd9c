using MediatR;
using DraftSurvey.Application.DTOs;

namespace DraftSurvey.Application.Features.Surveys.Commands
{
    public class CreateSurveyCommand : IRequest<Guid>
    {
        public string SurveyNumber { get; set; }
        public Guid VesselId { get; set; }
        public Guid EscaleId { get; set; }
        public Guid CreatedById { get; set; }
        public List<DraughtMarkReadingDto> MarkReadings { get; set; }
    }
}