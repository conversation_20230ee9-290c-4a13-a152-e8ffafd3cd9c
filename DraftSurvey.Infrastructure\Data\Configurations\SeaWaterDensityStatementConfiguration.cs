﻿// In C:\d\DraftSurveySolution\DraftSurvey.Infrastructure\Data\Configurations\SeaWaterDensityStatementConfiguration.cs

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using DraftSurvey.Domain.Entities;

namespace DraftSurvey.Infrastructure.Data.Configurations
{
    public class SeaWaterDensityStatementConfiguration : IEntityTypeConfiguration<SeaWaterDensityStatement>
    {
        public void Configure(EntityTypeBuilder<SeaWaterDensityStatement> builder)
        {
            builder.HasKey(swds => swds.Id);

            builder.Property(swds => swds.DensityValue)
                .HasColumnType("float")
                .IsRequired();

            builder.Property(swds => swds.MeasurementDate)
                .IsRequired();

            builder.Property(swds => swds.Location)
                .HasMaxLength(100);

            // >>> IMPORTANT: This defines the ONE-TO-ONE relationship <<<
            // SeaWaterDensityStatement is the dependent entity and holds the foreign key
            builder.HasOne(swds => swds.DraftSurvey) // SeaWaterDensityStatement has one DraftSurvey
                .WithOne(ds => ds.SeaWaterDensityStatement) // DraftSurvey has one SeaWaterDensityStatement (singular!)
                .HasForeignKey<SeaWaterDensityStatement>(swds => swds.DraftSurveyId) // Foreign key is on SeaWaterDensityStatement
                .OnDelete(DeleteBehavior.Cascade);
        }
    }
}