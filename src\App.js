import { Route, Routes, Navigate } from "react-router-dom";
import LoginPage from "./pages/LoginPage";
import UserManagementPage from "./pages/UserManagementPage";
import ProfilePage from "./pages/ProfilePage";
import EscalesPage from "./pages/EscalesPage";
import DraftsPage from "./pages/DraftsPage";
import TaskManagementPage from "./pages/TaskManagementPage";
import DashboardPage from "./pages/DashboardPage";
import PortPage from "./pages/PortPage";
import VesselsPage from "./pages/VesselsPage";
import AgentDashboard from "./pages/AgentDashboard";
import AgentTask from "./pages/AgentTask";
import AgentEscales from "./pages/AgentEscales";
import AgentDraft from "./pages/AgentDraft";
import UnauthorizedPage from "./pages/UnauthorizedPage";
import PrivateRoute from "./components/PrivateRoute";

const App = () => {
  return (
    <Routes>
      <Route path="/login" element={<LoginPage />} />
      <Route path="/unauthorized" element={<UnauthorizedPage />} />
      
      {/* Routes protégées */}
      <Route path="/" element={
        <PrivateRoute>
          <DashboardPage />
        </PrivateRoute>
      } />
      
      <Route path="/dashboard" element={
        <PrivateRoute requiredRoles={["Admin", "TeamLead"]}>
          <DashboardPage />
        </PrivateRoute>
      } />
      
      <Route path="/profile" element={
        <PrivateRoute>
          <ProfilePage />
        </PrivateRoute>
      } />
      
      <Route path="/users" element={
        <PrivateRoute requiredRoles={["Admin", "TeamLead"]}>
          <UserManagementPage />
        </PrivateRoute>
      } />
      
      <Route path="/escales" element={
        <PrivateRoute requiredRoles={["Admin", "TeamLead"]}>
          <EscalesPage />
        </PrivateRoute>
      } />
      
      <Route path="/drafts" element={
        <PrivateRoute requiredRoles={["Admin", "TeamLead"]}>
          <DraftsPage />
        </PrivateRoute>
      } />
      
      <Route path="/tasks" element={
        <PrivateRoute requiredRoles={["Admin", "TeamLead"]}>
          <TaskManagementPage />
        </PrivateRoute>
      } />
      
      <Route path="/ports" element={
        <PrivateRoute requiredRoles={["Admin", "TeamLead"]}>
          <PortPage />
        </PrivateRoute>
      } />
      
      <Route path="/ships" element={
        <PrivateRoute requiredRoles={["Admin", "TeamLead"]}>
          <VesselsPage />
        </PrivateRoute>
      } />
      
      {/* Routes Agent */}
      <Route path="/agentdashboard" element={
        <PrivateRoute requiredRoles={["Agent"]}>
          <AgentDashboard />
        </PrivateRoute>
      } />
      
      <Route path="/mytasks" element={
        <PrivateRoute requiredRoles={["Agent"]}>
          <AgentTask />
        </PrivateRoute>
      } />
      
      <Route path="/myportcalls" element={
        <PrivateRoute requiredRoles={["Agent"]}>
          <AgentEscales />
        </PrivateRoute>
      } />
      
      <Route path="/mydrafts" element={
        <PrivateRoute requiredRoles={["Agent"]}>
          <AgentDraft />
        </PrivateRoute>
      } />
    </Routes>
  );
};

export default App;