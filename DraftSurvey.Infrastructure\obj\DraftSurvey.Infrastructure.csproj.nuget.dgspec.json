{"format": 1, "restore": {"C:\\Users\\<USER>\\Downloads\\Nouveau dossier (2)\\d\\DraftSurveySolution\\DraftSurvey.Infrastructure\\DraftSurvey.Infrastructure.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Downloads\\Nouveau dossier (2)\\d\\DraftSurveySolution\\DraftSurvey.Domain\\DraftSurvey.Domain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Downloads\\Nouveau dossier (2)\\d\\DraftSurveySolution\\DraftSurvey.Domain\\DraftSurvey.Domain.csproj", "projectName": "DraftSurvey.Domain", "projectPath": "C:\\Users\\<USER>\\Downloads\\Nouveau dossier (2)\\d\\DraftSurveySolution\\DraftSurvey.Domain\\DraftSurvey.Domain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Downloads\\Nouveau dossier (2)\\d\\DraftSurveySolution\\DraftSurvey.Domain\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"BCrypt.Net-Next": {"target": "Package", "version": "[4.0.3, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[8.0.4, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[8.0.15, 8.0.15]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[8.0.15, 8.0.15]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[8.0.15, 8.0.15]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Downloads\\Nouveau dossier (2)\\d\\DraftSurveySolution\\DraftSurvey.Infrastructure\\DraftSurvey.Infrastructure.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Downloads\\Nouveau dossier (2)\\d\\DraftSurveySolution\\DraftSurvey.Infrastructure\\DraftSurvey.Infrastructure.csproj", "projectName": "DraftSurvey.Infrastructure", "projectPath": "C:\\Users\\<USER>\\Downloads\\Nouveau dossier (2)\\d\\DraftSurveySolution\\DraftSurvey.Infrastructure\\DraftSurvey.Infrastructure.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Downloads\\Nouveau dossier (2)\\d\\DraftSurveySolution\\DraftSurvey.Infrastructure\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\Downloads\\Nouveau dossier (2)\\d\\DraftSurveySolution\\DraftSurvey.Domain\\DraftSurvey.Domain.csproj": {"projectPath": "C:\\Users\\<USER>\\Downloads\\Nouveau dossier (2)\\d\\DraftSurveySolution\\DraftSurvey.Domain\\DraftSurvey.Domain.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"BCrypt.Net-Next": {"target": "Package", "version": "[4.0.3, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[9.0.4, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[9.0.4, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.4, )"}, "itext7": {"target": "Package", "version": "[8.0.3, )"}, "itextsharp": {"target": "Package", "version": "[5.5.13.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[8.0.15, 8.0.15]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[8.0.15, 8.0.15]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[8.0.15, 8.0.15]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203/PortableRuntimeIdentifierGraph.json"}}}}}