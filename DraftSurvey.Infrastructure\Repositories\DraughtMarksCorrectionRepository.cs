﻿// In DraftSurvey.Infrastructure/Repositories/DraughtMarksCorrectionRepository.cs
using DraftSurvey.Domain.Entities;
using DraftSurvey.Domain.Interfaces;
using DraftSurvey.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace DraftSurvey.Infrastructure.Repositories
{
    public class DraughtMarksCorrectionRepository : IDraughtMarksCorrectionRepository
    {
        private readonly DraftSurveyDbContext _context;

        public DraughtMarksCorrectionRepository(DraftSurveyDbContext context)
        {
            _context = context;
        }

        public async Task<DraughtMarksCorrection> GetByIdAsync(Guid id)
        {
            return await _context.DraughtMarksCorrections.FindAsync(id);
        }

        public async Task<IEnumerable<DraughtMarksCorrection>> GetBySurveyIdAsync(Guid surveyId)
        {
            return await _context.DraughtMarksCorrections
                .Where(dmc => dmc.DraftSurveyId == surveyId)
                .ToListAsync();
        }

        public async Task AddAsync(DraughtMarksCorrection correction)
        {
            await _context.DraughtMarksCorrections.AddAsync(correction);
            // No SaveChangesAsync() here
        }

        public async Task UpdateAsync(DraughtMarksCorrection correction)
        {
            _context.DraughtMarksCorrections.Update(correction);
            // No SaveChangesAsync() here
        }

        public async Task DeleteAsync(Guid id)
        {
            var correction = await GetByIdAsync(id);
            if (correction != null)
            {
                _context.DraughtMarksCorrections.Remove(correction);
                // No SaveChangesAsync() here
            }
        }

        public async Task SaveChangesAsync()
        {
            await _context.SaveChangesAsync();
        }
    }
}