import React, { useState, useEffect } from "react";
import {
  Box,
  CssBaseline,
  Drawer,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Typography,
  useTheme,
  useMediaQuery,
  Grid,
  Card,
  CardContent,
  alpha,
  Divider
} from "@mui/material";
import DashboardIcon from "@mui/icons-material/Dashboard";
import DescriptionIcon from "@mui/icons-material/Description";
import SailingIcon from "@mui/icons-material/Sailing";
import TaskIcon from "@mui/icons-material/Task";
import Navbar from "../components/Navbar/Navbar";
import { useNavigate, useLocation } from "react-router-dom";
import { useAuth } from "../context/AuthContext"; // Ajout du contexte d'authentification
import { getTasksByUserId } from "../api/taskAssignmentsApi"; // Correction du nom de l'API

const drawerWidth = 240;

const AgentDashboard = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("md"));
  const navigate = useNavigate();
  const location = useLocation();
  const { user } = useAuth(); // Utilisation du contexte d'authentification

  const [dashboardDrawerOpen, setDashboardDrawerOpen] = useState(false);
  const [todayTasks, setTodayTasks] = useState([]);
  const [loading, setLoading] = useState(false); // Ajout d'un état de chargement

  const stats = {
    todaysTasks: todayTasks.length,
    validatedDrafts: todayTasks.filter(t => t.status === "Approved").length,
    pendingDrafts: todayTasks.filter(t => t.status === "PendingValidation").length
  };

  const toggleDashboardDrawer = () => setDashboardDrawerOpen(!dashboardDrawerOpen);

  useEffect(() => {
    const fetchAgentTasks = async () => {
      try {
        setLoading(true);
        if (!user?.id) return; // Utilisation de user.id au lieu de currentUser.userId
        
        const allTasks = await getTasksByUserId(user.id);
        const today = new Date().toISOString().split("T")[0];

        const filtered = allTasks.filter(
          (task) => task.assignmentDate?.startsWith(today) // Utilisation de assignmentDate au lieu de dueDate
        );

        setTodayTasks(filtered);
      } catch (error) {
        console.error("Erreur lors du chargement des tâches agent :", error);
      } finally {
        setLoading(false);
      }
    };

    fetchAgentTasks();
  }, [user?.id]); // Dépendance sur user.id

  const drawerItems = [
    { title: "Tableau de bord", icon: <DashboardIcon />, path: "/agentdashboard" },
    { title: "Tâches", icon: <TaskIcon />, path: "/mytasks" },
    { title: "Escales", icon: <SailingIcon />, path: "/myportcalls" },
    { title: "Drafts", icon: <DescriptionIcon />, path: "/mydrafts" }
  ];

  const drawer = (
    <Box sx={{ 
      height: "100%", 
      display: "flex", 
      flexDirection: "column", 
      bgcolor: theme.palette.background.paper 
    }}>
      <List sx={{ flexGrow: 1, py: 0 }}>
        {drawerItems.map((item, index) => (
          <ListItem
            button
            key={item.title} // Utilisation d'une clé plus stable
            onClick={() => {
              navigate(item.path);
              if (isMobile) setDashboardDrawerOpen(false);
            }}
            selected={location.pathname === item.path}
            sx={{
              px: 3,
              py: 1.5,
              borderLeft: "3px solid transparent",
              color: theme.palette.text.primary,
              "&:hover": { bgcolor: theme.palette.action.hover },
              "&.Mui-selected": {
                bgcolor: alpha(theme.palette.primary.main, 0.08),
                borderLeft: `3px solid ${theme.palette.primary.main}`,
                "& .MuiListItemIcon-root": { color: theme.palette.primary.main },
                "& .MuiListItemText-primary": { 
                  fontWeight: "bold", 
                  color: theme.palette.primary.main 
                }
              }
            }}
          >
            <ListItemIcon sx={{ minWidth: 40 }}>{item.icon}</ListItemIcon>
            <ListItemText 
              primary={item.title} 
              primaryTypographyProps={{ fontWeight: "medium" }} 
            />
          </ListItem>
        ))}
      </List>
    </Box>
  );

  const getTaskTypeDisplay = (taskType) => {
    const taskTypes = {
      Ballast: "Gestion des ballasts",
      DraughtMarkReading: "Lecture des tirants d'eau",
      DraughtMarksCorrection: "Correction des tirants d'eau",
      DraughtSurveyGlobalCalculation: "Calcul global",
      FreshWaterTank: "Réservoir d'eau douce",
      GlobalCalculation: "Calcul global",
      Inspection: "Inspection de cale",
      LiquidStatement: "État des liquides",
      SeaWaterDensityStatement: "Densité de l'eau de mer"
    };
    return taskTypes[taskType] || taskType;
  };

  return (
    <Box sx={{ display: "flex", minHeight: "100vh", bgcolor: theme.palette.grey[50] }}>
      <CssBaseline />
      <Navbar toggleDashboardDrawer={toggleDashboardDrawer} />
      <Box component="nav" sx={{ width: { md: drawerWidth }, flexShrink: { md: 0 } }}>
        <Drawer
          variant={isMobile ? "temporary" : "permanent"}
          open={isMobile ? dashboardDrawerOpen : true}
          onClose={toggleDashboardDrawer}
          ModalProps={{ keepMounted: true }}
          sx={{
            "& .MuiDrawer-paper": {
              width: drawerWidth,
              boxSizing: "border-box",
              mt: { xs: "56px", sm: "64px" },
              height: { xs: "calc(100% - 56px)", sm: "calc(100% - 64px)" },
              borderRight: `1px solid ${theme.palette.divider}`
            }
          }}
        >
          {drawer}
        </Drawer>
      </Box>

      <Box
        component="main"
        sx={{
          flexGrow: 1,
          p: 3,
          width: { md: `calc(100% - ${drawerWidth}px)` },
          mt: { xs: "56px", sm: "64px" }
        }}
      >
        <Typography variant="h4" fontWeight="bold" mb={4} color="text.primary">
          Tableau de Bord Agent
        </Typography>

        <Grid container spacing={3} mb={2}>
          {[
            { 
              label: "Tâches aujourd'hui", 
              value: stats.todaysTasks, 
              color: "success" 
            },
            { 
              label: "Tâches validées", 
              value: stats.validatedDrafts, 
              color: "info" 
            },
            { 
              label: "Tâches en attente", 
              value: stats.pendingDrafts, 
              color: "warning" 
            }
          ].map((stat, index) => (
            <Grid item xs={12} sm={4} key={index}>
              <Card sx={{ 
                minHeight: 110, 
                position: "relative", 
                boxShadow: theme.shadows[3], 
                overflow: "hidden" 
              }}>
                <Box sx={{ 
                  position: "absolute", 
                  inset: 0, 
                  bgcolor: alpha(theme.palette[stat.color].light, 0.15) 
                }} />
                <CardContent sx={{ 
                  position: "relative", 
                  zIndex: 1, 
                  textAlign: "center" 
                }}>
                  <Typography variant="body2" color="text.secondary" mb={0.5}>
                    {stat.label}
                  </Typography>
                  <Typography variant="h4" fontWeight="bold" color={`${stat.color}.main`}>
                    {stat.value}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>

        <Divider sx={{ my: 3 }} />

        <Box>
          <Typography variant="h6" fontWeight="bold" gutterBottom>
            Nouvelles tâches aujourd'hui
          </Typography>

          {loading ? (
            <Typography variant="body2" color="text.secondary">
              Chargement des tâches...
            </Typography>
          ) : todayTasks.length === 0 ? (
            <Typography variant="body2" color="text.secondary">
              Aucune tâche assignée pour aujourd'hui.
            </Typography>
          ) : (
            todayTasks.map((task) => (
              <Card 
                key={task.id} // Utilisation de l'ID de la tâche comme clé
                sx={{ 
                  mb: 2, 
                  p: 2, 
                  border: "1px solid", 
                  borderColor: theme.palette.divider,
                  borderRadius: 2,
                  bgcolor: theme.palette.background.paper
                }}
              >
                <Typography variant="subtitle1" fontWeight="bold">
                  {getTaskTypeDisplay(task.taskType)}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Survey: {task.surveyNumber || "N/A"} | Statut: {task.status || "N/A"}
                </Typography>
                {task.notes && (
                  <Typography variant="body2" sx={{ mt: 1 }}>
                    Notes: {task.notes}
                  </Typography>
                )}
              </Card>
            ))
          )}
        </Box>
      </Box>
    </Box>
  );
};

export default AgentDashboard;