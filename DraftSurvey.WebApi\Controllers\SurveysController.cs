using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using DraftSurvey.Application.DTOs;
using DraftSurvey.Domain.Interfaces;
using Microsoft.AspNetCore.Mvc;

namespace DraftSurvey.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class SurveysController : ControllerBase
    {
        private readonly IDraftSurveyRepository _surveyRepository;
        private readonly IDraughtMarkReadingRepository _draughtMarkReadingRepository;
        private readonly IBallastRepository _ballastRepository;
        private readonly IFreshWaterTankRepository _freshWaterTankRepository;
        private readonly ISeaWaterDensityStatementRepository _seaWaterDensityStatementRepository;
        private readonly IDraughtMarksCorrectionRepository _draughtMarksCorrectionRepository;
        private readonly ILiquidStatementRepository _liquidStatementRepository;

        public SurveysController(
            IDraftSurveyRepository surveyRepository,
            IDraughtMarkReadingRepository draughtMarkReadingRepository,
            IBallastRepository ballastRepository,
            IFreshWaterTankRepository freshWaterTankRepository,
            ISeaWaterDensityStatementRepository seaWaterDensityStatementRepository,
            IDraughtMarksCorrectionRepository draughtMarksCorrectionRepository,
            ILiquidStatementRepository liquidStatementRepository)
        {
            _surveyRepository = surveyRepository;
            _draughtMarkReadingRepository = draughtMarkReadingRepository;
            _ballastRepository = ballastRepository;
            _freshWaterTankRepository = freshWaterTankRepository;
            _seaWaterDensityStatementRepository = seaWaterDensityStatementRepository;
            _draughtMarksCorrectionRepository = draughtMarksCorrectionRepository;
            _liquidStatementRepository = liquidStatementRepository;
        }

        // GET: api/surveys
        [HttpGet]
        public async Task<ActionResult<IEnumerable<SurveyListDto>>> GetSurveys()
        {
            var surveys = await _surveyRepository.GetAllAsync();
            var surveyDtos = new List<SurveyListDto>();

            foreach (var survey in surveys)
            {
                surveyDtos.Add(new SurveyListDto
                {
                    SurveyNumber = survey.SurveyNumber,
                    CreatedDate = survey.CreatedDate,
                    Status = survey.Status.ToString(),
                    VesselName = survey.Vessel?.Name,
                    EscaleReference = survey.Escale?.Reference,
                    NetCargoWeight = survey.DraughtSurveyGlobalCalculation?.NetCargoWeight
                });
            }

            return Ok(surveyDtos);
        }

        // GET: api/surveys/{id}
        [HttpGet("{id}")]
        public async Task<ActionResult<SurveyDto>> GetSurvey(Guid id)
        {
            var survey = await _surveyRepository.GetByIdAsync(id);
            if (survey == null)
            {
                return NotFound();
            }

            var surveyDto = new SurveyDto
            {
                Id = survey.Id,
                SurveyNumber = survey.SurveyNumber,
                CreatedDate = survey.CreatedDate,
                Status = survey.Status.ToString(),
                VesselId = survey.VesselId,
                VesselName = survey.Vessel?.Name,
                EscaleId = survey.EscaleId,
                EscaleReference = survey.Escale?.Reference,
                DraughtMarkReading = survey.DraughtMarkReading != null ? new DraughtMarkReadingDto
                {
                    Id = survey.DraughtMarkReading.Id,
                    PortForward = survey.DraughtMarkReading.PortForward,
                    StarboardForward = survey.DraughtMarkReading.StarboardForward,
                    PortMidship = survey.DraughtMarkReading.PortMidship,
                    StarboardMidship = survey.DraughtMarkReading.StarboardMidship,
                    PortAft = survey.DraughtMarkReading.PortAft,
                    StarboardAft = survey.DraughtMarkReading.StarboardAft,
                    ReadingTime = survey.DraughtMarkReading.ReadingTime,
                    DraftSurveyId = survey.DraughtMarkReading.DraftSurveyId
                } : null,
                GlobalCalculation = survey.DraughtSurveyGlobalCalculation != null ? new DraughtSurveyGlobalCalculationDto
                {
                    InitialDisplacement = survey.DraughtSurveyGlobalCalculation.InitialDisplacement,
                    FirstTrimCorrection = survey.DraughtSurveyGlobalCalculation.FirstTrimCorrection,
                    SecondTrimCorrection = survey.DraughtSurveyGlobalCalculation.SecondTrimCorrection,
                    NetCargoWeight = survey.DraughtSurveyGlobalCalculation.NetCargoWeight,
                    IsComplete = survey.DraughtSurveyGlobalCalculation.IsComplete
                } : null
            };

            // Add related entities
            surveyDto.Ballasts = new List<BallastDto>();
            foreach (var ballast in survey.Ballasts)
            {
                surveyDto.Ballasts.Add(new BallastDto
                {
                    Id = ballast.Id,
                    TankName = ballast.TankName,
                    Volume = ballast.Volume,
                    Density = ballast.Density,
                    DraftSurveyId = ballast.DraftSurveyId
                });
            }

            surveyDto.FreshWaterTanks = new List<FreshWaterTankDto>();
            var freshWaterTanks = await _freshWaterTankRepository.GetBySurveyIdAsync(survey.Id);
            foreach (var tank in freshWaterTanks)
            {
                surveyDto.FreshWaterTanks.Add(new FreshWaterTankDto
                {
                    Id = tank.Id,
                    TankName = tank.TankName,
                    Volume = tank.Volume,
                    Density = tank.Density,
                    TotalWeight = tank.Volume * tank.Density,
                    DraftSurveyId = tank.DraftSurveyId
                });
            }

            var seaWaterDensity = await _seaWaterDensityStatementRepository.GetBySurveyIdAsync(survey.Id);
            if (seaWaterDensity != null)
            {
                surveyDto.SeaWaterDensityStatement = new SeaWaterDensityStatementDto
                {
                    Id = seaWaterDensity.Id,
                    DraftSurveyId = seaWaterDensity.DraftSurveyId,
                    DensityValue = seaWaterDensity.DensityValue,
                    MeasurementDate = seaWaterDensity.MeasurementDate,
                    MeasurementTime = seaWaterDensity.MeasurementTime,
                    Density = seaWaterDensity.Density,
                    Temperature = seaWaterDensity.Temperature,
                    MeasurementMethod = seaWaterDensity.MeasurementMethod,
                    Location = seaWaterDensity.Location,
                    Notes = seaWaterDensity.Notes
                };
            }

            var corrections = await _draughtMarksCorrectionRepository.GetBySurveyIdAsync(survey.Id);
            surveyDto.DraughtMarksCorrections = new List<DraughtMarksCorrectionDto>();
            foreach (var correction in corrections)
            {
                surveyDto.DraughtMarksCorrections.Add(new DraughtMarksCorrectionDto
                {
                    Id = correction.Id,
                    Value = correction.Value,
                    CorrectionType = correction.CorrectionType,
                    DraftSurveyId = correction.DraftSurveyId
                });
            }

            var liquidStatements = await _liquidStatementRepository.GetBySurveyIdAsync(survey.Id);
            surveyDto.LiquidStatements = new List<LiquidStatementDto>();
            foreach (var statement in liquidStatements)
            {
                surveyDto.LiquidStatements.Add(new LiquidStatementDto
                {
                    Id = statement.Id,
                    DraftSurveyId = statement.DraftSurveyId,
                    LiquidId = statement.LiquidId,
                    Volume = statement.Volume,
                    MeasuredDensity = statement.MeasuredDensity,
                    CalculatedWeight = statement.CalculatedWeight,
                    MeasurementTime = statement.MeasurementTime,
                    TankName = statement.TankName,
                    Notes = statement.Notes
                });
            }

            return Ok(surveyDto);
        }

        // POST: api/surveys
        [HttpPost]
        public async Task<ActionResult<SurveyDto>> CreateSurvey([FromBody] SurveyCreateDto surveyCreateDto)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }
            var surveyNumber = $"SURV-{DateTime.UtcNow:yyyyMMdd-HHmmss}";

            var survey = new Domain.Entities.DraftSurvey
            {
                VesselId = surveyCreateDto.VesselId,
                EscaleId = surveyCreateDto.EscaleId,
                CreatedDate = DateTime.UtcNow,
                Status = Domain.Entities.DraftSurveyStatus.Initial,
                 SurveyNumber = surveyNumber
            };

            await _surveyRepository.AddAsync(survey);
            await _surveyRepository.SaveChangesAsync();

            // Add draught mark reading
            if (surveyCreateDto.DraughtMarkReading != null)
            {
                var reading = new Domain.Entities.DraughtMarkReading
                {
                    PortForward = surveyCreateDto.DraughtMarkReading.PortForward,
                    StarboardForward = surveyCreateDto.DraughtMarkReading.StarboardForward,
                    PortMidship = surveyCreateDto.DraughtMarkReading.PortMidship,
                    StarboardMidship = surveyCreateDto.DraughtMarkReading.StarboardMidship,
                    PortAft = surveyCreateDto.DraughtMarkReading.PortAft,
                    StarboardAft = surveyCreateDto.DraughtMarkReading.StarboardAft,
                    ReadingTime = surveyCreateDto.DraughtMarkReading.ReadingTime,
                    DraftSurveyId = survey.Id
                };
                await _draughtMarkReadingRepository.AddAsync(reading);
            }

            // Add ballasts
            foreach (var ballastDto in surveyCreateDto.Ballasts)
            {
                var ballast = new Domain.Entities.Ballast
                {
                    TankName = ballastDto.TankName,
                    Volume = ballastDto.Volume,
                    Density = ballastDto.Density ?? 1.025, // Default density
                    DraftSurveyId = survey.Id
                };
                await _ballastRepository.AddAsync(ballast);
            }

            // Add fresh water tanks
            foreach (var tankDto in surveyCreateDto.FreshWaterTanks)
            {
                var tank = new Domain.Entities.FreshWaterTank
                {
                    TankName = tankDto.TankName,
                    Volume = tankDto.Volume,
                    Density = tankDto.Density ?? 1.0, // Default density for fresh water
                    DraftSurveyId = survey.Id
                };
                await _freshWaterTankRepository.AddAsync(tank);
            }

            // Add sea water density statement
            if (surveyCreateDto.SeaWaterDensityStatement != null)
            {
                var seaWaterDensity = new Domain.Entities.SeaWaterDensityStatement
                {
                    DensityValue = surveyCreateDto.SeaWaterDensityStatement.DensityValue,
                    MeasurementDate = surveyCreateDto.SeaWaterDensityStatement.MeasurementDate,
                    MeasurementTime = surveyCreateDto.SeaWaterDensityStatement.MeasurementTime,
                    Density = surveyCreateDto.SeaWaterDensityStatement.Density,
                    Temperature = surveyCreateDto.SeaWaterDensityStatement.Temperature,
                    MeasurementMethod = surveyCreateDto.SeaWaterDensityStatement.MeasurementMethod,
                    Location = surveyCreateDto.SeaWaterDensityStatement.Location,
                    Notes = surveyCreateDto.SeaWaterDensityStatement.Notes,
                    DraftSurveyId = survey.Id
                };
                await _seaWaterDensityStatementRepository.AddAsync(seaWaterDensity);
            }

            // Add draught marks corrections
            foreach (var correctionDto in surveyCreateDto.DraughtMarksCorrections)
            {
                var correction = new Domain.Entities.DraughtMarksCorrection
                {
                   Value = correctionDto.Value,
                    CorrectionType = correctionDto.CorrectionType,
                    DraftSurveyId = survey.Id
                };
                await _draughtMarksCorrectionRepository.AddAsync(correction);
            }

            // Add liquid statements
            foreach (var liquidDto in surveyCreateDto.LiquidStatements)
            {
                var liquidStatement = new Domain.Entities.LiquidStatement
                {
                    LiquidId = liquidDto.LiquidId,
                    Volume = liquidDto.Volume,
                    MeasuredDensity = liquidDto.MeasuredDensity,
                    MeasurementTime = liquidDto.MeasurementTime,
                    TankName = liquidDto.TankName,
                    Notes = liquidDto.Notes,
                    DraftSurveyId = survey.Id
                };
                await _liquidStatementRepository.AddAsync(liquidStatement);
            }

            await _surveyRepository.SaveChangesAsync();

            return CreatedAtAction(nameof(GetSurvey), new { id = survey.Id }, await GetSurvey(survey.Id));
        }

        // PUT: api/surveys/{id}
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateSurvey(Guid id, [FromBody] SurveyUpdateDto surveyUpdateDto)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var survey = await _surveyRepository.GetByIdAsync(id);
            if (survey == null)
            {
                return NotFound();
            }

            survey.Status = Enum.Parse<Domain.Entities.DraftSurveyStatus>(surveyUpdateDto.Status);
            await _surveyRepository.UpdateAsync(survey);
            await _surveyRepository.SaveChangesAsync();

            return NoContent();
        }


        // POST: api/surveys/{id}/finalize
        [HttpPost("{id}/finalize")]
        public async Task<IActionResult> FinalizeSurvey(Guid id)
        {
            var survey = await _surveyRepository.GetByIdAsync(id);
            if (survey == null)
            {
                return NotFound();
            }

            await _surveyRepository.FinalizeAsync(id);
            await _surveyRepository.SaveChangesAsync();

            return NoContent();
        }

        // POST: api/surveys/{id}/recalculate
        [HttpPost("{id}/recalculate")]
        public async Task<IActionResult> RecalculateSurvey(Guid id)
        {
            var survey = await _surveyRepository.GetByIdAsync(id);
            if (survey == null)
            {
                return NotFound();
            }

            await _surveyRepository.RecalculateSurveyAsync(id);
            await _surveyRepository.SaveChangesAsync();

            return NoContent();
        }

        // GET: api/surveys/{id}/cargoweight
        [HttpGet("{id}/cargoweight")]
        public async Task<ActionResult<double>> GetCargoWeight(Guid id)
        {
            var survey = await _surveyRepository.GetByIdAsync(id);
            if (survey == null)
            {
                return NotFound();
            }

            var cargoWeight = await _surveyRepository.GetCargoWeightAsync(id);
            return Ok(cargoWeight);
        }

        // GET: api/surveys/vessel/{vesselId}
        [HttpGet("vessel/{vesselId}")]
        public async Task<ActionResult<IEnumerable<SurveyListDto>>> GetSurveysByVessel(Guid vesselId)
        {
            var surveys = await _surveyRepository.GetByVesselIdAsync(vesselId);
            var surveyDtos = new List<SurveyListDto>();

            foreach (var survey in surveys)
            {
                surveyDtos.Add(new SurveyListDto
                {
                    SurveyNumber = survey.SurveyNumber,
                    CreatedDate = survey.CreatedDate,
                    Status = survey.Status.ToString(),
                    VesselName = survey.Vessel?.Name,
                    EscaleReference = survey.Escale?.Reference,
                    NetCargoWeight = survey.DraughtSurveyGlobalCalculation?.NetCargoWeight
                });
            }

            return Ok(surveyDtos);
        }
        [HttpGet("by-user/{userId}")]
        public async Task<ActionResult<IEnumerable<SurveyListDto>>> GetByUser(Guid userId)
        {
            var surveys = await _surveyRepository.GetByUserIdAsync(userId);
            var surveyDtos = new List<SurveyListDto>();

            foreach (var survey in surveys)
            {
                surveyDtos.Add(new SurveyListDto
                {
                    SurveyNumber = survey.SurveyNumber,
                    CreatedDate = survey.CreatedDate,
                    Status = survey.Status.ToString(),
                    VesselName = survey.Vessel?.Name,
                    EscaleReference = survey.Escale?.Reference,
                    NetCargoWeight = survey.DraughtSurveyGlobalCalculation?.NetCargoWeight
                });
            }

            return Ok(surveyDtos);
        }

        // GET: api/surveys/port/{portId}
        [HttpGet("port/{portId}")]
        public async Task<ActionResult<IEnumerable<SurveyListDto>>> GetSurveysByPort(Guid portId, [FromQuery] DateTime? startDate, [FromQuery] DateTime? endDate)
        {
            startDate ??= DateTime.MinValue;
            endDate ??= DateTime.MaxValue;

            var surveys = await _surveyRepository.GetByPortAndDateRangeAsync(portId, startDate.Value, endDate.Value);
            var surveyDtos = new List<SurveyListDto>();

            foreach (var survey in surveys)
            {
                surveyDtos.Add(new SurveyListDto
                {
                    SurveyNumber = survey.SurveyNumber,
                    CreatedDate = survey.CreatedDate,
                    Status = survey.Status.ToString(),
                    VesselName = survey.Vessel?.Name,
                    EscaleReference = survey.Escale?.Reference,
                    NetCargoWeight = survey.DraughtSurveyGlobalCalculation?.NetCargoWeight
                });
            }
            
            return Ok(surveyDtos);
        }
    }
}