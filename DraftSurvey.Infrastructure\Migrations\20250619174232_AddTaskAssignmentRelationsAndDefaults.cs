﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace DraftSurvey.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddTaskAssignmentRelationsAndDefaults : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Ces opérations de DropForeignKey et DropPrimaryKey sont probablement liées
            // à un renommage ou une recréation de la table TaskAssignment.
            // Si la table TaskAssignment n'existait pas du tout auparavant sous le nom "TaskAssignment" (minuscule),
            // ces opérations peuvent aussi échouer. Pour l'instant, laissons-les.
            migrationBuilder.DropForeignKey(
                name: "FK_TaskAssignment_DraftSurveys_DraftSurveyId",
                table: "TaskAssignment");

            migrationBuilder.DropForeignKey(
                name: "FK_TaskAssignment_Users_UserId",
                table: "TaskAssignment");

            migrationBuilder.DropPrimary<PERSON><PERSON>(
                name: "PK_TaskAssignment",
                table: "TaskAssignment");

            // ###############################################################
            // LIGNES À COMMENTER OU SUPPRIMER POUR RÉSOUDRE L'ERREUR "DROP COLUMN"
            // ###############################################################

            // La colonne 'Draft' n'existe pas dans la table 'Vessels', d'où l'erreur.
            // Commentez ou supprimez cette ligne.
            // migrationBuilder.DropColumn(
            //     name: "Draft",
            //     table: "Vessels");

            // Si 'Length', 'Type', 'Width' n'existent pas non plus, commentez-les aussi.
            // Basé sur l'erreur précédente, 'Draft' est le problème.
            // Vérifiez pour les autres si des erreurs similaires apparaissent.
           
            // La colonne 'TaskType' n'existe peut-être pas non plus dans 'TaskAssignment'.
            // Commentez-la si vous obtenez une erreur similaire.
            migrationBuilder.DropColumn(
                name: "TaskType",
                table: "TaskAssignment");

            // ###############################################################
            // FIN DES MODIFICATIONS POUR LES DROP COLUMN
            // ###############################################################


            migrationBuilder.RenameTable(
                name: "TaskAssignment",
                newName: "TaskAssignments");

            migrationBuilder.RenameIndex(
                name: "IX_TaskAssignment_UserId",
                table: "TaskAssignments",
                newName: "IX_TaskAssignments_UserId");

            migrationBuilder.RenameIndex(
                name: "IX_TaskAssignment_DraftSurveyId",
                table: "TaskAssignments",
                newName: "IX_TaskAssignments_DraftSurveyId");

            migrationBuilder.AlterColumn<string>(
                name: "Notes",
                table: "TaskAssignments",
                type: "nvarchar(1000)",
                maxLength: 1000,
                nullable: false, // Assurez-vous que votre entité en C# correspond (nullable ou non)
                oldClrType: typeof(string),
                oldType: "nvarchar(max)");

            // Ajout des colonnes booléennes (ce sont celles que nous voulons ajouter)
            migrationBuilder.AddColumn<bool>(
                name: "BallastCalculationAssigned",
                table: "TaskAssignments",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "BallastCalculationCompleted",
                table: "TaskAssignments",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "BallastSoundingAssigned",
                table: "TaskAssignments",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "BallastSoundingCompleted",
                table: "TaskAssignments",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "DocumentsAssigned",
                table: "TaskAssignments",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "DocumentsCompleted",
                table: "TaskAssignments",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "DraftMarksReadingsAssigned",
                table: "TaskAssignments",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "DraftMarksReadingsCompleted",
                table: "TaskAssignments",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "FreshWaterCalculationAssigned",
                table: "TaskAssignments",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "FreshWaterCalculationCompleted",
                table: "TaskAssignments",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "FreshWaterSoundingAssigned",
                table: "TaskAssignments",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "FreshWaterSoundingCompleted",
                table: "TaskAssignments",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "GeneralInfoAssigned",
                table: "TaskAssignments",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "GeneralInfoCompleted",
                table: "TaskAssignments",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "GlobalCalculationAssigned",
                table: "TaskAssignments",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "GlobalCalculationCompleted",
                table: "TaskAssignments",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "HoldInspectionAssigned",
                table: "TaskAssignments",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "HoldInspectionCompleted",
                table: "TaskAssignments",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "LiquidStatementAssigned",
                table: "TaskAssignments",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "LiquidStatementCompleted",
                table: "TaskAssignments",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "SeaWaterDensityStatementAssigned",
                table: "TaskAssignments",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "SeaWaterDensityStatementCompleted",
                table: "TaskAssignments",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<string>(
                name: "Status",
                table: "TaskAssignments",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: false,
                defaultValue: ""); // La valeur par défaut pour le statut doit être "Assigned" si elle est toujours une chaîne.

            migrationBuilder.AddPrimaryKey(
                name: "PK_TaskAssignments",
                table: "TaskAssignments",
                column: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_TaskAssignments_DraftSurveys_DraftSurveyId",
                table: "TaskAssignments",
                column: "DraftSurveyId",
                principalTable: "DraftSurveys",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_TaskAssignments_Users_UserId",
                table: "TaskAssignments",
                column: "UserId",
                principalTable: "Users",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // Les opérations dans Down doivent inverser celles de Up.
            // Si vous avez commenté des DropColumn dans Up, il faut commenter les AddColumn correspondants ici.
            migrationBuilder.DropForeignKey(
                name: "FK_TaskAssignments_DraftSurveys_DraftSurveyId",
                table: "TaskAssignments");

            migrationBuilder.DropForeignKey(
                name: "FK_TaskAssignments_Users_UserId",
                table: "TaskAssignments");

            migrationBuilder.DropPrimaryKey(
                name: "PK_TaskAssignments",
                table: "TaskAssignments");

            migrationBuilder.DropColumn(
                name: "BallastCalculationAssigned",
                table: "TaskAssignments");

            migrationBuilder.DropColumn(
                name: "BallastCalculationCompleted",
                table: "TaskAssignments");

            migrationBuilder.DropColumn(
                name: "BallastSoundingAssigned",
                table: "TaskAssignments");

            migrationBuilder.DropColumn(
                name: "BallastSoundingCompleted",
                table: "TaskAssignments");

            migrationBuilder.DropColumn(
                name: "DocumentsAssigned",
                table: "TaskAssignments");

            migrationBuilder.DropColumn(
                name: "DocumentsCompleted",
                table: "TaskAssignments");

            migrationBuilder.DropColumn(
                name: "DraftMarksReadingsAssigned",
                table: "TaskAssignments");

            migrationBuilder.DropColumn(
                name: "DraftMarksReadingsCompleted",
                table: "TaskAssignments");

            migrationBuilder.DropColumn(
                name: "FreshWaterCalculationAssigned",
                table: "TaskAssignments");

            migrationBuilder.DropColumn(
                name: "FreshWaterCalculationCompleted",
                table: "TaskAssignments");

            migrationBuilder.DropColumn(
                name: "FreshWaterSoundingAssigned",
                table: "TaskAssignments");

            migrationBuilder.DropColumn(
                name: "FreshWaterSoundingCompleted",
                table: "TaskAssignments");

            migrationBuilder.DropColumn(
                name: "GeneralInfoAssigned",
                table: "TaskAssignments");

            migrationBuilder.DropColumn(
                name: "GeneralInfoCompleted",
                table: "TaskAssignments");

            migrationBuilder.DropColumn(
                name: "GlobalCalculationAssigned",
                table: "TaskAssignments");

            migrationBuilder.DropColumn(
                name: "GlobalCalculationCompleted",
                table: "TaskAssignments");

            migrationBuilder.DropColumn(
                name: "HoldInspectionAssigned",
                table: "TaskAssignments");

            migrationBuilder.DropColumn(
                name: "HoldInspectionCompleted",
                table: "TaskAssignments");

            migrationBuilder.DropColumn(
                name: "LiquidStatementAssigned",
                table: "TaskAssignments");

            migrationBuilder.DropColumn(
                name: "LiquidStatementCompleted",
                table: "TaskAssignments");

            migrationBuilder.DropColumn(
                name: "SeaWaterDensityStatementAssigned",
                table: "TaskAssignments");

            migrationBuilder.DropColumn(
                name: "SeaWaterDensityStatementCompleted",
                table: "TaskAssignments");

            migrationBuilder.DropColumn(
                name: "Status",
                table: "TaskAssignments");

            migrationBuilder.RenameTable(
                name: "TaskAssignments",
                newName: "TaskAssignment");

            migrationBuilder.RenameIndex(
                name: "IX_TaskAssignments_UserId",
                table: "TaskAssignment",
                newName: "IX_TaskAssignment_UserId");

            migrationBuilder.RenameIndex(
                name: "IX_TaskAssignments_DraftSurveyId",
                table: "TaskAssignment",
                newName: "IX_TaskAssignment_DraftSurveyId");

            // ###############################################################
            // LIGNES À COMMENTER OU SUPPRIMER DANS LA MÉTHODE Down
            // ###############################################################
            // Si vous avez commenté ces DropColumn dans la méthode Up,
            // vous devez commenter les AddColumn correspondants ici dans Down.
            
           

           
            migrationBuilder.AlterColumn<string>(
                name: "Notes",
                table: "TaskAssignment",
                type: "nvarchar(max)",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "nvarchar(1000)",
                oldMaxLength: 1000);

            migrationBuilder.AddColumn<string>(
                name: "TaskType",
                table: "TaskAssignment",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddPrimaryKey(
                name: "PK_TaskAssignment",
                table: "TaskAssignment",
                column: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_TaskAssignment_DraftSurveys_DraftSurveyId",
                table: "TaskAssignment",
                column: "DraftSurveyId",
                principalTable: "DraftSurveys",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_TaskAssignment_Users_UserId",
                table: "TaskAssignment",
                column: "UserId",
                principalTable: "Users",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
            // ###############################################################
            // FIN DES MODIFICATIONS DANS LA MÉTHODE Down
            // ###############################################################
        }
    }
}