import React, { useState, useEffect } from 'react';
import { 
  Box, 
  TextField, 
  Button, 
  Typography, 
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Alert
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import DeleteIcon from '@mui/icons-material/Delete';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import { getTanksBySurvey, createTank, updateTank, deleteTank } from '../api/freshWaterTankApi';

const FreshWaterTankForm = ({ surveyId, onComplete }) => {
  const [tanks, setTanks] = useState([]);
  const [formData, setFormData] = useState({
    tankName: '',
    volume: '',
    density: '1.000',
    draftSurveyId: surveyId
  });
  const [completed, setCompleted] = useState(false);
  const [error, setError] = useState(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const fetchTanks = async () => {
      try {
        setLoading(true);
        const data = await getTanksBySurvey(surveyId);
        setTanks(data);
        setLoading(false);
      } catch (err) {
        setError('Erreur lors du chargement des réservoirs');
        setLoading(false);
      }
    };
    fetchTanks();
  }, [surveyId]);

  const handleChange = (e) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      setLoading(true);
      const newTank = await createTank({
        ...formData,
        volume: parseFloat(formData.volume),
        density: parseFloat(formData.density)
      });
      setTanks([...tanks, newTank]);
      setFormData({
        tankName: '',
        volume: '',
        density: '1.000',
        draftSurveyId: surveyId
      });
      setError(null);
      setLoading(false);
    } catch (err) {
      setError('Erreur lors de la création du réservoir');
      setLoading(false);
    }
  };

  const handleDelete = async (id) => {
    try {
      setLoading(true);
      await deleteTank(id);
      setTanks(tanks.filter(t => t.id !== id));
      setError(null);
      setLoading(false);
    } catch (err) {
      setError('Erreur lors de la suppression du réservoir');
      setLoading(false);
    }
  };

  const handleUpdate = async (id, updatedData) => {
    try {
      setLoading(true);
      await updateTank(id, updatedData);
      setTanks(tanks.map(t => t.id === id ? { ...t, ...updatedData } : t));
      setError(null);
      setLoading(false);
    } catch (err) {
      setError('Erreur lors de la mise à jour du réservoir');
      setLoading(false);
    }
  };

  const handleMarkComplete = () => {
    setCompleted(true);
    onComplete();
  };

  return (
    <Paper sx={{ p: 3, mb: 3 }}>
      <Typography variant="h6" gutterBottom>Réservoirs d'Eau Douce</Typography>
      
      {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>}

      <Box component="form" onSubmit={handleSubmit} sx={{ mb: 3 }}>
        <TextField
          name="tankName"
          label="Nom du réservoir"
          value={formData.tankName}
          onChange={handleChange}
          fullWidth
          margin="normal"
          required
        />
        
        <TextField
          name="volume"
          label="Volume (m³)"
          type="number"
          value={formData.volume}
          onChange={handleChange}
          fullWidth
          margin="normal"
          required
          inputProps={{ step: "0.01" }}
        />
        
        <TextField
          name="density"
          label="Densité (t/m³)"
          type="number"
          value={formData.density}
          onChange={handleChange}
          fullWidth
          margin="normal"
          required
          inputProps={{ step: "0.001" }}
        />
        
        <Button 
          type="submit" 
          variant="contained" 
          startIcon={<AddIcon />}
          sx={{ mt: 2, mr: 2 }}
          disabled={loading}
        >
          {loading ? 'Chargement...' : 'Ajouter Réservoir'}
        </Button>
      </Box>

      <TableContainer>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Nom</TableCell>
              <TableCell>Volume (m³)</TableCell>
              <TableCell>Densité (t/m³)</TableCell>
              <TableCell>Poids (t)</TableCell>
              <TableCell>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {tanks.map((tank) => (
              <TableRow key={tank.id}>
                <TableCell>{tank.tankName}</TableCell>
                <TableCell>{tank.volume.toFixed(2)}</TableCell>
                <TableCell>{tank.density.toFixed(3)}</TableCell>
                <TableCell>{(tank.volume * tank.density).toFixed(2)}</TableCell>
                <TableCell>
                  <IconButton onClick={() => handleDelete(tank.id)} disabled={loading}>
                    <DeleteIcon color="error" />
                  </IconButton>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {!completed && (
        <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end' }}>
          <Button
            variant="contained"
            color="success"
            startIcon={<CheckCircleIcon />}
            onClick={handleMarkComplete}
            disabled={loading}
          >
            Marquer comme Terminé
          </Button>
        </Box>
      )}

      {completed && (
        <Alert severity="success" sx={{ mt: 2 }}>
          Cette sous-tâche a été marquée comme terminée
        </Alert>
      )}
    </Paper>
  );
};

export default FreshWaterTankForm;