import React, { useState, useEffect } from 'react';
import EntityPageLayout from '../../components/Shared/EntityPageLayout';
import { FileText, Download, Upload, Calendar, User, Shield, AlertTriangle } from 'lucide-react';

const DocumentsPage = () => {
  const [documents, setDocuments] = useState([]);
  const [loading, setLoading] = useState(true);

  // Mock data - replace with actual API call
  useEffect(() => {
    const mockDocuments = [
      {
        id: 1,
        fileName: "Draft_Survey_Report_MSC_OSCAR_20240115.pdf",
        documentType: "Draft Survey Report",
        vesselName: "MSC OSCAR",
        uploadedBy: "<PERSON>",
        uploadDate: "2024-01-15T11:30:00Z",
        fileSize: "2.4 MB",
        status: "Approved",
        version: "1.0",
        category: "Survey Reports",
        confidentiality: "Internal",
        expiryDate: null,
        description: "Complete draft survey report for cargo loading operation"
      },
      {
        id: 2,
        fileName: "Safety_Certificate_EVER_GIVEN_2024.pdf",
        documentType: "Safety Certificate",
        vesselName: "EVER GIVEN",
        uploadedBy: "<PERSON>",
        uploadDate: "2024-01-10T09:15:00Z",
        fileSize: "1.8 MB",
        status: "Valid",
        version: "2.1",
        category: "Certificates",
        confidentiality: "Public",
        expiryDate: "2024-12-31T23:59:59Z",
        description: "Annual safety certificate renewal"
      },
      {
        id: 3,
        fileName: "Cargo_Manifest_MAERSK_MADRID_20240116.xlsx",
        documentType: "Cargo Manifest",
        vesselName: "MAERSK MADRID",
        uploadedBy: "Michael Chen",
        uploadDate: "2024-01-16T07:45:00Z",
        fileSize: "856 KB",
        status: "Pending Review",
        version: "1.0",
        category: "Cargo Documents",
        confidentiality: "Confidential",
        expiryDate: null,
        description: "Detailed cargo manifest for upcoming voyage"
      },
      {
        id: 4,
        fileName: "Inspection_Report_Hull_MSC_OSCAR_20240114.pdf",
        documentType: "Inspection Report",
        vesselName: "MSC OSCAR",
        uploadedBy: "Emma Rodriguez",
        uploadDate: "2024-01-14T16:45:00Z",
        fileSize: "3.2 MB",
        status: "Under Review",
        version: "1.0",
        category: "Inspection Reports",
        confidentiality: "Internal",
        expiryDate: null,
        description: "Hull inspection findings and recommendations"
      },
      {
        id: 5,
        fileName: "Environmental_Compliance_EVER_GIVEN_2024.pdf",
        documentType: "Environmental Report",
        vesselName: "EVER GIVEN",
        uploadedBy: "David Kim",
        uploadDate: "2024-01-13T17:20:00Z",
        fileSize: "1.5 MB",
        status: "Rejected",
        version: "1.0",
        category: "Environmental",
        confidentiality: "Internal",
        expiryDate: "2024-06-30T23:59:59Z",
        description: "Environmental compliance assessment - requires revision"
      }
    ];

    setTimeout(() => {
      setDocuments(mockDocuments);
      setLoading(false);
    }, 1000);
  }, []);

  const columns = [
    {
      header: 'Document Information',
      key: 'fileName',
      render: (document) => (
        <div className="flex items-center">
          <div className="flex-shrink-0 h-10 w-10">
            <div className="h-10 w-10 rounded-full bg-indigo-100 flex items-center justify-center">
              <FileText className="h-5 w-5 text-indigo-600" />
            </div>
          </div>
          <div className="ml-4">
            <div className="text-sm font-medium text-gray-900 max-w-xs truncate" title={document.fileName}>
              {document.fileName}
            </div>
            <div className="text-sm text-gray-500">{document.documentType}</div>
          </div>
        </div>
      )
    },
    {
      header: 'Vessel & Category',
      key: 'vessel',
      render: (document) => (
        <div>
          <div className="text-sm text-gray-900">{document.vesselName}</div>
          <div className="text-sm text-gray-500">{document.category}</div>
        </div>
      )
    },
    {
      header: 'Status',
      key: 'status',
      render: (document) => {
        const statusConfig = {
          'Approved': { color: 'bg-green-100 text-green-800', icon: Shield },
          'Valid': { color: 'bg-blue-100 text-blue-800', icon: Shield },
          'Pending Review': { color: 'bg-yellow-100 text-yellow-800', icon: Calendar },
          'Under Review': { color: 'bg-orange-100 text-orange-800', icon: Calendar },
          'Rejected': { color: 'bg-red-100 text-red-800', icon: AlertTriangle }
        };
        const config = statusConfig[document.status] || statusConfig['Pending Review'];
        const IconComponent = config.icon;
        
        return (
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
            <IconComponent className="w-3 h-3 mr-1" />
            {document.status}
          </span>
        );
      }
    },
    {
      header: 'Confidentiality',
      key: 'confidentiality',
      render: (document) => {
        const confidentialityColors = {
          'Public': 'bg-green-100 text-green-800',
          'Internal': 'bg-blue-100 text-blue-800',
          'Confidential': 'bg-red-100 text-red-800',
          'Restricted': 'bg-purple-100 text-purple-800'
        };
        return (
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${confidentialityColors[document.confidentiality] || 'bg-gray-100 text-gray-800'}`}>
            {document.confidentiality}
          </span>
        );
      }
    },
    {
      header: 'File Details',
      key: 'fileDetails',
      render: (document) => (
        <div className="text-sm">
          <div className="text-gray-900">Size: {document.fileSize}</div>
          <div className="text-gray-500">v{document.version}</div>
        </div>
      )
    },
    {
      header: 'Uploaded By',
      key: 'uploadedBy',
      render: (document) => (
        <div className="flex items-center text-sm">
          <User className="h-4 w-4 text-gray-400 mr-2" />
          <div>
            <div className="text-gray-900">{document.uploadedBy}</div>
            <div className="text-gray-500">{new Date(document.uploadDate).toLocaleDateString()}</div>
          </div>
        </div>
      )
    },
    {
      header: 'Expiry Date',
      key: 'expiryDate',
      render: (document) => {
        if (!document.expiryDate) {
          return <span className="text-gray-400">No expiry</span>;
        }
        
        const expiryDate = new Date(document.expiryDate);
        const today = new Date();
        const daysUntilExpiry = Math.ceil((expiryDate - today) / (1000 * 60 * 60 * 24));
        
        let textColor = 'text-gray-900';
        if (daysUntilExpiry < 30) textColor = 'text-red-600';
        else if (daysUntilExpiry < 90) textColor = 'text-yellow-600';
        
        return (
          <div className={`text-sm ${textColor}`}>
            {expiryDate.toLocaleDateString()}
            {daysUntilExpiry < 90 && (
              <div className="text-xs">
                ({daysUntilExpiry} days)
              </div>
            )}
          </div>
        );
      }
    }
  ];

  const handleAdd = () => {
    console.log('Add new document');
  };

  const handleEdit = (document) => {
    console.log('Edit document:', document);
  };

  const handleDelete = (document) => {
    console.log('Delete document:', document);
  };

  const handleView = (document) => {
    console.log('View document:', document);
  };

  const approvedDocs = documents.filter(d => d.status === 'Approved' || d.status === 'Valid').length;
  const pendingDocs = documents.filter(d => d.status.includes('Review')).length;
  const rejectedDocs = documents.filter(d => d.status === 'Rejected').length;
  const expiringDocs = documents.filter(d => {
    if (!d.expiryDate) return false;
    const daysUntilExpiry = Math.ceil((new Date(d.expiryDate) - new Date()) / (1000 * 60 * 60 * 24));
    return daysUntilExpiry < 30;
  }).length;

  return (
    <EntityPageLayout
      title="Documents Management"
      description="Manage vessel documents, certificates, reports, and ensure compliance with regulatory requirements."
      data={documents}
      columns={columns}
      onAdd={handleAdd}
      onEdit={handleEdit}
      onDelete={handleDelete}
      onView={handleView}
      loading={loading}
      searchPlaceholder="Search documents by name, type, vessel..."
    >
      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <FileText className="h-8 w-8 text-indigo-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Total Documents</p>
              <p className="text-2xl font-semibold text-gray-900">{documents.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Shield className="h-8 w-8 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Approved</p>
              <p className="text-2xl font-semibold text-gray-900">{approvedDocs}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Calendar className="h-8 w-8 text-yellow-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Pending Review</p>
              <p className="text-2xl font-semibold text-gray-900">{pendingDocs}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <AlertTriangle className="h-8 w-8 text-red-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Expiring Soon</p>
              <p className="text-2xl font-semibold text-gray-900">{expiringDocs}</p>
            </div>
          </div>
        </div>
      </div>
    </EntityPageLayout>
  );
};

export default DocumentsPage;
