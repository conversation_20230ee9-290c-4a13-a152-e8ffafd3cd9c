﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace DraftSurvey.Application.DTOs
{
    // DTO for GET operations (without Id)
    public class SurveyResponseDto
    {
        public string SurveyNumber { get; set; }
        public DateTime CreatedDate { get; set; }
        public string Status { get; set; }
        public string VesselName { get; set; }
        public string EscaleReference { get; set; }

        public DraughtMarkReadingDto DraughtMarkReading { get; set; }
        public List<BallastDto> Ballasts { get; set; } = new List<BallastDto>();
        public List<FreshWaterTankDto> FreshWaterTanks { get; set; } = new List<FreshWaterTankDto>();
        public SeaWaterDensityStatementDto SeaWaterDensityStatement { get; set; }
        public DraughtSurveyGlobalCalculationDto GlobalCalculation { get; set; }
    }

    // DTO with Id (for detailed view)
    public class SurveyDto : SurveyResponseDto
    {
        public Guid Id { get; set; }
        public Guid VesselId { get; set; }
        public Guid EscaleId { get; set; }
        public List<DraughtMarksCorrectionDto> DraughtMarksCorrections { get; set; } = new List<DraughtMarksCorrectionDto>();
        public List<LiquidStatementDto> LiquidStatements { get; set; } = new List<LiquidStatementDto>();
    }

    public class DraughtSurveyGlobalCalculationDto
    {
        public double InitialDisplacement { get; set; }
        public double FirstTrimCorrection { get; set; }
        public double SecondTrimCorrection { get; set; }
        public double NetCargoWeight { get; set; }
        public bool IsComplete { get; set; }
    }

    public class SurveyCreateDto
    {
        [Required]
        public Guid VesselId { get; set; }

        [Required]
        public Guid EscaleId { get; set; }

        [Required]
        public DraughtMarkReadingCreateDto DraughtMarkReading { get; set; }

        public List<BallastCreateDto> Ballasts { get; set; } = new List<BallastCreateDto>();
        public List<DraughtMarksCorrectionCreateDto> DraughtMarksCorrections { get; set; } = new List<DraughtMarksCorrectionCreateDto>();
        public List<LiquidStatementCreateDto> LiquidStatements { get; set; } = new List<LiquidStatementCreateDto>();
        public List<FreshWaterTankCreateDto> FreshWaterTanks { get; set; } = new List<FreshWaterTankCreateDto>();

        [Required]
        public SeaWaterDensityStatementCreateDto SeaWaterDensityStatement { get; set; }
    }

    public class SurveyUpdateDto
    {
        public string Status { get; set; }
    }

    public class SurveyListDto
    {
        public string SurveyNumber { get; set; }
        public DateTime CreatedDate { get; set; }
        public string Status { get; set; }
        public string VesselName { get; set; }
        public string EscaleReference { get; set; }
        public double? NetCargoWeight { get; set; }
    }
}