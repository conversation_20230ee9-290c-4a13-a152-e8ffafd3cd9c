﻿// In DraftSurvey.Infrastructure/Data/Configurations/FreshWaterTankConfiguration.cs
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using DraftSurvey.Domain.Entities;

namespace DraftSurvey.Infrastructure.Data.Configurations
{
    public class FreshWaterTankConfiguration : IEntityTypeConfiguration<FreshWaterTank>
    {
        public void Configure(EntityTypeBuilder<FreshWaterTank> builder)
        {
            builder.HasKey(fwt => fwt.Id);

            builder.Property(fwt => fwt.TankName)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(fwt => fwt.Notes)
                .HasMaxLength(500);

            // Relation
            builder.HasOne(fwt => fwt.DraftSurvey)
                .WithMany(ds => ds.FreshWaterTanks) // Assurez-vous d'avoir ICollection<FreshWaterTank> dans DraftSurvey
                                                    // CORRECTED: Use DraftSurveyId as the foreign key here
                .HasForeignKey(fwt => fwt.DraftSurveyId)
                .OnDelete(DeleteBehavior.Cascade);

            // Types de colonnes
            builder.Property(fwt => fwt.Volume).HasColumnType("float");
            builder.Property(fwt => fwt.Density).HasColumnType("float");
        }
    }
}