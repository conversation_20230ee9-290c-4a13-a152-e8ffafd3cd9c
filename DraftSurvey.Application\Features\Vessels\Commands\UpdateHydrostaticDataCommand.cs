﻿using MediatR;

namespace DraftSurvey.Application.Features.Vessels.Commands
{
    public class UpdateHydrostaticDataCommand : IRequest
    {
        public Guid VesselId { get; set; }
        public double DisplacementFactor { get; set; }
        public double TPC { get; set; }
        public double LengthBetweenPerpendiculars { get; set; }
        public double LongitudinalCenterOfFloatation { get; set; }
    }
}