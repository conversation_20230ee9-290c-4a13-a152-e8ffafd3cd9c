
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using DraftSurvey.Domain.Entities;
namespace DraftSurvey.Domain.Interfaces
{
    public interface IEscaleRepository
    {
        Task<Escale> GetByIdAsync(Guid id);
        Task<IEnumerable<Escale>> GetAllAsync();
        Task<IEnumerable<Escale>> GetActiveEscalesAsync();
        Task<IEnumerable<Escale>> GetByPortAndPeriodAsync(Guid portId, DateTime start, DateTime end);
        Task AddAsync(Escale escale);
        Task CompleteEscaleAsync(Guid escaleId);
        Task SaveChangesAsync();
        Task<IEnumerable<Escale>> GetByUserIdAsync(Guid userId);

    }
}