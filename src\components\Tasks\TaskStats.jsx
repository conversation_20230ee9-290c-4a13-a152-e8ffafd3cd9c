import React from 'react';
import { Box, Typography, Grid, Paper } from '@mui/material';
import {
  AssignmentInd as AssignedIcon,
  HourglassEmpty as PendingIcon,
  CheckCircle as CompletedIcon,
  PlayCircleFilled as InProgressIcon,
  ListAlt as TotalIcon
} from '@mui/icons-material';

const TaskStats = ({ tasks }) => {
  const stats = {
    total: tasks.length,
    assigned: tasks.filter(t => t.status === 'Assigned').length,
    inProgress: tasks.filter(t => t.status === 'InProgress').length,
    pending: tasks.filter(t => t.status === 'PendingValidation').length,
    completed: tasks.filter(t => t.status === 'Completed').length
  };

  const statCards = [
    { 
      label: 'Total', 
      value: stats.total, 
      color: 'primary.main',
      icon: <TotalIcon fontSize="large" />
    },
    { 
      label: 'Assignées', 
      value: stats.assigned, 
      color: 'info.main',
      icon: <AssignedIcon fontSize="large" />
    },
    { 
      label: 'En cours', 
      value: stats.inProgress, 
      color: 'warning.main',
      icon: <InProgressIcon fontSize="large" />
    },
    { 
      label: 'En attente', 
      value: stats.pending, 
      color: 'secondary.main',
      icon: <PendingIcon fontSize="large" />
    },
    { 
      label: 'Terminées', 
      value: stats.completed, 
      color: 'success.main',
      icon: <CompletedIcon fontSize="large" />
    }
  ];

  return (
    <Paper sx={{ p: 2, mb: 3 }}>
      <Typography variant="h6" gutterBottom sx={{ fontWeight: 'bold' }}>
        Statistiques des Tâches
      </Typography>
      <Grid container spacing={2}>
        {statCards.map((stat, index) => (
          <Grid item xs={12} sm={6} md={2.4} key={index}>
            <Box sx={{ 
              p: 2, 
              bgcolor: `${stat.color}10`, 
              borderRadius: 2,
              borderLeft: `4px solid ${stat.color}`,
              display: 'flex',
              alignItems: 'center',
              gap: 2
            }}>
              <Box sx={{ color: stat.color }}>
                {stat.icon}
              </Box>
              <Box>
                <Typography variant="body2" color="text.secondary">
                  {stat.label}
                </Typography>
                <Typography variant="h4" fontWeight="bold" color={stat.color}>
                  {stat.value}
                </Typography>
              </Box>
            </Box>
          </Grid>
        ))}
      </Grid>
    </Paper>
  );
};

export default TaskStats;