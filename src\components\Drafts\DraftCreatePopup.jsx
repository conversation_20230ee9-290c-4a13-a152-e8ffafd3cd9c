import React, { useState } from "react";
import { 
  <PERSON>alog, 
  DialogTitle, 
  DialogContent, 
  DialogActions, 
  TextField, 
  Button, 
  IconButton,
  Typography, // Added Typography for consistent text styling
  Box, // Added Box for layout and spacing
  CircularProgress, // Added for loading indicator on button
  Alert, // Added for displaying error messages
  useTheme // Added useTheme for consistent styling
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";

const DraftCreatePopup = ({ open, onClose, onCreate }) => {
  const theme = useTheme();

  const [formData, setFormData] = useState({
    vesselId: '', // Assuming these are the fields needed for survey creation
    escaleId: '',
    status: 'Initial', // Default status, adjust as needed
    // Add other fields relevant to survey creation if necessary, e.g., surveyNumber, createdDate etc.
    // For simplicity, using values matching createSurveyWithDetails in DraftsPage.jsx
  });

  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState(null); // State for error messages

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleCreate = async () => {
    setError(null); // Clear previous errors
    // Basic validation based on fields available in formData
    if (!formData.vesselId || !formData.escaleId) {
      setError("Veuillez remplir tous les champs obligatoires.");
      return;
    }

    setSubmitting(true);
    try {
      await onCreate(formData); // onCreate is now responsible for API call and snackbar
      // onClose is typically handled by the parent component (DraftsPage) after successful save
    } catch (err) {
      setError(err.message || "Une erreur est survenue lors de la création.");
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle sx={{ 
        display: "flex", 
        justifyContent: "space-between", 
        alignItems: "center",
        bgcolor: "black", // Consistent dark background for title
        color: "white",
        py: 2,
        borderBottom: `1px solid ${theme.palette.divider}`,
        boxShadow: theme.shadows[1]
      }}>
        <Typography variant="h6" sx={{ fontWeight: "bold" }}>
          Créer un Draft
        </Typography>
        <IconButton onClick={onClose} sx={{ color: "white" }}> {/* White close icon */}
          <CloseIcon />
        </IconButton>
      </DialogTitle>
      <DialogContent sx={{ padding: 3, bgcolor: theme.palette.background.paper }}>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        {/* Example fields, adjust according to your actual `createSurveyWithDetails` needs */}
        <TextField 
          fullWidth 
          label="ID du Navire" 
          name="vesselId"
          variant="outlined" 
          value={formData.vesselId} 
          onChange={handleChange} 
          sx={{ marginBottom: 2 }} 
          required
          disabled={submitting}
        />
        <TextField 
          fullWidth 
          label="ID de l'Escale" 
          name="escaleId"
          variant="outlined" 
          value={formData.escaleId} 
          onChange={handleChange} 
          sx={{ marginBottom: 2 }} 
          required
          disabled={submitting}
        />
        {/* You might want to add a dropdown for status, or other initial fields */}
        <TextField 
          fullWidth 
          label="Statut Initial" 
          name="status"
          variant="outlined" 
          value={formData.status} 
          onChange={handleChange} 
          sx={{ marginBottom: 2 }} 
          disabled // Example: if status is always 'Initial' on creation
        />
      </DialogContent>
      <DialogActions sx={{ padding: 3 }}>
        <Button 
          onClick={onClose} 
          variant="outlined"
          color="error" // Consistent red color for Cancel
          disabled={submitting}
          sx={{
            '&:hover': {
              borderColor: theme.palette.error.dark,
              backgroundColor: theme.palette.error.light + '10'
            }
          }}
        >
          Annuler
        </Button>
        <Button 
          onClick={handleCreate} 
          variant="contained" 
          color="black" // Using black for consistent main button background
          disabled={submitting}
          startIcon={submitting ? <CircularProgress size={20} color="inherit" /> : null}
          sx={{ 
            bgcolor: theme.palette.success.main, // Green for creation, consistent with DraftsPage button
            color: "white", 
            fontWeight: "bold",
            "&:hover": {
              bgcolor: theme.palette.success.dark, // Darker green on hover
            }
          }}
        >
          {submitting ? "Création..." : "Créer"}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default DraftCreatePopup;