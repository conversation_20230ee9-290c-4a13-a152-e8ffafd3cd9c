﻿// In DraftSurvey.Infrastructure/Repositories/AvisChargementRepository.cs
using DraftSurvey.Domain.Entities;
using DraftSurvey.Domain.Interfaces;
using DraftSurvey.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace DraftSurvey.Infrastructure.Repositories
{
    public class AvisChargementRepository : IAvisChargementRepository
    {
        private readonly DraftSurveyDbContext _context;

        public AvisChargementRepository(DraftSurveyDbContext context)
        {
            _context = context;
        }

        public async Task<AvisChargement> GetByIdAsync(Guid id)
        {
            return await _context.AvisChargements.FindAsync(id);
        }

        public async Task<IEnumerable<AvisChargement>> GetByEscaleIdAsync(Guid escaleId)
        {
            return await _context.AvisChargements
                .Where(ac => ac.EscaleId == escaleId)
                .ToListAsync();
        }

        public async Task AddAsync(AvisChargement avisChargement)
        {
            await _context.AvisChargements.AddAsync(avisChargement);
            // No SaveChangesAsync() here
        }

        public async Task UpdateAsync(AvisChargement avisChargement)
        {
            _context.AvisChargements.Update(avisChargement);
            // No SaveChangesAsync() here
        }

        public async Task DeleteAsync(Guid id)
        {
            var avisChargement = await GetByIdAsync(id);
            if (avisChargement != null)
            {
                _context.AvisChargements.Remove(avisChargement);
                // No SaveChangesAsync() here
            }
        }

        public async Task SaveChangesAsync()
        {
            await _context.SaveChangesAsync();
        }
    }
}