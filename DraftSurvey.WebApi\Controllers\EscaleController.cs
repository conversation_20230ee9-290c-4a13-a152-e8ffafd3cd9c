﻿using AutoMapper;
using DraftSurvey.Application.DTOs;
using DraftSurvey.Domain.Entities;
using DraftSurvey.Domain.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace DraftSurvey.WebApi.Controllers
{
    [Authorize]
    [ApiController]
    [Route("api/escales")]
    public class EscaleController : ControllerBase
    {
        private readonly IEscaleRepository _escaleRepository;
        private readonly IVesselRepository _vesselRepository;
        private readonly IPortRepository _portRepository;
        private readonly IMapper _mapper;
        private readonly ILogger<EscaleController> _logger;

        public EscaleController(
            IEscaleRepository escaleRepository,
            IVesselRepository vesselRepository,
            IPortRepository portRepository,
            IMapper mapper,
            ILogger<EscaleController> logger)
        {
            _escaleRepository = escaleRepository;
            _vesselRepository = vesselRepository;
            _portRepository = portRepository;
            _mapper = mapper;
            _logger = logger;
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<EscaleDto>>> GetAll()
        {
            var escales = await _escaleRepository.GetAllAsync();
            return Ok(_mapper.Map<IEnumerable<EscaleDto>>(escales));
        }

        [HttpGet("active")]
        public async Task<ActionResult<IEnumerable<EscaleDto>>> GetActive()
        {
            var escales = await _escaleRepository.GetActiveEscalesAsync();
            return Ok(_mapper.Map<IEnumerable<EscaleDto>>(escales));
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<EscaleDetailsDto>> GetById(Guid id)
        {
            var escale = await _escaleRepository.GetByIdAsync(id);
            if (escale == null) return NotFound();

            return Ok(_mapper.Map<EscaleDetailsDto>(escale));
        }

        [HttpPost]
        [Authorize(Roles = "TeamLead,Admin")]
        public async Task<ActionResult<EscaleDto>> Create([FromBody] EscaleCreateDto dto)
        {
            // Validation des relations
            var vessel = await _vesselRepository.GetByIdAsync(dto.VesselId);
            if (vessel == null) return BadRequest("Vessel not found");

            var port = await _portRepository.GetByIdAsync(dto.PortId);
            if (port == null) return BadRequest("Port not found");

            var escale = _mapper.Map<Escale>(dto);
            await _escaleRepository.AddAsync(escale);
            await _escaleRepository.SaveChangesAsync();

            return CreatedAtAction(nameof(GetById),
                new { id = escale.Id },
                _mapper.Map<EscaleDto>(escale));
        }

        [HttpPut("{id}/complete")]
        [Authorize(Roles = "TeamLead,Admin")]
        public async Task<IActionResult> Complete(Guid id)
        {
            try
            {
                await _escaleRepository.CompleteEscaleAsync(id);
                await _escaleRepository.SaveChangesAsync();
                return NoContent();
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpGet("by-port/{portId}")]
        public async Task<ActionResult<IEnumerable<EscaleDto>>> GetByPort(Guid portId,
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null)
        {
            var escales = await _escaleRepository.GetByPortAndPeriodAsync(
                portId,
                startDate ?? DateTime.UtcNow.AddDays(-30),
                endDate ?? DateTime.UtcNow);

            return Ok(_mapper.Map<IEnumerable<EscaleDto>>(escales));
        }
        [HttpGet("by-user/{userId}")]
        public async Task<ActionResult<IEnumerable<EscaleDto>>> GetByUser(Guid userId)
        {
            try
            {
                var escales = await _escaleRepository.GetByUserIdAsync(userId);
                return Ok(_mapper.Map<IEnumerable<EscaleDto>>(escales));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching escales for user {UserId}", userId);
                return StatusCode(500, "An error occurred while processing your request");
            }
        }
    }
}