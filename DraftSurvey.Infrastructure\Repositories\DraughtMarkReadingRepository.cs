﻿using DraftSurvey.Domain.Entities;
using DraftSurvey.Domain.Interfaces;
using DraftSurvey.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using System;
using System.Threading.Tasks;

namespace DraftSurvey.Infrastructure.Repositories
{
    public class DraughtMarkReadingRepository : IDraughtMarkReadingRepository
    {
        private readonly DraftSurveyDbContext _context;

        public DraughtMarkReadingRepository(DraftSurveyDbContext context)
        {
            _context = context;
        }

        public async Task<DraughtMarkReading> GetBySurveyIdAsync(Guid surveyId)
        {
            return await _context.DraughtMarkReadings
                .FirstOrDefaultAsync(d => d.DraftSurveyId == surveyId);
        }

        public async Task AddAsync(DraughtMarkReading reading)
        {
            if (reading.DraftSurveyId == Guid.Empty)
            {
                throw new ArgumentException("DraftSurveyId must be provided");
            }

            await _context.DraughtMarkReadings.AddAsync(reading);
        }

        public async Task UpdateAsync(DraughtMarkReading reading)
        {
            if (reading.DraftSurveyId == Guid.Empty)
            {
                throw new ArgumentException("DraftSurveyId must be provided");
            }

            _context.DraughtMarkReadings.Update(reading);
        }

        public async Task SaveChangesAsync()
        {
            await _context.SaveChangesAsync();
        }
    }
}