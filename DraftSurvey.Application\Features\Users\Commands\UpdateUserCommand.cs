// Dans DraftSurvey.Application/Features/Users/<USER>/UpdateUserCommand.cs
using MediatR;
using DraftSurvey.Domain.Interfaces;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace DraftSurvey.Application.Features.Users.Commands
{
    public class UpdateUserCommand : IRequest<Unit>
    {
        public Guid UserId { get; set; }
        public string FullName { get; set; }
        public Guid RoleId { get; set; }
        public Guid? PortId { get; set; }
        public bool IsActive { get; set; }
    }

    public class UpdateUserCommandHandler : IRequestHandler<UpdateUserCommand, Unit>
    {
        private readonly IUserRepository _repository;

        public UpdateUserCommandHandler(IUserRepository repository)
        {
            _repository = repository;
        }

        public async Task<Unit> Handle(UpdateUserCommand request, CancellationToken cancellationToken)
        {
            var user = await _repository.GetByIdAsync(request.UserId);
            if (user == null)
            {
                throw new Exception("User not found");
            }

            user.FullName = request.FullName;
            user.RoleId = request.RoleId;
            user.PortId = request.PortId;
            user.IsActive = request.IsActive;

            await _repository.UpdateUserAsync(user);
            return Unit.Value;
        }
    }
}