import React, { useEffect, useState } from "react";
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  Table,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
  TableContainer,
  Paper,
  TextField,
  MenuItem,
  CssBaseline,
  Drawer,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Button,
  CircularProgress,
  alpha,
  useTheme,
  useMediaQuery,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions
} from "@mui/material";
import DashboardIcon from "@mui/icons-material/Dashboard";
import DescriptionIcon from "@mui/icons-material/Description";
import SailingIcon from "@mui/icons-material/Sailing";
import TaskIcon from "@mui/icons-material/Task";
import VisibilityIcon from "@mui/icons-material/Visibility";
import Navbar from "../components/Navbar/Navbar";
import { useNavigate, useLocation } from "react-router-dom";
import { getEscalesByUserId } from "../api/escaleApi";

const drawerWidth = 240;

const AgentEscales = () => {
  const [portcalls, setPortcalls] = useState([]);
  const [filter, setFilter] = useState("all");
  const [loading, setLoading] = useState(true);
  const [selectedEscale, setSelectedEscale] = useState(null);
  const [openDialog, setOpenDialog] = useState(false);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("md"));
  const navigate = useNavigate();
  const location = useLocation();

  const storedUser = localStorage.getItem("userData");
  const currentUser = storedUser ? JSON.parse(storedUser) : null;

  const [drawerOpen, setDrawerOpen] = useState(false);
  const toggleDrawer = () => setDrawerOpen(!drawerOpen);

  useEffect(() => {
    const fetch = async () => {
      setLoading(true);
      try {
        if (currentUser?.userId) {
          const data = await getEscalesByUserId(currentUser.userId);
          // Ajout du statut calculé pour chaque escale
          const escalesWithStatus = data.map(escale => ({
            ...escale,
            status: escale.departureDate ? "terminée" : "en cours"
          }));
          setPortcalls(escalesWithStatus);
        }
      } catch (e) {
        console.error("Erreur chargement escales :", e);
      } finally {
        setLoading(false);
      }
    };

    fetch();
  }, [currentUser?.userId]);

  const filtered = filter === "all" ? portcalls : portcalls.filter((p) => p.status === filter);
  const countByStatus = (status) => portcalls.filter((p) => p.status === status).length;

  const handleViewDetails = (escale) => {
    setSelectedEscale(escale);
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  const drawerItems = [
    { title: "Tableau de bord", icon: <DashboardIcon />, path: "/agentdashboard" },
    { title: "Tâches", icon: <TaskIcon />, path: "/mytasks" },
    { title: "Escales", icon: <SailingIcon />, path: "/myportcalls" },
    { title: "Drafts", icon: <DescriptionIcon />, path: "/mydrafts" }
  ];

  const drawer = (
    <Box sx={{ height: "100%", display: "flex", flexDirection: "column" }}>
      <List sx={{ flexGrow: 1, py: 0 }}>
        {drawerItems.map((item, index) => (
          <ListItem
            button
            key={index}
            onClick={() => {
              navigate(item.path);
              if (isMobile) setDrawerOpen(false);
            }}
            selected={location.pathname === item.path}
            sx={{
              px: 3,
              py: 1.5,
              borderLeft: "3px solid transparent",
              color: theme.palette.text.primary,
              "&:hover": { bgcolor: theme.palette.grey[100] },
              "&.Mui-selected": {
                bgcolor: alpha(theme.palette.primary.main, 0.08),
                borderLeft: `3px solid ${theme.palette.primary.main}`,
                "& .MuiListItemIcon-root": { color: theme.palette.primary.main },
                "& .MuiListItemText-primary": {
                  fontWeight: "bold",
                  color: theme.palette.primary.main
                }
              }
            }}
          >
            <ListItemIcon sx={{ minWidth: 40 }}>{item.icon}</ListItemIcon>
            <ListItemText primary={item.title} />
          </ListItem>
        ))}
      </List>
    </Box>
  );

  return (
    <Box sx={{ display: "flex", minHeight: "100vh", bgcolor: theme.palette.grey[100] }}>
      <CssBaseline />
      <Navbar toggleDashboardDrawer={toggleDrawer} />
      <Box component="nav" sx={{ width: { md: drawerWidth }, flexShrink: { md: 0 } }}>
        <Drawer
          variant={isMobile ? "temporary" : "permanent"}
          open={isMobile ? drawerOpen : true}
          onClose={toggleDrawer}
          ModalProps={{ keepMounted: true }}
          sx={{
            "& .MuiDrawer-paper": {
              width: drawerWidth,
              boxSizing: "border-box",
              mt: { xs: "56px", sm: "64px" },
              height: { xs: "calc(100% - 56px)", sm: "calc(100% - 64px)" },
              borderRight: `1px solid ${theme.palette.divider}`
            }
          }}
        >
          {drawer}
        </Drawer>
      </Box>

      <Box
        component="main"
        sx={{
          flexGrow: 1,
          p: 3,
          width: { md: `calc(100% - ${drawerWidth}px)` },
          mt: { xs: "56px", sm: "64px" }
        }}
      >
        <Box
          sx={{
            width: "100%",
            maxWidth: 1400,
            mx: "auto",
            p: { xs: 2, sm: 3, md: 4 },
            borderRadius: 3,
            bgcolor: "white",
            boxShadow: theme.shadows[6],
            border: `1px solid ${theme.palette.divider}`
          }}
        >
          <Grid container spacing={2} alignItems="center" justifyContent="space-between" sx={{ mb: 3 }}>
            <Grid item xs={12} sm={6}>
              <Typography variant="h4" fontWeight="bold" color="black">
                Mes Escales
              </Typography>
            </Grid>
            <Grid item xs={12} sm={6} textAlign={{ xs: "left", sm: "right" }}>
              <Button variant="contained" color="success" onClick={() => navigate("/portcalls")}>
                + Nouvelle escale
              </Button>
            </Grid>
          </Grid>

          <Box mb={3} width={220}>
            <TextField
              select
              label="Filtrer par statut"
              fullWidth
              value={filter}
              onChange={(e) => setFilter(e.target.value)}
              size="small"
            >
              <MenuItem value="all">Toutes</MenuItem>
              <MenuItem value="en cours">En cours</MenuItem>
              <MenuItem value="terminée">Terminées</MenuItem>
            </TextField>
          </Box>

          <Grid container spacing={3} mb={4}>
            <Grid item xs={12} md={4}>
              <Card sx={{ bgcolor: "#FFF9C4" }}>
                <CardContent>
                  <Typography variant="body2" color="text.secondary">En cours</Typography>
                  <Typography variant="h4" fontWeight="bold" color="warning.main">
                    {countByStatus("en cours")}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={4}>
              <Card sx={{ bgcolor: "#C8E6C9" }}>
                <CardContent>
                  <Typography variant="body2" color="text.secondary">Terminées</Typography>
                  <Typography variant="h4" fontWeight="bold" color="success.main">
                    {countByStatus("terminée")}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={4}>
              <Card sx={{ bgcolor: "#E1F5FE" }}>
                <CardContent>
                  <Typography variant="body2" color="text.secondary">Total</Typography>
                  <Typography variant="h4" fontWeight="bold" color="info.main">
                    {portcalls.length}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          {loading ? (
            <Box display="flex" justifyContent="center" py={5}>
              <CircularProgress />
            </Box>
          ) : (
            <TableContainer component={Paper}>
              <Table>
                <TableHead>
                  <TableRow sx={{ backgroundColor: "#F5F5F6" }}>
                    <TableCell>Référence</TableCell>
                    <TableCell>Navire</TableCell>
                    <TableCell>Port</TableCell>
                    <TableCell>Arrivée</TableCell>
                    <TableCell>Départ</TableCell>
                    <TableCell>Statut</TableCell>
                    <TableCell>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {filtered.length > 0 ? (
                    filtered.map((escale) => (
                      <TableRow key={escale.id} hover>
                        <TableCell>{escale.reference}</TableCell>
                        <TableCell>{escale.vessel?.name || "N/A"}</TableCell>
                        <TableCell>{escale.port?.name || "N/A"}</TableCell>
                        <TableCell>
                          {new Date(escale.arrivalDate).toLocaleDateString()}
                        </TableCell>
                        <TableCell>
                          {escale.departureDate ? 
                            new Date(escale.departureDate).toLocaleDateString() : 
                            "En cours"}
                        </TableCell>
                        <TableCell>
                          <Typography
                            variant="body2"
                            sx={{
                              color: escale.status === "terminée" 
                                ? "success.main" 
                                : "warning.main",
                              fontWeight: "bold"
                            }}
                          >
                            {escale.status}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <IconButton 
                            onClick={() => handleViewDetails(escale)}
                            color="primary"
                          >
                            <VisibilityIcon />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={7} align="center" sx={{ fontStyle: "italic", color: "#999" }}>
                        Aucune escale trouvée
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          )}
        </Box>
      </Box>

      {/* Dialog pour les détails de l'escale */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>Détails de l'escale</DialogTitle>
        <DialogContent>
          {selectedEscale && (
            <>
              <DialogContentText>
                <Typography variant="h6" gutterBottom>
                  Référence: {selectedEscale.reference}
                </Typography>
              </DialogContentText>
              
              <Grid container spacing={2} sx={{ mt: 2 }}>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle1" gutterBottom>
                    <strong>Navire:</strong> {selectedEscale.vessel?.name || "N/A"}
                  </Typography>
                  <Typography variant="subtitle1" gutterBottom>
                    <strong>Port:</strong> {selectedEscale.port?.name || "N/A"}
                  </Typography>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle1" gutterBottom>
                    <strong>Date d'arrivée:</strong> {new Date(selectedEscale.arrivalDate).toLocaleString()}
                  </Typography>
                  <Typography variant="subtitle1" gutterBottom>
                    <strong>Date de départ:</strong> {selectedEscale.departureDate ? 
                      new Date(selectedEscale.departureDate).toLocaleString() : 
                      "En cours"}
                  </Typography>
                </Grid>
              </Grid>

              <Box sx={{ mt: 3 }}>
                <Typography variant="h6" gutterBottom>
                  Draft Surveys ({selectedEscale.draftSurveys?.length || 0})
                </Typography>
                {selectedEscale.draftSurveys?.length > 0 ? (
                  <TableContainer component={Paper}>
                    <Table size="small">
                      <TableHead>
                        <TableRow>
                          <TableCell>Référence</TableCell>
                          <TableCell>Type</TableCell>
                          <TableCell>Statut</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {selectedEscale.draftSurveys?.map((survey) => (
                          <TableRow key={survey.id}>
                            <TableCell>{survey.reference}</TableCell>
                            <TableCell>{survey.type}</TableCell>
                            <TableCell>
                              <Typography
                                variant="body2"
                                sx={{
                                  color: survey.status === "final" 
                                    ? "success.main" 
                                    : "warning.main",
                                  fontWeight: "bold"
                                }}
                              >
                                {survey.status}
                              </Typography>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                ) : (
                  <Typography variant="body2" color="text.secondary">
                    Aucun draft survey associé
                  </Typography>
                )}
              </Box>
            </>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Fermer</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default AgentEscales;