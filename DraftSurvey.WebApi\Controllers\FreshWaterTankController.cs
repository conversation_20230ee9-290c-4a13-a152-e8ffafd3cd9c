﻿using AutoMapper;
using DraftSurvey.Application.DTOs;
using DraftSurvey.Domain.Entities;
using DraftSurvey.Domain.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace DraftSurvey.WebApi.Controllers
{
    [Authorize]
    [ApiController]
    [Route("api/[controller]")]
    public class FreshWaterTankController : ControllerBase
    {
        private readonly IFreshWaterTankRepository _repository;
        private readonly IMapper _mapper;

        public FreshWaterTankController(IFreshWaterTankRepository repository, IMapper mapper)
        {
            _repository = repository;
            _mapper = mapper;
        }

        // GET api/FreshWaterTank/{id}
        [HttpGet("{id}")]
        public async Task<ActionResult<FreshWaterTankDto>> GetById(Guid id)
        {
            var tank = await _repository.GetByIdAsync(id);
            if (tank == null)
                return NotFound();

            return Ok(_mapper.Map<FreshWaterTankDto>(tank));
        }

        // GET api/FreshWaterTank/by-survey/{draftSurveyId}
        [HttpGet("by-survey/{draftSurveyId}")]
        public async Task<ActionResult<IEnumerable<FreshWaterTankDto>>> GetBySurvey(Guid draftSurveyId)
        {
            var tanks = await _repository.GetBySurveyIdAsync(draftSurveyId);
            return Ok(_mapper.Map<IEnumerable<FreshWaterTankDto>>(tanks));
        }

        // POST api/FreshWaterTank
        [HttpPost]
        [Authorize(Roles = "Admin,TeamLead,Inspector")]
        public async Task<ActionResult<FreshWaterTankDto>> Create([FromBody] FreshWaterTankCreateDto dto)
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            var tank = _mapper.Map<FreshWaterTank>(dto);

            // Optionally set default density if not provided
            if (!dto.Density.HasValue)
                tank.Density = 1.000;

            await _repository.AddAsync(tank);
            await _repository.SaveChangesAsync();

            var resultDto = _mapper.Map<FreshWaterTankDto>(tank);
            return CreatedAtAction(nameof(GetById), new { id = tank.Id }, resultDto);
        }

        // PUT api/FreshWaterTank/{id}
        [HttpPut("{id}")]
        [Authorize(Roles = "Admin,TeamLead,Inspector")]
        public async Task<IActionResult> Update(Guid id, [FromBody] FreshWaterTankUpdateDto dto)
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            var tank = await _repository.GetByIdAsync(id);
            if (tank == null)
                return NotFound();

            _mapper.Map(dto, tank);
            await _repository.UpdateAsync(tank);
            await _repository.SaveChangesAsync();

            return NoContent();
        }

        // DELETE api/FreshWaterTank/{id}
        [HttpDelete("{id}")]
        [Authorize(Roles = "Admin,TeamLead")]
        public async Task<IActionResult> Delete(Guid id)
        {
            var tank = await _repository.GetByIdAsync(id);
            if (tank == null)
                return NotFound();

            await _repository.DeleteAsync(id);
            await _repository.SaveChangesAsync();

            return NoContent();
        }
    }
}
