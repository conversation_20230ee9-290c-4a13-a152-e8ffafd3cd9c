import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import PropTypes from 'prop-types';
import { useEffect, useState } from 'react';

const PrivateRoute = ({ children, requiredRoles, loadingFallback = null }) => {
  const { isAuthenticated, user, isLoading, validateToken } = useAuth();
  const location = useLocation();
  const [isValidating, setIsValidating] = useState(true);

  useEffect(() => {
    const checkAuth = async () => {
      if (isAuthenticated) {
        await validateToken();
      }
      setIsValidating(false);
    };

    checkAuth();
  }, [isAuthenticated, validateToken]);

  if (isLoading || isValidating) {
    return loadingFallback || <div>Loading...</div>;
  }

  if (!isAuthenticated) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  if (requiredRoles && user?.role?.name) {
    const userRole = user.role.name.toLowerCase();
    const isAllowed = requiredRoles.some(role => role.toLowerCase() === userRole);
    if (!isAllowed) {
      return <Navigate to="/unauthorized" state={{ from: location }} replace />;
    }
  }

  return children;
};

PrivateRoute.propTypes = {
  children: PropTypes.node.isRequired,
  requiredRoles: PropTypes.arrayOf(PropTypes.string),
  loadingFallback: PropTypes.node
};

export default PrivateRoute;