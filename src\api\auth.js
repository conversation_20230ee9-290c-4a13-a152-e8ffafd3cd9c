import axios from 'axios';

const API_URL = 'http://localhost:5138/api/auth';

// Configuration Axios pour intercepter les requêtes
const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json'
  }
});

// Intercepteur pour ajouter le token aux requêtes
api.interceptors.request.use(config => {
  const authData = getAuthData();
  if (authData?.token) {
    config.headers.Authorization = `Bearer ${authData.token}`;
  }
  return config;
}, error => {
  return Promise.reject(error);
});

// Intercepteur pour gérer les tokens expirés
api.interceptors.response.use(response => response, async error => {
  const originalRequest = error.config;
  
  if (error.response?.status === 401 && !originalRequest._retry) {
    originalRequest._retry = true;
    
    try {
      const newToken = await refreshToken();
      if (newToken) {
        originalRequest.headers.Authorization = `Bearer ${newToken}`;
        return api(originalRequest);
      }
    } catch (refreshError) {
      console.error('Failed to refresh token:', refreshError);
      logout();
      window.location.href = '/login?session_expired=1';
    }
  }
  
  return Promise.reject(error);
});

// Fonction pour normaliser la structure de l'utilisateur
const normalizeUser = (userData) => {
  if (!userData) return null;
  
  // Si la structure est déjà normalisée
  if (userData.role && userData.port) return userData;
  
  // Normalisation depuis la structure plate
  return {
    ...userData,
    role: {
      id: userData.roleId,
      name: userData.roleName
    },
    port: userData.portId ? {
      id: userData.portId,
      name: userData.portName
    } : null
  };
};

// 🔐 LOGIN
export const login = async (username, password) => {
  try {
    const response = await api.post('/login', {
      username,
      password
    });

    if (!response.data?.token) {
      throw new Error('Authentication failed: No token received');
    }

    // Stocker les données d'authentification normalisées
    const authData = {
      token: response.data.token,
      user: normalizeUser({
        id: response.data.userId,
        username: response.data.username,
        email: response.data.email,
        fullName: response.data.fullName,
        roleId: response.data.roleId,
        roleName: response.data.roleName,
        portId: response.data.portId,
        portName: response.data.portName
      }),
      expiresAt: new Date(response.data.expiration).getTime()
    };

    storeAuthData(authData);
    return authData;

  } catch (error) {
    let errorMessage = 'Login failed. Please try again.';
    
    if (error.response) {
      // Gestion des erreurs spécifiques du backend
      switch (error.response.data?.Code) {
        case 'AUTH_FAILED':
          errorMessage = 'Invalid username or password';
          break;
        case 'ACCOUNT_DISABLED':
          errorMessage = 'Account is disabled';
          break;
        case 'USER_NOT_FOUND':
          errorMessage = 'User not found';
          break;
        default:
          errorMessage = error.response.data?.Message || errorMessage;
      }
    }
    
    throw new Error(errorMessage);
  }
};

// 🔄 REFRESH TOKEN (interne)
const refreshToken = async () => {
  try {
    const authData = getAuthData();
    if (!authData) return null;

    // Implémentation basique de rafraîchissement (à adapter selon votre backend)
    const response = await api.post('/refresh-token', {
      token: authData.token
    });

    if (!response.data?.token) {
      throw new Error('No new token received');
    }

    const newAuthData = {
      ...authData,
      token: response.data.token,
      expiresAt: new Date(response.data.expiration).getTime()
    };

    storeAuthData(newAuthData);
    return response.data.token;

  } catch (error) {
    console.error('Refresh token failed:', error);
    logout();
    return null;
  }
};

// ✅ VALIDATE TOKEN
export const validateToken = async () => {
  try {
    const authData = getAuthData();
    if (!authData?.token) return false;

    // 1. Vérification locale de l'expiration
    if (authData.expiresAt < Date.now()) {
      logout();
      return false;
    }

    // 2. Validation côté serveur
    await api.post('/validate');
    return true;

  } catch (error) {
    console.error('Token validation error:', error);
    logout();
    return false;
  }
};

// 🚪 LOGOUT
export const logout = () => {
  removeAuthData();
  // Nettoyage supplémentaire si nécessaire
  if (window.location.pathname !== '/login') {
    window.location.href = '/login';
  }
};

// 👤 GET CURRENT USER
export const getCurrentUser = () => {
  const authData = getAuthData();
  return authData ? normalizeUser(authData.user) : null;
};

// 🛠️ Helpers pour le stockage local
const storeAuthData = (data) => {
  try {
    localStorage.setItem('auth', JSON.stringify(data));
  } catch (error) {
    console.error('Failed to store auth data:', error);
  }
};

const getAuthData = () => {
  try {
    const data = localStorage.getItem('auth');
    return data ? JSON.parse(data) : null;
  } catch (error) {
    console.error('Failed to parse auth data:', error);
    return null;
  }
};

const removeAuthData = () => {
  localStorage.removeItem('auth');
};

// Export de l'instance axios pour une utilisation ailleurs
export { api as authApi };