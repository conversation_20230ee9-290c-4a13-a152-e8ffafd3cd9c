﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace DraftSurvey.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class Updatetaskassi : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "BallastCalculationAssigned",
                table: "TaskAssignments");

            migrationBuilder.DropColumn(
                name: "BallastCalculationCompleted",
                table: "TaskAssignments");

            migrationBuilder.DropColumn(
                name: "BallastSoundingAssigned",
                table: "TaskAssignments");

            migrationBuilder.DropColumn(
                name: "BallastSoundingCompleted",
                table: "TaskAssignments");

            migrationBuilder.DropColumn(
                name: "DocumentsAssigned",
                table: "TaskAssignments");

            migrationBuilder.DropColumn(
                name: "DocumentsCompleted",
                table: "TaskAssignments");

            migrationBuilder.DropColumn(
                name: "DraftMarksReadingsAssigned",
                table: "TaskAssignments");

            migrationBuilder.DropColumn(
                name: "DraftMarksReadingsCompleted",
                table: "TaskAssignments");

            migrationBuilder.DropColumn(
                name: "FreshWaterCalculationAssigned",
                table: "TaskAssignments");

            migrationBuilder.DropColumn(
                name: "FreshWaterCalculationCompleted",
                table: "TaskAssignments");

            migrationBuilder.DropColumn(
                name: "FreshWaterSoundingAssigned",
                table: "TaskAssignments");

            migrationBuilder.DropColumn(
                name: "FreshWaterSoundingCompleted",
                table: "TaskAssignments");

            migrationBuilder.DropColumn(
                name: "GeneralInfoAssigned",
                table: "TaskAssignments");

            migrationBuilder.DropColumn(
                name: "GeneralInfoCompleted",
                table: "TaskAssignments");

            migrationBuilder.DropColumn(
                name: "GlobalCalculationAssigned",
                table: "TaskAssignments");

            migrationBuilder.DropColumn(
                name: "GlobalCalculationCompleted",
                table: "TaskAssignments");

            migrationBuilder.DropColumn(
                name: "HoldInspectionAssigned",
                table: "TaskAssignments");

            migrationBuilder.DropColumn(
                name: "HoldInspectionCompleted",
                table: "TaskAssignments");

            migrationBuilder.DropColumn(
                name: "LiquidStatementAssigned",
                table: "TaskAssignments");

            migrationBuilder.DropColumn(
                name: "LiquidStatementCompleted",
                table: "TaskAssignments");

            migrationBuilder.DropColumn(
                name: "SeaWaterDensityStatementAssigned",
                table: "TaskAssignments");

            migrationBuilder.RenameColumn(
                name: "SeaWaterDensityStatementCompleted",
                table: "TaskAssignments",
                newName: "IsCompleted");

            migrationBuilder.AlterColumn<string>(
                name: "Status",
                table: "TaskAssignments",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: false,
                defaultValue: "Assigned",
                oldClrType: typeof(string),
                oldType: "nvarchar(50)",
                oldMaxLength: 50);

            migrationBuilder.AlterColumn<DateTime>(
                name: "AssignmentDate",
                table: "TaskAssignments",
                type: "datetime2",
                nullable: false,
                defaultValueSql: "GETDATE()",
                oldClrType: typeof(DateTime),
                oldType: "datetime2");

            migrationBuilder.AddColumn<string>(
                name: "TaskData",
                table: "TaskAssignments",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "TaskType",
                table: "TaskAssignments",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: false,
                defaultValue: "");

            migrationBuilder.CreateIndex(
                name: "IX_TaskAssignments_IsCompleted",
                table: "TaskAssignments",
                column: "IsCompleted");

            migrationBuilder.CreateIndex(
                name: "IX_TaskAssignments_TaskType",
                table: "TaskAssignments",
                column: "TaskType");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_TaskAssignments_IsCompleted",
                table: "TaskAssignments");

            migrationBuilder.DropIndex(
                name: "IX_TaskAssignments_TaskType",
                table: "TaskAssignments");

            migrationBuilder.DropColumn(
                name: "TaskData",
                table: "TaskAssignments");

            migrationBuilder.DropColumn(
                name: "TaskType",
                table: "TaskAssignments");

            migrationBuilder.RenameColumn(
                name: "IsCompleted",
                table: "TaskAssignments",
                newName: "SeaWaterDensityStatementCompleted");

            migrationBuilder.AlterColumn<string>(
                name: "Status",
                table: "TaskAssignments",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "nvarchar(50)",
                oldMaxLength: 50,
                oldDefaultValue: "Assigned");

            migrationBuilder.AlterColumn<DateTime>(
                name: "AssignmentDate",
                table: "TaskAssignments",
                type: "datetime2",
                nullable: false,
                oldClrType: typeof(DateTime),
                oldType: "datetime2",
                oldDefaultValueSql: "GETDATE()");

            migrationBuilder.AddColumn<bool>(
                name: "BallastCalculationAssigned",
                table: "TaskAssignments",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "BallastCalculationCompleted",
                table: "TaskAssignments",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "BallastSoundingAssigned",
                table: "TaskAssignments",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "BallastSoundingCompleted",
                table: "TaskAssignments",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "DocumentsAssigned",
                table: "TaskAssignments",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "DocumentsCompleted",
                table: "TaskAssignments",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "DraftMarksReadingsAssigned",
                table: "TaskAssignments",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "DraftMarksReadingsCompleted",
                table: "TaskAssignments",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "FreshWaterCalculationAssigned",
                table: "TaskAssignments",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "FreshWaterCalculationCompleted",
                table: "TaskAssignments",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "FreshWaterSoundingAssigned",
                table: "TaskAssignments",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "FreshWaterSoundingCompleted",
                table: "TaskAssignments",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "GeneralInfoAssigned",
                table: "TaskAssignments",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "GeneralInfoCompleted",
                table: "TaskAssignments",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "GlobalCalculationAssigned",
                table: "TaskAssignments",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "GlobalCalculationCompleted",
                table: "TaskAssignments",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "HoldInspectionAssigned",
                table: "TaskAssignments",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "HoldInspectionCompleted",
                table: "TaskAssignments",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "LiquidStatementAssigned",
                table: "TaskAssignments",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "LiquidStatementCompleted",
                table: "TaskAssignments",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "SeaWaterDensityStatementAssigned",
                table: "TaskAssignments",
                type: "bit",
                nullable: false,
                defaultValue: false);
        }
    }
}
