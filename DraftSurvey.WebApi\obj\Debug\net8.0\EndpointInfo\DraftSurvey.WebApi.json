{"openapi": "3.0.4", "info": {"title": "DraftSurvey API", "description": "API for DraftSurvey Application", "contact": {"name": "Development Team"}, "version": "v1"}, "paths": {"/api/Auth/login": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Auth/validate": {"post": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "OK"}}}}, "/api/Ballasts/{id}": {"get": {"tags": ["Ballasts"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BallastDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BallastDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BallastDto"}}}}}}, "put": {"tags": ["Ballasts"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BallastDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BallastDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BallastDto"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["Ballasts"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Ballasts/by-survey/{surveyId}": {"get": {"tags": ["Ballasts"], "parameters": [{"name": "surveyId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BallastDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BallastDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BallastDto"}}}}}}}}, "/api/Ballasts": {"post": {"tags": ["Ballasts"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BallastDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BallastDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BallastDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BallastDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BallastDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BallastDto"}}}}}}}, "/api/DraughtMarkReadings/by-survey/{surveyId}": {"get": {"tags": ["DraughtMarkReadings"], "parameters": [{"name": "surveyId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/DraughtMarkReadingDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DraughtMarkReadingDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DraughtMarkReadingDto"}}}}}}}, "/api/DraughtMarkReadings": {"post": {"tags": ["DraughtMarkReadings"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DraughtMarkReadingDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DraughtMarkReadingDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DraughtMarkReadingDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/DraughtMarkReadingDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DraughtMarkReadingDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DraughtMarkReadingDto"}}}}}}}, "/api/DraughtMarkReadings/{surveyId}": {"put": {"tags": ["DraughtMarkReadings"], "parameters": [{"name": "surveyId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DraughtMarkReadingDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DraughtMarkReadingDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DraughtMarkReadingDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/escales": {"get": {"tags": ["Escale"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/EscaleDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/EscaleDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/EscaleDto"}}}}}}}, "post": {"tags": ["Escale"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EscaleCreateDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/EscaleCreateDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/EscaleCreateDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/EscaleDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/EscaleDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/EscaleDto"}}}}}}}, "/api/escales/active": {"get": {"tags": ["Escale"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/EscaleDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/EscaleDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/EscaleDto"}}}}}}}}, "/api/escales/{id}": {"get": {"tags": ["Escale"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/EscaleDetailsDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/EscaleDetailsDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/EscaleDetailsDto"}}}}}}}, "/api/escales/{id}/complete": {"put": {"tags": ["Escale"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/api/escales/by-port/{portId}": {"get": {"tags": ["Escale"], "parameters": [{"name": "portId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "startDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "endDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/EscaleDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/EscaleDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/EscaleDto"}}}}}}}}, "/api/ports": {"get": {"tags": ["Ports"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PortDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PortDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PortDto"}}}}}}}, "post": {"tags": ["Ports"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PortDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PortDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PortDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PortDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PortDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PortDto"}}}}}}}, "/api/ports/{id}": {"get": {"tags": ["Ports"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PortDetailsDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PortDetailsDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PortDetailsDto"}}}}}}, "put": {"tags": ["Ports"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PortDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PortDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PortDto"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["Ports"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/api/ports/search": {"get": {"tags": ["Ports"], "parameters": [{"name": "term", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PortDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PortDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PortDto"}}}}}}}}, "/api/ports/{id}/water-density": {"put": {"tags": ["Ports"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "number", "format": "double"}}, "text/json": {"schema": {"type": "number", "format": "double"}}, "application/*+json": {"schema": {"type": "number", "format": "double"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/ports/{id}/assign-manager/{userId}": {"put": {"tags": ["Ports"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "userId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/api/ports/{id}/staff": {"get": {"tags": ["Ports"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserDto"}}}}}}}}, "/api/Role": {"get": {"tags": ["Role"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/RoleDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/RoleDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/RoleDto"}}}}}}}, "post": {"tags": ["Role"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RoleCreateDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RoleCreateDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RoleCreateDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RoleDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RoleDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RoleDto"}}}}}}}, "/api/Role/{id}": {"get": {"tags": ["Role"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RoleDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RoleDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RoleDto"}}}}}}, "put": {"tags": ["Role"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RoleUpdateDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RoleUpdateDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RoleUpdateDto"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["Role"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/api/surveys/{id}": {"get": {"tags": ["Surveys"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SurveyDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SurveyDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SurveyDto"}}}}}}}, "/api/surveys": {"post": {"tags": ["Surveys"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SurveyCreateDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SurveyCreateDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SurveyCreateDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SurveyDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SurveyDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SurveyDto"}}}}}}}, "/api/surveys/{id}/finalize": {"put": {"tags": ["Surveys"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/api/surveys/{id}/recalculate": {"put": {"tags": ["Surveys"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/api/surveys/{id}/cargo-weight": {"get": {"tags": ["Surveys"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CargoWeightResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CargoWeightResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CargoWeightResponse"}}}}}}}, "/api/users": {"get": {"tags": ["Users"], "responses": {"200": {"description": "OK"}}}, "post": {"tags": ["Users"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserCreateDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserCreateDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UserCreateDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/users/{id}": {"get": {"tags": ["Users"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}, "put": {"tags": ["Users"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserUpdateDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserUpdateDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UserUpdateDto"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["Users"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/api/users/{id}/change-password": {"post": {"tags": ["Users"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChangePasswordDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ChangePasswordDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ChangePasswordDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/users/by-role/{roleName}": {"get": {"tags": ["Users"], "parameters": [{"name": "<PERSON><PERSON><PERSON>", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/users/by-port/{portId}": {"get": {"tags": ["Users"], "parameters": [{"name": "portId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/api/vessels": {"get": {"tags": ["<PERSON><PERSON><PERSON>"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/VesselDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/VesselDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/VesselDto"}}}}}}}, "post": {"tags": ["<PERSON><PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VesselCreateDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/VesselCreateDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/VesselCreateDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/VesselDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/VesselDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/VesselDto"}}}}}}}, "/api/vessels/{id}": {"get": {"tags": ["<PERSON><PERSON><PERSON>"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/VesselDetailsDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/VesselDetailsDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/VesselDetailsDto"}}}}}}, "put": {"tags": ["<PERSON><PERSON><PERSON>"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VesselUpdateDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/VesselUpdateDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/VesselUpdateDto"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["<PERSON><PERSON><PERSON>"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/api/vessels/search": {"get": {"tags": ["<PERSON><PERSON><PERSON>"], "parameters": [{"name": "name", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/VesselDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/VesselDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/VesselDto"}}}}}}}}, "/api/vessels/imo/{imoNumber}": {"get": {"tags": ["<PERSON><PERSON><PERSON>"], "parameters": [{"name": "imoNumber", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/VesselDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/VesselDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/VesselDto"}}}}}}}, "/api/vessels/{id}/hydrostatic": {"put": {"tags": ["<PERSON><PERSON><PERSON>"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HydrostaticDataDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/HydrostaticDataDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/HydrostaticDataDto"}}}}, "responses": {"200": {"description": "OK"}}}}}, "components": {"schemas": {"BallastDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "tankName": {"type": "string", "nullable": true}, "volume": {"type": "number", "format": "double"}, "density": {"type": "number", "format": "double"}, "surveyId": {"type": "string", "format": "uuid"}}, "additionalProperties": false}, "CalculationResultDto": {"type": "object", "properties": {"displacement": {"type": "number", "format": "double"}, "netCargoWeight": {"type": "number", "format": "double"}}, "additionalProperties": false}, "CargoWeightResponse": {"type": "object", "properties": {"weight": {"type": "number", "format": "double"}}, "additionalProperties": false}, "ChangePasswordDto": {"type": "object", "properties": {"currentPassword": {"type": "string", "nullable": true}, "newPassword": {"type": "string", "nullable": true}}, "additionalProperties": false}, "DraughtMarkReadingDto": {"type": "object", "properties": {"portForward": {"type": "number", "format": "double"}, "starboardForward": {"type": "number", "format": "double"}, "portMidship": {"type": "number", "format": "double"}, "starboardMidship": {"type": "number", "format": "double"}, "portAft": {"type": "number", "format": "double"}, "starboardAft": {"type": "number", "format": "double"}, "readingTime": {"type": "string", "format": "date-time"}, "surveyId": {"type": "string", "format": "uuid"}}, "additionalProperties": false}, "EscaleCreateDto": {"required": ["portId", "reference", "vesselId"], "type": "object", "properties": {"reference": {"minLength": 1, "type": "string"}, "vesselId": {"type": "string", "format": "uuid"}, "portId": {"type": "string", "format": "uuid"}, "arrivalDate": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "EscaleDetailsDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "reference": {"type": "string", "nullable": true}, "arrivalDate": {"type": "string", "format": "date-time"}, "departureDate": {"type": "string", "format": "date-time", "nullable": true}, "isActive": {"type": "boolean"}, "vesselId": {"type": "string", "format": "uuid"}, "portId": {"type": "string", "format": "uuid"}, "vesselName": {"type": "string", "nullable": true}, "portName": {"type": "string", "nullable": true}, "surveyCount": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "EscaleDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "reference": {"type": "string", "nullable": true}, "arrivalDate": {"type": "string", "format": "date-time"}, "departureDate": {"type": "string", "format": "date-time", "nullable": true}, "isActive": {"type": "boolean"}, "vesselId": {"type": "string", "format": "uuid"}, "portId": {"type": "string", "format": "uuid"}}, "additionalProperties": false}, "HydrostaticDataDto": {"type": "object", "properties": {"displacementFactor": {"type": "number", "format": "double"}, "tpc": {"type": "number", "format": "double"}}, "additionalProperties": false}, "LoginRequest": {"type": "object", "properties": {"username": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}}, "additionalProperties": false}, "PortDetailsDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "code": {"type": "string", "nullable": true}, "country": {"type": "string", "nullable": true}, "standardWaterDensity": {"type": "number", "format": "double"}, "currentVessels": {"type": "array", "items": {"$ref": "#/components/schemas/VesselDto"}, "nullable": true}, "portStaff": {"type": "array", "items": {"$ref": "#/components/schemas/UserDto"}, "nullable": true}}, "additionalProperties": false}, "PortDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "code": {"type": "string", "nullable": true}, "country": {"type": "string", "nullable": true}, "standardWaterDensity": {"type": "number", "format": "double"}}, "additionalProperties": false}, "RoleCreateDto": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "canManageUsers": {"type": "boolean"}, "canManageAllPorts": {"type": "boolean"}}, "additionalProperties": false}, "RoleDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "canManageUsers": {"type": "boolean"}, "canManageAllPorts": {"type": "boolean"}}, "additionalProperties": false}, "RoleUpdateDto": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "canManageUsers": {"type": "boolean"}, "canManageAllPorts": {"type": "boolean"}}, "additionalProperties": false}, "SurveyCreateDto": {"type": "object", "properties": {"vesselId": {"type": "string", "format": "uuid"}, "escaleId": {"type": "string", "format": "uuid"}, "markReadings": {"$ref": "#/components/schemas/DraughtMarkReadingDto"}, "ballasts": {"type": "array", "items": {"$ref": "#/components/schemas/BallastDto"}, "nullable": true}}, "additionalProperties": false}, "SurveyDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "surveyNumber": {"type": "string", "nullable": true}, "createdDate": {"type": "string", "format": "date-time"}, "status": {"type": "string", "nullable": true}, "vesselId": {"type": "string", "format": "uuid"}, "escaleId": {"type": "string", "format": "uuid"}, "markReadings": {"type": "array", "items": {"$ref": "#/components/schemas/DraughtMarkReadingDto"}, "nullable": true}, "ballasts": {"type": "array", "items": {"$ref": "#/components/schemas/BallastDto"}, "nullable": true}, "calculationResult": {"$ref": "#/components/schemas/CalculationResultDto"}}, "additionalProperties": false}, "UserCreateDto": {"type": "object", "properties": {"username": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}, "fullName": {"type": "string", "nullable": true}, "roleId": {"type": "string", "format": "uuid"}, "portId": {"type": "string", "format": "uuid", "nullable": true}}, "additionalProperties": false}, "UserDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "username": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "fullName": {"type": "string", "nullable": true}, "portId": {"type": "string", "format": "uuid", "nullable": true}, "role": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UserUpdateDto": {"type": "object", "properties": {"email": {"type": "string", "nullable": true}, "fullName": {"type": "string", "nullable": true}, "roleId": {"type": "string", "format": "uuid"}, "portId": {"type": "string", "format": "uuid", "nullable": true}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "VesselCreateDto": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "imoNumber": {"type": "string", "nullable": true}, "deadweightTonnage": {"type": "number", "format": "double"}}, "additionalProperties": false}, "VesselDetailsDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "imoNumber": {"type": "string", "nullable": true}, "deadweightTonnage": {"type": "number", "format": "double"}, "recentSurveys": {"type": "array", "items": {"$ref": "#/components/schemas/SurveyDto"}, "nullable": true}, "parameters": {"$ref": "#/components/schemas/VesselParametersDto"}}, "additionalProperties": false}, "VesselDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "imoNumber": {"type": "string", "nullable": true}, "deadweightTonnage": {"type": "number", "format": "double"}}, "additionalProperties": false}, "VesselParametersDto": {"type": "object", "properties": {"lengthBetweenPerpendiculars": {"type": "number", "format": "double"}, "longitudinalCenterOfFloatation": {"type": "number", "format": "double"}, "momentToChangeTrim": {"type": "number", "format": "double"}}, "additionalProperties": false}, "VesselUpdateDto": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "deadweightTonnage": {"type": "number", "format": "double"}}, "additionalProperties": false}}, "securitySchemes": {"Bearer": {"type": "http", "description": "Enter JWT Bearer token", "scheme": "bearer", "bearerFormat": "JWT"}}}, "security": [{"Bearer": []}]}