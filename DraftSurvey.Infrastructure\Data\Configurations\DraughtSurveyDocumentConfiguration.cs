﻿// In C:\d\DraftSurveySolution\DraftSurvey.Infrastructure\Data\Configurations\DraughtSurveyDocumentConfiguration.cs

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using DraftSurvey.Domain.Entities;

namespace DraftSurvey.Infrastructure.Data.Configurations
{
    public class DraughtSurveyDocumentConfiguration : IEntityTypeConfiguration<DraughtSurveyDocument>
    {
        public void Configure(EntityTypeBuilder<DraughtSurveyDocument> builder)
        {
            builder.HasKey(dsd => dsd.Id); // Assuming DraughtSurveyDocument has its own Id as PK

            builder.Property(dsd => dsd.AttachedDate)
                .IsRequired();

            builder.Property(dsd => dsd.UsageType)
                .HasMaxLength(50)
                .IsRequired(false); // Adjust based on business rules

            builder.Property(dsd => dsd.Notes)
                .HasMaxLength(500);

            // Relationships for the join entity
            builder.HasOne(dsd => dsd.DraftSurvey)
                .WithMany(ds => ds.DraughtSurveyDocuments)
                .HasForeignKey(dsd => dsd.DraftSurveyId)
                .OnDelete(DeleteBehavior.Cascade); // If DraftSurvey is deleted, linked DraughtSurveyDocuments are also deleted

            builder.HasOne(dsd => dsd.Document)
                .WithMany() // Document might not need a direct collection back to DraughtSurveyDocument if this is purely a join entity
                .HasForeignKey(dsd => dsd.DocumentId)
                .OnDelete(DeleteBehavior.Restrict); // Prevent deleting a Document if it's still linked to a survey
        }
    }
}