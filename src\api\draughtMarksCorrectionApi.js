import api from './axiosConfig';

export const getCorrectionsBySurvey = async (surveyId) => {
  try {
    const response = await api.get(`/draughtmarkscorrection/by-survey/${surveyId}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching corrections:', error);
    throw error;
  }
};

export const createCorrection = async (correctionData) => {
  try {
    const response = await api.post('/draughtmarkscorrection', correctionData);
    return response.data;
  } catch (error) {
    console.error('Error creating correction:', error);
    throw error;
  }
};