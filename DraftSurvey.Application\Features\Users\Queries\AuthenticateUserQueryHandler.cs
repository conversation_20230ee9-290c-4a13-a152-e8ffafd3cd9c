﻿using DraftSurvey.Application.DTOs;
using DraftSurvey.Application.Interfaces;
using DraftSurvey.Domain.Interfaces;
using DraftSurvey.Infrastructure.Services.Auth;
using MediatR;
using System.Threading;
using System.Threading.Tasks;

namespace DraftSurvey.Application.Features.Users.Queries
{
    public class AuthenticateUserQueryHandler : IRequestHandler<AuthenticateUserQuery, AuthResponseDto>
    {
        private readonly IAuthService _authService;
        private readonly IUserRepository _userRepository;

        public AuthenticateUserQueryHandler(IAuthService authService, IUserRepository userRepository)
        {
            _authService = authService;
            _userRepository = userRepository;
        }

        public async Task<AuthResponseDto> Handle(AuthenticateUserQuery request, CancellationToken cancellationToken)
        {
            // Authentification avec AuthService
            var token = await _authService.AuthenticateAsync(request.Username, request.Password);
            if (string.IsNullOrEmpty(token)) return null;

            // Recherche de l'utilisateur via le UserRepository
            var user = await _userRepository.GetByUsernameAsync(request.Username);

            // Retour de la réponse contenant le token
            return new AuthResponseDto
            {
                UserId = user.Id,
                Token = token,
                Expiration = DateTime.UtcNow.AddHours(12)
            };
        }
    }
}
