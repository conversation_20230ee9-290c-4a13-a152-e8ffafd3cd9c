﻿using AutoMapper;
using DraftSurvey.Application.DTOs;
using DraftSurvey.Domain.Entities;
using DraftSurvey.Domain.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace DraftSurvey.WebApi.Controllers
{
    [Authorize]
    [ApiController]
    [Route("api/[controller]")]
    [Produces("application/json")]
    public class TaskAssignmentsController : ControllerBase
    {
        private readonly ITaskAssignmentRepository _repository;
        private readonly IDraftSurveyRepository _surveyRepository;
        private readonly IUserRepository _userRepository;
        private readonly IMapper _mapper;
        private readonly ILogger<TaskAssignmentsController> _logger;

        public TaskAssignmentsController(
            ITaskAssignmentRepository repository,
            IDraftSurveyRepository surveyRepository,
            IUserRepository userRepository,
            IMapper mapper,
            ILogger<TaskAssignmentsController> logger)
        {
            _repository = repository;
            _surveyRepository = surveyRepository;
            _userRepository = userRepository;
            _mapper = mapper;
            _logger = logger;
        }

        /// <summary>
        /// Get all task assignments
        /// </summary>
        [HttpGet]
        [Authorize(Roles = "Admin,TeamLead,Surveyor,Agent")]
        [ProducesResponseType(200, Type = typeof(IEnumerable<TaskAssignmentDto>))]
        public async Task<ActionResult<IEnumerable<TaskAssignmentDto>>> GetAll(string? include = null)
        {
            try
            {
                var tasks = await _repository.GetAllAsync(include);
                return Ok(_mapper.Map<IEnumerable<TaskAssignmentDto>>(tasks));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all task assignments");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Get task assignment by ID
        /// </summary>
        /// <param name="id">Task assignment ID</param>
        [HttpGet("{id}")]
        [Authorize(Roles = "Admin,TeamLead,Surveyor,Agent")]
        [ProducesResponseType(200, Type = typeof(TaskAssignmentDto))]
        [ProducesResponseType(404)]
        public async Task<ActionResult<TaskAssignmentDto>> GetById(Guid id)
        {
            try
            {
                var task = await _repository.GetByIdAsync(id);
                if (task == null) return NotFound();
                return Ok(_mapper.Map<TaskAssignmentDto>(task));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting task assignment by ID: {Id}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Get tasks assigned to a specific user
        /// </summary>
        /// <param name="userId">User ID</param>
        [HttpGet("user/{userId}")]
        [Authorize(Roles = "Admin,TeamLead,Surveyor,Agent")]
        [ProducesResponseType(200, Type = typeof(IEnumerable<TaskAssignmentDto>))]
        public async Task<ActionResult<IEnumerable<TaskAssignmentDto>>> GetByUserId(Guid userId)
        {
            try
            {
                var tasks = await _repository.GetByUserIdAsync(userId);
                return Ok(_mapper.Map<IEnumerable<TaskAssignmentDto>>(tasks));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting tasks for user: {UserId}", userId);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Get tasks for a specific survey
        /// </summary>
        /// <param name="surveyId">Survey ID</param>
        [HttpGet("survey/{surveyId}")]
        [Authorize(Roles = "Admin,TeamLead,Surveyor,Agent")]
        [ProducesResponseType(200, Type = typeof(IEnumerable<TaskAssignmentDto>))]
        public async Task<ActionResult<IEnumerable<TaskAssignmentDto>>> GetBySurveyId(Guid surveyId)
        {
            try
            {
                var tasks = await _repository.GetByDraftSurveyIdAsync(surveyId);
                return Ok(_mapper.Map<IEnumerable<TaskAssignmentDto>>(tasks));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting tasks for survey: {SurveyId}", surveyId);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Create a new task assignment
        /// </summary>
        [HttpPost]
        [Authorize(Roles = "Admin,TeamLead")]
        [ProducesResponseType(201, Type = typeof(TaskAssignmentDto))]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        public async Task<ActionResult<TaskAssignmentDto>> Create([FromBody] TaskAssignmentCreateDto dto)
        {
            try
            {
                // Validate input
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                // Check survey exists
                var survey = await _surveyRepository.GetByIdAsync(dto.DraftSurveyId);
                if (survey == null)
                {
                    return BadRequest("Survey not found");
                }

                // Check user exists
                var user = await _userRepository.GetByIdAsync(dto.UserId);
                if (user == null)
                {
                    return BadRequest("User not found");
                }

                // Map and create task
                var task = _mapper.Map<TaskAssignment>(dto);
                task.Status = "Assigned";
                task.AssignmentDate = DateTime.UtcNow;

                await _repository.AddAsync(task);
                await _repository.SaveChangesAsync();

                // Reload with related data for response
                var createdTask = await _repository.GetByIdAsync(task.Id);
                if (createdTask == null)
                {
                    return StatusCode(500, "Failed to retrieve created task");
                }

                return CreatedAtAction(
                    nameof(GetById),
                    new { id = task.Id },
                    _mapper.Map<TaskAssignmentDto>(createdTask));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating task assignment");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Update a task assignment
        /// </summary>
        /// <param name="id">Task assignment ID</param>
        [HttpPut("{id}")]
        [Authorize(Roles = "Admin,TeamLead")]
        [ProducesResponseType(204)]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> Update(Guid id, [FromBody] TaskAssignmentUpdateDto dto)
        {
            try
            {
                var task = await _repository.GetByIdAsync(id);
                if (task == null) return NotFound();

                _mapper.Map(dto, task);

                if (dto.IsCompleted.HasValue)
                {
                    task.CompletionDate = dto.IsCompleted.Value ? DateTime.UtcNow : null;
                    task.Status = dto.IsCompleted.Value ? "Completed" : "InProgress";
                }

                await _repository.UpdateAsync(task);
                await _repository.SaveChangesAsync();
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating task assignment: {Id}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Mark task as completed
        /// </summary>
        /// <param name="id">Task assignment ID</param>
        [HttpPut("{id}/complete")]
        [Authorize(Roles = "Admin,TeamLead,Surveyor")]
        [ProducesResponseType(204)]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> MarkAsCompleted(Guid id, [FromBody] UpdateTaskCompletionDto dto)
        {
            try
            {
                var task = await _repository.GetByIdAsync(id);
                if (task == null) return NotFound();

                task.IsCompleted = dto.IsCompleted;
                task.CompletionDate = dto.IsCompleted ? DateTime.UtcNow : null;
                task.Status = dto.IsCompleted ? "Completed" : "InProgress";
                task.Notes = dto.CompletionNotes;

                await _repository.UpdateAsync(task);
                await _repository.SaveChangesAsync();
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error marking task as completed: {Id}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Submit task for validation
        /// </summary>
        /// <param name="id">Task assignment ID</param>
        [HttpPost("{id}/submit")]
        [Authorize(Roles = "Admin,TeamLead,Agent")]
        [ProducesResponseType(200)]
        [ProducesResponseType(404)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> SubmitTask(Guid id, [FromBody] TaskSubmissionDto dto)
        {
            try
            {
                var task = await _repository.GetByIdAsync(id);
                if (task == null) return NotFound();

                task.Status = "PendingValidation";
                task.Notes = dto.Notes;

                await _repository.SaveChangesAsync();
                return Ok(new { Message = "Task submitted for validation" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error submitting task: {Id}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Approve a completed task
        /// </summary>
        /// <param name="id">Task assignment ID</param>
        [HttpPost("{id}/approve")]
        [Authorize(Roles = "TeamLead")]
        [ProducesResponseType(200, Type = typeof(TaskAssignmentDto))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> ApproveTask(Guid id, [FromQuery] string? comment)
        {
            try
            {
                var task = await _repository.GetByIdAsync(id);
                if (task == null) return NotFound();
                if (task.Status != "PendingValidation")
                    return BadRequest("Only tasks in PendingValidation status can be approved");

                task.Status = "Approved";
                task.CompletionDate = DateTime.UtcNow;
                if (!string.IsNullOrEmpty(comment))
                    task.Notes += $"\nApproval comment: {comment}";

                await _repository.SaveChangesAsync();
                return Ok(_mapper.Map<TaskAssignmentDto>(task));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error approving task: {Id}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Reject a completed task
        /// </summary>
        /// <param name="id">Task assignment ID</param>
        [HttpPost("{id}/reject")]
        [Authorize(Roles = "TeamLead")]
        [ProducesResponseType(200, Type = typeof(TaskAssignmentDto))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> RejectTask(Guid id, [FromBody] TaskRejectionDto dto)
        {
            try
            {
                var task = await _repository.GetByIdAsync(id);
                if (task == null) return NotFound();
                if (task.Status != "PendingValidation")
                    return BadRequest("Only tasks in PendingValidation status can be rejected");

                task.Status = dto.RequiresResubmission ? "Rejected" : "Cancelled";
                task.Notes += $"\nRejection reason: {dto.Reason}";

                await _repository.SaveChangesAsync();
                return Ok(_mapper.Map<TaskAssignmentDto>(task));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error rejecting task: {Id}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Delete a task assignment
        /// </summary>
        /// <param name="id">Task assignment ID</param>
        [HttpDelete("{id}")]
        [Authorize(Roles = "Admin,TeamLead")]
        [ProducesResponseType(204)]
        [ProducesResponseType(404)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> Delete(Guid id)
        {
            try
            {
                var task = await _repository.GetByIdAsync(id);
                if (task == null) return NotFound();

                await _repository.DeleteAsync(id);
                await _repository.SaveChangesAsync();
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting task: {Id}", id);
                return StatusCode(500, "Internal server error");
            }
        }
    }
}