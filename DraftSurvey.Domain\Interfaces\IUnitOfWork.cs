// In DraftSurvey.Domain/Interfaces/IUnitOfWork.cs
using System;
using System.Threading.Tasks;

namespace DraftSurvey.Domain.Interfaces
{
    public interface IUnitOfWork : IDisposable
    {
        // Existing Repositories
        IDraftSurveyRepository DraftSurveys { get; }
        IVesselRepository Vessels { get; }
        IEscaleRepository Escales { get; }
        IUserRepository Users { get; }
        IBallastRepository Ballasts { get; }
        IDraughtMarkReadingRepository DraughtMarkReadings { get; }

        // NEW Repositories (Add these)
        IAvisChargementRepository AvisChargements { get; }
        IDraughtMarksCorrectionRepository DraughtMarksCorrections { get; }
        ILiquidStatementRepository LiquidStatements { get; }
        ISeaWaterDensityStatementRepository SeaWaterDensityStatements { get; }
        IFreshWaterTankRepository FreshWaterTanks { get; }
        IInspectionRepository Inspections { get; }
        IDraughtSurveyGlobalCalculationRepository DraughtSurveyGlobalCalculations { get; }

        Task<int> CommitAsync();
        Task BeginTransactionAsync();
        Task CommitTransactionAsync();
        Task RollbackTransactionAsync();
        Task<int> SaveChangesAsync();
    }
}