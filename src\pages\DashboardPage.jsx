import React from "react";
import {
  Drawer,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Box,
  Typography,
  useTheme,
  useMediaQuery,
  CssBaseline,
  Card,
  CardContent,
  Grid,
  Divider,
  Chip,
  alpha,
} from "@mui/material";
import {
  Group as GroupIcon,
  LocationCity as LocationCityIcon,
  Sailing as SailingIcon,
  Description as DescriptionIcon,
  Task as TaskIcon,
  DirectionsBoat as DirectionsBoatIcon,
  Notifications as NotificationsIcon,
  Dashboard as DashboardIcon,
  FiberManualRecord as DotIcon,
} from "@mui/icons-material";
import { useNavigate, useLocation } from "react-router-dom";
import Navbar from "../components/Navbar/Navbar"; // Assuming Navbar handles AppBar

const drawerWidth = 240;

const DashboardPage = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("md"));
  const [dashboardDrawerOpen, setDashboardDrawerOpen] = React.useState(false);

  const toggleDashboardDrawer = () => {
    setDashboardDrawerOpen(!dashboardDrawerOpen);
  };

  const menuItems = [
    { title: "Tableau de bord", icon: <DashboardIcon />, path: "/dashboard" },
    { title: "Utilisateurs", icon: <GroupIcon />, path: "/users" },
    { title: "Ports", icon: <LocationCityIcon />, path: "/ports" },
    { title: "Escales", icon: <SailingIcon />, path: "/escales" },
    { title: "Drafts", icon: <DescriptionIcon />, path: "/drafts" },
    { title: "Tâches", icon: <TaskIcon />, path: "/tasks" },
    { title: "Navires", icon: <DirectionsBoatIcon />, path: "/ships" },
  ];

  const statsData = [
    {
      title: "Escales en cours",
      value: 8,
      color: theme.palette.warning.main, // Yellow/Orange
      bgColorLight: alpha(theme.palette.warning.light, 0.1), // For gradient effect
    },
    {
      title: "Drafts validés",
      value: 24,
      color: theme.palette.info.main, // Blue
      bgColorLight: alpha(theme.palette.info.light, 0.1), // For gradient effect
    },
    {
      title: "Alertes",
      value: 3,
      color: theme.palette.error.main, // Red
      bgColorLight: alpha(theme.palette.error.light, 0.1), // For gradient effect
    },
    {
      title: "Navires au port",
      value: 5,
      color: theme.palette.text.primary, // Black (default text color)
      bgColorLight: theme.palette.grey[100], // For neutral cards
    },
  ];

  const notifications = [
    { id: 1, text: "Nouvelle escale programmée", time: "10:30", read: false },
    { id: 2, text: "Draft en attente de validation", time: "09:45", read: true },
    { id: 3, text: "Mise à jour terminée", time: "Hier", read: true },
    { id: 4, text: "Maintenance système planifiée", time: "Il y a 2j", read: false },
  ];

  const drawer = (
    <Box
      sx={{
        height: "100%",
        display: "flex",
        flexDirection: "column",
        bgcolor: theme.palette.background.paper,
      }}
    >
      <Box
        sx={{
          p: 2,
          bgcolor: theme.palette.grey[900],
          color: theme.palette.common.white,
          textAlign: "center",
          borderBottom: `1px solid ${theme.palette.divider}`,
        }}
      >
        <Typography variant="h6" fontWeight="bold">
          DRAFT SURVEY
        </Typography>
      </Box>

      <List sx={{ flexGrow: 1, py: 0 }}>
        {menuItems.map((item, index) => (
          <ListItem
            button
            key={index}
            onClick={() => {
              navigate(item.path);
              if (isMobile) setDashboardDrawerOpen(false);
            }}
            selected={location.pathname === item.path}
            sx={{
              px: 3,
              py: 1.5,
              borderLeft: "3px solid transparent",
              color: theme.palette.text.primary,
              "&:hover": {
                bgcolor: theme.palette.grey[100],
              },
              "&.Mui-selected": {
                bgcolor: alpha(theme.palette.primary.main, 0.08),
                borderLeft: `3px solid ${theme.palette.primary.main}`,
                "& .MuiListItemIcon-root": {
                  color: theme.palette.primary.main,
                },
                "& .MuiListItemText-primary": {
                  fontWeight: "bold",
                  color: theme.palette.primary.main,
                },
              },
            }}
          >
            <ListItemIcon sx={{ minWidth: 40, color: theme.palette.text.secondary }}>
              {item.icon}
            </ListItemIcon>
            <ListItemText
              primary={item.title}
              primaryTypographyProps={{ fontWeight: "medium" }}
            />
          </ListItem>
        ))}
      </List>
    </Box>
  );

  return (
    <Box sx={{ display: "flex", minHeight: "100vh", bgcolor: theme.palette.grey[50] }}>
      <CssBaseline />

      <Navbar toggleDashboardDrawer={toggleDashboardDrawer} />

      <Box
        component="nav"
        sx={{ width: { md: drawerWidth }, flexShrink: { md: 0 } }}
      >
        <Drawer
          variant={isMobile ? "temporary" : "permanent"}
          open={isMobile ? dashboardDrawerOpen : true}
          onClose={toggleDashboardDrawer}
          ModalProps={{ keepMounted: true }}
          sx={{
            "& .MuiDrawer-paper": {
              width: drawerWidth,
              boxSizing: "border-box",
              mt: { xs: "56px", sm: "64px" },
              height: { xs: "calc(100% - 56px)", sm: "calc(100% - 64px)" },
              borderRight: `1px solid ${theme.palette.divider}`,
            },
          }}
        >
          {drawer}
        </Drawer>
      </Box>

      <Box
        component="main"
        sx={{
          flexGrow: 1,
          p: 3,
          width: { md: `calc(100% - ${drawerWidth}px)` },
          mt: { xs: "56px", sm: "64px" },
          bgcolor: theme.palette.background.default,
        }}
      >
        <Typography variant="h4" fontWeight="bold" mb={4} color="text.primary">
          Tableau de Bord
        </Typography>

        {/* --- Statistiques --- */}
        <Box mb={4} sx={{
          p: 3,
          borderRadius: theme.shape.borderRadius,
          border: `1px solid ${theme.palette.divider}`,
          bgcolor: theme.palette.background.paper,
        }}>
          <Typography variant="h5" fontWeight="bold" mb={3} color="text.primary">
            Statistiques Clés
          </Typography>
          <Grid container spacing={3}>
            {statsData.map((stat, index) => (
              <Grid item xs={12} sm={6} md={3} key={index}>
                <Card
                  sx={{
                    borderRadius: theme.shape.borderRadius,
                    transition: 'transform 0.3s ease-in-out, box-shadow 0.3s ease-in-out',
                    '&:hover': {
                      transform: 'translateY(-5px)', // Lift effect
                      boxShadow: theme.shadows[6], // More prominent shadow on hover
                    },
                    boxShadow: theme.shadows[3], // Default shadow
                    position: 'relative', // Needed for pseudo-element gradient
                    overflow: 'hidden', // To contain the pseudo-element
                    minHeight: 120,
                  }}
                >
                    <Box
                      sx={{
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        right: 0,
                        bottom: 0,
                        background: `linear-gradient(to bottom right, ${stat.bgColorLight}, ${alpha(stat.bgColorLight, 0)})`, // Subtle gradient
                        opacity: 0.7, // Control intensity
                        zIndex: 0, // Ensure it's behind content
                      }}
                    />
                  <CardContent sx={{
                      p: 2,
                      textAlign: 'center',
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      justifyContent: 'center',
                      position: 'relative', // Bring content above pseudo-element
                      zIndex: 1,
                  }}>
                    <Typography variant="body2" color="text.secondary" mb={0.5}>
                      {stat.title}
                    </Typography>
                    <Typography
                      variant="h4"
                      fontWeight="bold"
                      color={stat.color}
                      sx={{ display: 'flex', alignItems: 'center' }}
                    >
                      {stat.value}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Box>

        {/* --- Présentation de l'application --- */}
        <Box mb={4} sx={{
          p: 3,
          borderRadius: theme.shape.borderRadius,
          border: `1px solid ${theme.palette.divider}`,
          bgcolor: theme.palette.background.paper,
        }}>
          <Typography variant="h5" fontWeight="bold" gutterBottom color="text.primary">
            Présentation de l'Application
          </Typography>
          <Typography variant="body1" paragraph color="text.secondary">
            L'application **DraftSurvey** est une solution professionnelle pour la gestion des opérations de tirant d'eau dans les ports. Elle permet le suivi complet des navires, des escales et des calculs de cargaison, offrant une vue d'ensemble claire et des outils précis pour les professionnels maritimes.
          </Typography>
          <Typography variant="h6" paragraph color="text.primary">
            **Fonctionnalités principales** :
          </Typography>
          <Box component="ul" sx={{ pl: 2, mb: 2, color: theme.palette.text.secondary }}>
            <li><Typography variant="body1" color="inherit">Gestion des escales portuaires avec planification détaillée.</Typography></li>
            <li><Typography variant="body1" color="inherit">Calcul précis des tirants d'eau pour optimiser le chargement.</Typography></li>
            <li><Typography variant="body1" color="inherit">Suivi des navires en temps réel et historique des mouvements.</Typography></li>
            <li><Typography variant="body1" color="inherit">Gestion des tâches et alertes personnalisables pour ne rien manquer.</Typography></li>
          </Box>
          
        </Box>

        {/* --- Notifications --- */}
        <Box sx={{
          p: 3,
          borderRadius: theme.shape.borderRadius,
          border: `1px solid ${theme.palette.divider}`,
          bgcolor: theme.palette.background.paper,
        }}>
          <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
            <NotificationsIcon sx={{ mr: 1, color: theme.palette.text.secondary }} />
            <Typography variant="h5" fontWeight="bold" color="text.primary">
              Notifications
            </Typography>
            {notifications.some(n => !n.read) && (
              <DotIcon sx={{ fontSize: 10, color: theme.palette.error.main, ml: 1 }} />
            )}
          </Box>
          <Divider sx={{ mb: 2 }} />
          {notifications.length === 0 ? (
            <Typography variant="body2" color="text.secondary" sx={{ fontStyle: 'italic' }}>
              Aucune notification récente.
            </Typography>
          ) : (
            notifications.map((notification) => (
              <Box
                key={notification.id}
                sx={{
                  mb: 1.5,
                  p: 1.5,
                  bgcolor: !notification.read
                    ? alpha(theme.palette.warning.light, 0.05)
                    : "transparent",
                  borderRadius: theme.shape.borderRadius,
                  border: !notification.read ? `1px solid ${alpha(theme.palette.warning.main, 0.1)}` : 'none',
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                }}
              >
                <Typography
                  variant="body2"
                  fontWeight={!notification.read ? "bold" : "normal"}
                  color={!notification.read ? theme.palette.text.primary : theme.palette.text.secondary}
                >
                  {notification.text}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  {notification.time}
                </Typography>
              </Box>
            ))
          )}
        </Box>
      </Box>
    </Box>
  );
};

export default DashboardPage;