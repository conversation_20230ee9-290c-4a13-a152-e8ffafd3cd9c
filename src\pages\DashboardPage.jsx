import React from "react";
import {
  Drawer,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Box,
  Typography,
  useTheme,
  useMediaQuery,
  CssBaseline,
} from "@mui/material";
import { createAlphaColor } from "../utils/colorUtils";
import {
  Group as GroupIcon,
  LocationCity as LocationCityIcon,
  Sailing as SailingIcon,
  Description as DescriptionIcon,
  Task as TaskIcon,
  DirectionsBoat as DirectionsBoatIcon,
  Dashboard as DashboardIcon,
  Storage as StorageIcon,
  Assignment as AssignmentIcon,
  Water as WaterIcon,
  Assessment as AssessmentIcon,
  ExpandLess,
  ExpandMore,
  AccountTree as AccountTreeIcon,
  Inventory as InventoryIcon,
  LocalShipping as LocalShippingIcon,
  Science as ScienceIcon,
} from "@mui/icons-material";
import { useNavigate, useLocation, Outlet } from "react-router-dom";
import Navbar from "../components/Navbar/Navbar"; // Assuming Navbar handles AppBar
import ModernDashboardContent from "../components/Dashboard/ModernDashboardContent";

const drawerWidth = 240;

const DashboardPage = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("md"));
  const [dashboardDrawerOpen, setDashboardDrawerOpen] = React.useState(false);
  const [entitiesOpen, setEntitiesOpen] = React.useState(false);

  const toggleDashboardDrawer = () => {
    setDashboardDrawerOpen(!dashboardDrawerOpen);
  };

  const toggleEntities = () => {
    setEntitiesOpen(!entitiesOpen);
  };

  const menuItems = [
    { title: "Tableau de bord", icon: <DashboardIcon />, path: "/dashboard" },
    { title: "Utilisateurs", icon: <GroupIcon />, path: "/users" },
    { title: "Ports", icon: <LocationCityIcon />, path: "/ports" },
    { title: "Escales", icon: <SailingIcon />, path: "/escales" },
    { title: "Drafts", icon: <DescriptionIcon />, path: "/drafts" },
    { title: "Tâches", icon: <TaskIcon />, path: "/tasks" },
    { title: "Navires", icon: <DirectionsBoatIcon />, path: "/ships" },
  ];

  const entityItems = [
    { title: "Vessels", icon: <DirectionsBoatIcon />, path: "/entities/vessels" },
    { title: "Ballasts", icon: <WaterIcon />, path: "/entities/ballasts" },
    { title: "Inspections", icon: <AssessmentIcon />, path: "/entities/inspections" },
    { title: "Documents", icon: <AssignmentIcon />, path: "/entities/documents" },
    { title: "Liquid Statements", icon: <ScienceIcon />, path: "/entities/liquid-statements" },
    { title: "Fresh Water Tanks", icon: <StorageIcon />, path: "/entities/fresh-water-tanks" },
    { title: "Sea Water Density", icon: <WaterIcon />, path: "/entities/sea-water-density" },
    { title: "Task Assignments", icon: <AccountTreeIcon />, path: "/entities/task-assignments" },
    { title: "Draught Readings", icon: <InventoryIcon />, path: "/entities/draught-readings" },
    { title: "Cargo Loading", icon: <LocalShippingIcon />, path: "/entities/cargo-loading" },
  ];



  const drawer = (
    <Box
      sx={{
        height: "100%",
        display: "flex",
        flexDirection: "column",
        bgcolor: theme.palette.background.paper,
      }}
    >
      <Box
        sx={{
          p: 2,
          bgcolor: theme.palette.grey[900],
          color: theme.palette.common.white,
          textAlign: "center",
          borderBottom: `1px solid ${theme.palette.divider}`,
        }}
      >
        <Typography variant="h6" fontWeight="bold">
          DRAFT SURVEY
        </Typography>
      </Box>

      <List sx={{ flexGrow: 1, py: 0 }}>
        {menuItems.map((item, index) => (
          <ListItem
            button
            key={index}
            onClick={() => {
              navigate(item.path);
              if (isMobile) setDashboardDrawerOpen(false);
            }}
            selected={location.pathname === item.path}
            sx={{
              px: 3,
              py: 1.5,
              borderLeft: "3px solid transparent",
              color: theme.palette.text.primary,
              "&:hover": {
                bgcolor: theme.palette.grey[100],
              },
              "&.Mui-selected": {
                bgcolor: createAlphaColor(theme.palette.primary.main, 0.08),
                borderLeft: `3px solid ${theme.palette.primary.main}`,
                "& .MuiListItemIcon-root": {
                  color: theme.palette.primary.main,
                },
                "& .MuiListItemText-primary": {
                  fontWeight: "bold",
                  color: theme.palette.primary.main,
                },
              },
            }}
          >
            <ListItemIcon sx={{ minWidth: 40, color: theme.palette.text.secondary }}>
              {item.icon}
            </ListItemIcon>
            <ListItemText
              primary={item.title}
              primaryTypographyProps={{ fontWeight: "medium" }}
            />
          </ListItem>
        ))}

        <Divider sx={{ my: 1 }} />

        {/* Entities Section */}
        <ListItem
          button
          onClick={toggleEntities}
          sx={{
            px: 3,
            py: 1.5,
            borderLeft: "3px solid transparent",
            color: theme.palette.text.primary,
            "&:hover": {
              bgcolor: theme.palette.grey[100],
            },
          }}
        >
          <ListItemIcon sx={{ minWidth: 40, color: theme.palette.text.secondary }}>
            <StorageIcon />
          </ListItemIcon>
          <ListItemText
            primary="Entities Management"
            primaryTypographyProps={{ fontWeight: "medium" }}
          />
          {entitiesOpen ? <ExpandLess /> : <ExpandMore />}
        </ListItem>

        {/* Entity Sub-items */}
        {entitiesOpen && entityItems.map((item, index) => (
          <ListItem
            button
            key={`entity-${index}`}
            onClick={() => {
              navigate(item.path);
              if (isMobile) setDashboardDrawerOpen(false);
            }}
            selected={location.pathname === item.path}
            sx={{
              pl: 6,
              pr: 3,
              py: 1,
              borderLeft: "3px solid transparent",
              color: theme.palette.text.secondary,
              "&:hover": {
                bgcolor: theme.palette.grey[50],
              },
              "&.Mui-selected": {
                bgcolor: createAlphaColor(theme.palette.secondary.main, 0.08),
                borderLeft: `3px solid ${theme.palette.secondary.main}`,
                "& .MuiListItemIcon-root": {
                  color: theme.palette.secondary.main,
                },
                "& .MuiListItemText-primary": {
                  fontWeight: "bold",
                  color: theme.palette.secondary.main,
                },
              },
            }}
          >
            <ListItemIcon sx={{ minWidth: 35, color: theme.palette.text.disabled }}>
              {item.icon}
            </ListItemIcon>
            <ListItemText
              primary={item.title}
              primaryTypographyProps={{ fontSize: "0.875rem" }}
            />
          </ListItem>
        ))}
      </List>
    </Box>
  );

  return (
    <Box sx={{ display: "flex", minHeight: "100vh", bgcolor: theme.palette.grey[50] }}>
      <CssBaseline />

      <Navbar toggleDashboardDrawer={toggleDashboardDrawer} />

      <Box
        component="nav"
        sx={{ width: { md: drawerWidth }, flexShrink: { md: 0 } }}
      >
        <Drawer
          variant={isMobile ? "temporary" : "permanent"}
          open={isMobile ? dashboardDrawerOpen : true}
          onClose={toggleDashboardDrawer}
          ModalProps={{ keepMounted: true }}
          sx={{
            "& .MuiDrawer-paper": {
              width: drawerWidth,
              boxSizing: "border-box",
              mt: { xs: "56px", sm: "64px" },
              height: { xs: "calc(100% - 56px)", sm: "calc(100% - 64px)" },
              borderRight: `1px solid ${theme.palette.divider}`,
            },
          }}
        >
          {drawer}
        </Drawer>
      </Box>

      <Box
        component="main"
        sx={{
          flexGrow: 1,
          width: { md: `calc(100% - ${drawerWidth}px)` },
          mt: { xs: "56px", sm: "64px" },
          bgcolor: theme.palette.background.default,
        }}
      >
        {/* Show modern dashboard content only on the main dashboard route */}
        {location.pathname === '/dashboard' ? (
          <ModernDashboardContent />
        ) : (
          <Outlet />
        )}
      </Box>
    </Box>
  );
};

export default DashboardPage;