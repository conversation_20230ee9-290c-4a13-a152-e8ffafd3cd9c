import React, { useState, useEffect } from 'react';
import EntityPageLayout from '../../components/Shared/EntityPageLayout';
import { Ruler, TrendingUp, TrendingDown, Navigation, Calendar } from 'lucide-react';

const DraughtReadingsPage = () => {
  const [draughtReadings, setDraughtReadings] = useState([]);
  const [loading, setLoading] = useState(true);

  // Mock data - replace with actual API call
  useEffect(() => {
    const mockDraughtReadings = [
      {
        id: 1,
        readingId: "DR-2024-001",
        vesselName: "MSC OSCAR",
        readingType: "Pre-loading",
        forwardPort: 8.45,
        forwardStarboard: 8.42,
        midshipPort: 8.50,
        midshipStarboard: 8.48,
        aftPort: 8.55,
        aftStarboard: 8.53,
        meanDraught: 8.49,
        trim: 0.10,
        list: 0.02,
        readingDate: "2024-01-15T09:30:00Z",
        surveyor: "<PERSON>",
        weather: "Clear",
        seaState: "Calm",
        location: "Port of Rotterdam",
        notes: "All readings within acceptable limits"
      },
      {
        id: 2,
        readingId: "DR-2024-002",
        vesselName: "MSC OSCAR",
        readingType: "Post-loading",
        forwardPort: 12.85,
        forwardStarboard: 12.82,
        midshipPort: 12.90,
        midshipStarboard: 12.88,
        aftPort: 12.95,
        aftStarboard: 12.93,
        meanDraught: 12.89,
        trim: 0.10,
        list: 0.03,
        readingDate: "2024-01-15T16:45:00Z",
        surveyor: "John Anderson",
        weather: "Clear",
        seaState: "Calm",
        location: "Port of Rotterdam",
        notes: "Cargo loading completed successfully"
      },
      {
        id: 3,
        readingId: "DR-2024-003",
        vesselName: "EVER GIVEN",
        readingType: "Arrival",
        forwardPort: 10.25,
        forwardStarboard: 10.23,
        midshipPort: 10.30,
        midshipStarboard: 10.28,
        aftPort: 10.35,
        aftStarboard: 10.33,
        meanDraught: 10.29,
        trim: 0.10,
        list: 0.02,
        readingDate: "2024-01-14T08:15:00Z",
        surveyor: "Sarah Mitchell",
        weather: "Partly Cloudy",
        seaState: "Slight",
        location: "Suez Canal",
        notes: "Vessel arrived with normal draught"
      },
      {
        id: 4,
        readingId: "DR-2024-004",
        vesselName: "MAERSK MADRID",
        readingType: "Departure",
        forwardPort: 14.15,
        forwardStarboard: 14.12,
        midshipPort: 14.20,
        midshipStarboard: 14.18,
        aftPort: 14.25,
        aftStarboard: 14.23,
        meanDraught: 14.19,
        trim: 0.10,
        list: 0.03,
        readingDate: "2024-01-13T14:30:00Z",
        surveyor: "Michael Chen",
        weather: "Sunny",
        seaState: "Moderate",
        location: "Singapore",
        notes: "Fully loaded departure"
      },
      {
        id: 5,
        readingId: "DR-2024-005",
        vesselName: "EVER GIVEN",
        readingType: "Ballast",
        forwardPort: 6.85,
        forwardStarboard: 6.83,
        midshipPort: 6.90,
        midshipStarboard: 6.88,
        aftPort: 6.95,
        aftStarboard: 6.93,
        meanDraught: 6.89,
        trim: 0.10,
        list: 0.02,
        readingDate: "2024-01-12T11:00:00Z",
        surveyor: "Emma Rodriguez",
        weather: "Overcast",
        seaState: "Rough",
        location: "North Sea",
        notes: "Ballast condition readings"
      }
    ];

    setTimeout(() => {
      setDraughtReadings(mockDraughtReadings);
      setLoading(false);
    }, 1000);
  }, []);

  const columns = [
    {
      header: 'Reading Details',
      key: 'readingId',
      render: (reading) => (
        <div className="flex items-center">
          <div className="flex-shrink-0 h-10 w-10">
            <div className="h-10 w-10 rounded-full bg-emerald-100 flex items-center justify-center">
              <Ruler className="h-5 w-5 text-emerald-600" />
            </div>
          </div>
          <div className="ml-4">
            <div className="text-sm font-medium text-gray-900">{reading.readingId}</div>
            <div className="text-sm text-gray-500">{reading.vesselName}</div>
          </div>
        </div>
      )
    },
    {
      header: 'Reading Type',
      key: 'readingType',
      render: (reading) => {
        const typeColors = {
          'Pre-loading': 'bg-blue-100 text-blue-800',
          'Post-loading': 'bg-green-100 text-green-800',
          'Arrival': 'bg-purple-100 text-purple-800',
          'Departure': 'bg-orange-100 text-orange-800',
          'Ballast': 'bg-gray-100 text-gray-800'
        };
        
        return (
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${typeColors[reading.readingType] || 'bg-gray-100 text-gray-800'}`}>
            {reading.readingType}
          </span>
        );
      }
    },
    {
      header: 'Forward Readings',
      key: 'forward',
      render: (reading) => (
        <div className="text-sm">
          <div className="text-gray-900">Port: {reading.forwardPort.toFixed(2)}m</div>
          <div className="text-gray-500">Stbd: {reading.forwardStarboard.toFixed(2)}m</div>
        </div>
      )
    },
    {
      header: 'Midship Readings',
      key: 'midship',
      render: (reading) => (
        <div className="text-sm">
          <div className="text-gray-900">Port: {reading.midshipPort.toFixed(2)}m</div>
          <div className="text-gray-500">Stbd: {reading.midshipStarboard.toFixed(2)}m</div>
        </div>
      )
    },
    {
      header: 'Aft Readings',
      key: 'aft',
      render: (reading) => (
        <div className="text-sm">
          <div className="text-gray-900">Port: {reading.aftPort.toFixed(2)}m</div>
          <div className="text-gray-500">Stbd: {reading.aftStarboard.toFixed(2)}m</div>
        </div>
      )
    },
    {
      header: 'Mean Draught',
      key: 'meanDraught',
      render: (reading) => (
        <div className="text-center">
          <div className="text-lg font-semibold text-blue-600">
            {reading.meanDraught.toFixed(2)}m
          </div>
        </div>
      )
    },
    {
      header: 'Trim & List',
      key: 'trimList',
      render: (reading) => (
        <div className="text-sm">
          <div className="text-gray-900 flex items-center">
            <Navigation className="h-4 w-4 text-blue-500 mr-1" />
            Trim: {reading.trim.toFixed(2)}m
          </div>
          <div className="text-gray-500">
            List: {reading.list.toFixed(2)}m
          </div>
        </div>
      )
    },
    {
      header: 'Surveyor',
      key: 'surveyor',
      render: (reading) => (
        <div className="text-sm text-gray-900">{reading.surveyor}</div>
      )
    },
    {
      header: 'Conditions',
      key: 'conditions',
      render: (reading) => (
        <div className="text-sm">
          <div className="text-gray-900">{reading.weather}</div>
          <div className="text-gray-500">Sea: {reading.seaState}</div>
        </div>
      )
    },
    {
      header: 'Date & Location',
      key: 'dateLocation',
      render: (reading) => (
        <div className="text-sm">
          <div className="text-gray-900 flex items-center">
            <Calendar className="h-4 w-4 text-gray-400 mr-1" />
            {new Date(reading.readingDate).toLocaleDateString()}
          </div>
          <div className="text-gray-500">{reading.location}</div>
        </div>
      )
    }
  ];

  const handleAdd = () => {
    console.log('Add new draught reading');
  };

  const handleEdit = (reading) => {
    console.log('Edit draught reading:', reading);
  };

  const handleDelete = (reading) => {
    console.log('Delete draught reading:', reading);
  };

  const handleView = (reading) => {
    console.log('View draught reading:', reading);
  };

  const averageMeanDraught = draughtReadings.reduce((sum, r) => sum + r.meanDraught, 0) / draughtReadings.length || 0;
  const maxDraught = Math.max(...draughtReadings.map(r => r.meanDraught));
  const minDraught = Math.min(...draughtReadings.map(r => r.meanDraught));
  const averageTrim = draughtReadings.reduce((sum, r) => sum + Math.abs(r.trim), 0) / draughtReadings.length || 0;

  return (
    <EntityPageLayout
      title="Draught Readings Management"
      description="Monitor and manage vessel draught readings for accurate cargo calculations and stability assessments."
      data={draughtReadings}
      columns={columns}
      onAdd={handleAdd}
      onEdit={handleEdit}
      onDelete={handleDelete}
      onView={handleView}
      loading={loading}
      searchPlaceholder="Search draught readings by vessel, surveyor, type..."
    >
      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Ruler className="h-8 w-8 text-emerald-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Total Readings</p>
              <p className="text-2xl font-semibold text-gray-900">{draughtReadings.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <TrendingUp className="h-8 w-8 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Avg Draught</p>
              <p className="text-2xl font-semibold text-gray-900">{averageMeanDraught.toFixed(2)}m</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <TrendingDown className="h-8 w-8 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Range</p>
              <p className="text-2xl font-semibold text-gray-900">{(maxDraught - minDraught).toFixed(2)}m</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Navigation className="h-8 w-8 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Avg Trim</p>
              <p className="text-2xl font-semibold text-gray-900">{averageTrim.toFixed(2)}m</p>
            </div>
          </div>
        </div>
      </div>
    </EntityPageLayout>
  );
};

export default DraughtReadingsPage;
