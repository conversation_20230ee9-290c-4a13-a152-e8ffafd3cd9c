import React, { useState, useEffect } from 'react';
import {
  Box, TextField, Button, FormControl, InputLabel, Select, MenuItem,
  CircularProgress, Alert, Typography, useTheme // Import useTheme
} from '@mui/material';
import { getVessels, getPorts } from '../../api/escaleApi';
import { format } from 'date-fns';

const EditEscaleForm = ({ escale, onSave, onCancel }) => {
  const theme = useTheme(); // Access the theme

  const [formData, setFormData] = useState({
    reference: '',
    vesselId: '',
    portId: '',
    arrivalDate: ''
  });
  const [vessels, setVessels] = useState([]);
  const [ports, setPorts] = useState([]);
  const [loadingOptions, setLoadingOptions] = useState(true); // Loading state for vessels/ports
  const [submitting, setSubmitting] = useState(false); // Loading state for form submission
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchData = async () => {
      setLoadingOptions(true);
      setError(null);
      try {
        const [vesselsData, portsData] = await Promise.all([
          getVessels(),
          getPorts()
        ]);
        setVessels(vesselsData);
        setPorts(portsData);
      } catch (err) {
        setError(err.response?.data?.message || err.message || "Impossible de charger les listes de navires et ports.");
        console.error("Fetch options error:", err);
      } finally {
        setLoadingOptions(false);
      }
    };

    fetchData();
  }, []); // Run once to fetch vessels and ports

  useEffect(() => {
    // Populate form when 'escale' prop changes
    if (escale) {
      setFormData({
        reference: escale.reference || '',
        vesselId: escale.vesselId || '',
        portId: escale.portId || '',
        arrivalDate: escale.arrivalDate ? format(new Date(escale.arrivalDate), "yyyy-MM-dd'T'HH:mm") : ''
      });
      setError(null); // Clear errors on new escale selection
    }
  }, [escale]); // Re-run when escale prop changes

  const handleSubmit = async (e) => {
    e.preventDefault();
    setSubmitting(true);
    setError(null);

    try {
      // Basic validation
      if (!formData.reference.trim() || !formData.vesselId || !formData.portId || !formData.arrivalDate) {
        throw new Error("Veuillez remplir tous les champs obligatoires.");
      }

      await onSave(escale.id, formData); // Pass escale ID and updated data to parent
    } catch (err) {
      setError(err.response?.data?.message || err.message || "Échec de l'enregistrement des modifications.");
      console.error("Form submission error:", err);
    } finally {
      setSubmitting(false);
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  if (loadingOptions) {
    return (
      <Box display="flex" justifyContent="center" py={4}>
        <CircularProgress color="primary" />
        <Typography ml={2}>Chargement des options...</Typography>
      </Box>
    );
  }

  return (
    <Box component="form" onSubmit={handleSubmit} noValidate sx={{ mt: 1 }}>
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <TextField
        fullWidth
        label="Référence de l'escale *"
        name="reference"
        value={formData.reference}
        onChange={handleChange}
        required
        variant="outlined"
        sx={{ mb: 3 }}
        disabled={submitting}
      />

      <FormControl fullWidth sx={{ mb: 3 }} required disabled={submitting}>
        <InputLabel id="vessel-select-label">Navire *</InputLabel>
        <Select
          labelId="vessel-select-label"
          id="vessel-select"
          name="vesselId"
          value={formData.vesselId}
          label="Navire *"
          onChange={handleChange}
        >
          <MenuItem value="">
            <em>Sélectionner un navire</em>
          </MenuItem>
          {vessels.map((vessel) => (
            <MenuItem key={vessel.id} value={vessel.id}>
              {vessel.name} (IMO: {vessel.imoNumber})
            </MenuItem>
          ))}
        </Select>
      </FormControl>

      <FormControl fullWidth sx={{ mb: 3 }} required disabled={submitting}>
        <InputLabel id="port-select-label">Port *</InputLabel>
        <Select
          labelId="port-select-label"
          id="port-select"
          name="portId"
          value={formData.portId}
          label="Port *"
          onChange={handleChange}
        >
          <MenuItem value="">
            <em>Sélectionner un port</em>
          </MenuItem>
          {ports.map((port) => (
            <MenuItem key={port.id} value={port.id}>
              {port.name} ({port.country})
            </MenuItem>
          ))}
        </Select>
      </FormControl>

      <TextField
        fullWidth
        label="Date et heure d'arrivée *"
        name="arrivalDate"
        type="datetime-local"
        value={formData.arrivalDate}
        onChange={handleChange}
        required
        InputLabelProps={{ shrink: true }}
        variant="outlined"
        sx={{ mb: 3 }}
        disabled={submitting}
      />

      <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2, mt: 3 }}>
        <Button
          onClick={onCancel}
          variant="outlined"
          color="error"
          disabled={submitting}
          sx={{ '&:hover': { borderColor: theme.palette.error.dark, backgroundColor: theme.palette.error.light + '10' } }}
        >
          Annuler
        </Button>
        <Button
          type="submit"
          variant="contained"
          color="black"
          disabled={submitting}
          startIcon={submitting ? <CircularProgress size={20} color="inherit" /> : null}
          sx={{ '&:hover': { backgroundColor: theme.palette.primary.dark } }}
        >
          Enregistrer les modifications
        </Button>
      </Box>
    </Box>
  );
};

export default EditEscaleForm;