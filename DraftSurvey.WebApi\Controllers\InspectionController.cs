﻿using AutoMapper;
using DraftSurvey.Application.DTOs;
using DraftSurvey.Domain.Entities;
using DraftSurvey.Domain.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace DraftSurvey.WebApi.Controllers
{
    [Authorize]
    [ApiController]
    [Route("api/[controller]")]
    public class InspectionController : ControllerBase
    {
        private readonly IInspectionRepository _repository;
        private readonly IMapper _mapper;

        public InspectionController(IInspectionRepository repository, IMapper mapper)
        {
            _repository = repository;
            _mapper = mapper;
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<InspectionDto>> GetById(Guid id)
        {
            var inspection = await _repository.GetByIdAsync(id);
            if (inspection == null) return NotFound();
            return Ok(_mapper.Map<InspectionDto>(inspection));
        }

        [HttpGet("by-escale/{escaleId}")]
        public async Task<ActionResult<IEnumerable<InspectionDto>>> GetByEscale(Guid escaleId)
        {
            var inspections = await _repository.GetByEscaleIdAsync(escaleId);
            return Ok(_mapper.Map<IEnumerable<InspectionDto>>(inspections));
        }

        [HttpPost]
        [Authorize(Roles = "Admin,TeamLead,Inspector")]
        public async Task<ActionResult<InspectionDto>> Create([FromBody] InspectionCreateDto dto)
        {
            var inspection = _mapper.Map<Inspection>(dto);

            if (dto.HoldInspections != null)
            {
                foreach (var holdDto in dto.HoldInspections)
                {
                    var hold = _mapper.Map<HoldInspection>(holdDto);
                    hold.InspectionId = inspection.Id;
                    inspection.HoldInspections.Add(hold);
                }
            }

            await _repository.AddAsync(inspection);
            await _repository.SaveChangesAsync();

            return CreatedAtAction(nameof(GetById), new { id = inspection.Id },
                _mapper.Map<InspectionDto>(inspection));
        }

        [HttpPut("{id}")]
        [Authorize(Roles = "Admin,TeamLead,Inspector")]
        public async Task<IActionResult> Update(Guid id, [FromBody] InspectionUpdateDto dto)
        {
            var inspection = await _repository.GetByIdAsync(id);
            if (inspection == null) return NotFound();

            _mapper.Map(dto, inspection);
            await _repository.UpdateAsync(inspection);
            await _repository.SaveChangesAsync();

            return NoContent();
        }

        [HttpDelete("{id}")]
        [Authorize(Roles = "Admin,TeamLead")]
        public async Task<IActionResult> Delete(Guid id)
        {
            var inspection = await _repository.GetByIdAsync(id);
            if (inspection == null) return NotFound();

            await _repository.DeleteAsync(id);
            await _repository.SaveChangesAsync();

            return NoContent();
        }

        [HttpPut("{id}/complete")]
        [Authorize(Roles = "Admin,TeamLead,Inspector")]
        public async Task<IActionResult> Complete(Guid id)
        {
            try
            {
                await _repository.CompleteInspectionAsync(id);
                return NoContent();
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpGet("{inspectionId}/holds")]
        public async Task<ActionResult<IEnumerable<HoldInspectionDto>>> GetHoldInspections(Guid inspectionId)
        {
            var holds = await _repository.GetHoldInspectionsByInspectionIdAsync(inspectionId);
            return Ok(_mapper.Map<IEnumerable<HoldInspectionDto>>(holds));
        }

        [HttpGet("holds/{holdId}")]
        public async Task<ActionResult<HoldInspectionDto>> GetHoldInspection(Guid holdId)
        {
            var hold = await _repository.GetHoldInspectionByIdAsync(holdId);
            if (hold == null) return NotFound();
            return Ok(_mapper.Map<HoldInspectionDto>(hold));
        }

        [HttpPost("{inspectionId}/holds")]
        [Authorize(Roles = "Admin,TeamLead,Inspector")]
        public async Task<ActionResult<HoldInspectionDto>> AddHoldInspection(Guid inspectionId, [FromBody] HoldInspectionCreateDto dto)
        {
            var inspection = await _repository.GetByIdAsync(inspectionId);
            if (inspection == null) return NotFound("Inspection not found.");

            var hold = _mapper.Map<HoldInspection>(dto);
            hold.InspectionId = inspectionId;

            await _repository.AddHoldInspectionAsync(hold);
            await _repository.SaveChangesAsync();

            await _repository.CompleteInspectionAsync(inspectionId);
            return CreatedAtAction(nameof(GetHoldInspection), new { holdId = hold.Id },
                _mapper.Map<HoldInspectionDto>(hold));
        }

        [HttpPut("holds/{holdId}/approve")]
        [Authorize(Roles = "Admin,TeamLead,Inspector")]
        public async Task<IActionResult> ApproveHold(Guid holdId)
        {
            var hold = await _repository.GetHoldInspectionByIdAsync(holdId);
            if (hold == null) return NotFound();

            hold.IsApproved = true;
            await _repository.UpdateHoldInspectionAsync(hold);
            await _repository.SaveChangesAsync();

            await _repository.CompleteInspectionAsync(hold.InspectionId);
            return NoContent();
        }

        [HttpPut("holds/{holdId}/reject")]
        [Authorize(Roles = "Admin,TeamLead,Inspector")]
        public async Task<IActionResult> RejectHold(Guid holdId, [FromBody] string reason)
        {
            var hold = await _repository.GetHoldInspectionByIdAsync(holdId);
            if (hold == null) return NotFound();

            hold.IsApproved = false;
            hold.RejectionReason = reason;
            await _repository.UpdateHoldInspectionAsync(hold);
            await _repository.SaveChangesAsync();

            await _repository.CompleteInspectionAsync(hold.InspectionId);
            return NoContent();
        }

        [HttpGet("{inspectionId}/reports")]
        public async Task<ActionResult<IEnumerable<InspectionReportDto>>> GetReports(Guid inspectionId)
        {
            var inspection = await _repository.GetByIdAsync(inspectionId);
            if (inspection == null) return NotFound();

            return Ok(_mapper.Map<IEnumerable<InspectionReportDto>>(inspection.InspectionReports));
        }

        [HttpPost("{inspectionId}/report")]
        [Authorize(Roles = "Admin,TeamLead,Inspector")]
        public async Task<ActionResult<InspectionReportDto>> GenerateReport(Guid inspectionId)
        {
            var inspection = await _repository.GetByIdAsync(inspectionId);
            if (inspection == null) return NotFound();

            var report = new InspectionReport
            {
                InspectionId = inspectionId,
                GeneratedDate = DateTime.UtcNow,
                ReportContent = $"Inspection Report for {inspection.InspectionDate}\n" +
                              $"Inspector: {inspection.InspectorName}\n" +
                              $"Status: {(inspection.AllHoldsApproved ? "Approved" : "Rejected")}"
            };

            await _repository.AddReportAsync(report);
            await _repository.SaveChangesAsync();

            return CreatedAtAction(nameof(GetReports), new { inspectionId },
                _mapper.Map<InspectionReportDto>(report));
        }
    }
}