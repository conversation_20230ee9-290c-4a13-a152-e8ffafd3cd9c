﻿// In DraftSurvey.Domain/Interfaces/IFreshWaterTankRepository.cs
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using DraftSurvey.Domain.Entities;

namespace DraftSurvey.Domain.Interfaces
{
    public interface IFreshWaterTankRepository
    {
        Task<FreshWaterTank> GetByIdAsync(Guid id);
        Task<IEnumerable<FreshWaterTank>> GetBySurveyIdAsync(Guid surveyId);
        Task AddAsync(FreshWaterTank tank);
        Task UpdateAsync(FreshWaterTank tank);
        Task DeleteAsync(Guid id);
        Task SaveChangesAsync();
    }
}