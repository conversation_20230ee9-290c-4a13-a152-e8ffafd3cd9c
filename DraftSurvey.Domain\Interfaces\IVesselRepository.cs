using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using DraftSurvey.Domain.Entities;

namespace DraftSurvey.Domain.Interfaces
{
    public interface IVesselRepository
    {
        Task<Vessel> GetByIdAsync(Guid id);
        Task<Vessel> GetByIMONumberAsync(string imo);
        Task<IEnumerable<Vessel>> SearchByNameAsync(string name);
        Task AddAsync(Vessel vessel);
        Task UpdateAsync(Vessel vessel);
        Task UpdateHydrostaticDataAsync(Guid vesselId, double displacementFactor, double tpc);
        Task<IEnumerable<Vessel>> GetAllAsync();
        Task DeleteAsync(Guid id);
        Task<bool> IMONumberExistsAsync(string imoNumber);
    }
}