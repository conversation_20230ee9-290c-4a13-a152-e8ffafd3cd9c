import React, { useState } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Tabs,
  Tab,
  Box,
  CircularProgress,
  Typography,
  Chip,
  Avatar,
  List,
  ListItem,
  ListItemText,
  Divider
} from '@mui/material';
import { Check, Close } from '@mui/icons-material';
import { completeSubtask, approveTask, rejectTask } from '../../api/taskAssignmentsApi';

const TaskDetailsDialog = ({ open, onClose, task, onTaskUpdated, currentUserRole }) => {
  const [activeTab, setActiveTab] = useState(0);
  const [loading, setLoading] = useState(false);

  const getTabs = () => {
    const tabs = [];
    if (task.taskType === 'Ballast') tabs.push('Ballasts');
    if (task.taskType === 'DraughtMarkReading') tabs.push('Tirants d\'eau');
    if (task.taskType === 'SeaWaterDensityStatement') tabs.push('Densité');
    if (task.taskType === 'LiquidStatement') tabs.push('Liquides');
    if (task.taskType === 'Inspection') tabs.push('Inspection');
    tabs.push('Détails Généraux');
    return tabs;
  };

  const handleApprove = async () => {
    try {
      setLoading(true);
      await approveTask(task.id);
      onTaskUpdated();
      onClose();
    } catch (error) {
      console.error('Error approving task:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleReject = async () => {
    try {
      setLoading(true);
      await rejectTask(task.id);
      onTaskUpdated();
      onClose();
    } catch (error) {
      console.error('Error rejecting task:', error);
    } finally {
      setLoading(false);
    }
  };

  const tabs = getTabs();

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        Détails de la Tâche: {task.taskType}
        <Chip 
          label={task.status} 
          color={
            task.status === 'Approved' ? 'success' :
            task.status === 'Rejected' ? 'error' :
            task.status === 'PendingValidation' ? 'warning' : 'default'
          }
          sx={{ ml: 2 }}
        />
      </DialogTitle>
      
      <DialogContent>
        <Tabs value={activeTab} onChange={(e, newValue) => setActiveTab(newValue)}>
          {tabs.map((tab, index) => (
            <Tab key={index} label={tab} />
          ))}
        </Tabs>

        <Box sx={{ p: 2 }}>
          {loading ? (
            <Box display="flex" justifyContent="center" p={4}>
              <CircularProgress />
            </Box>
          ) : (
            <>
              {activeTab === tabs.length - 1 && (
                <Box>
                  <List>
                    <ListItem>
                      <ListItemText 
                        primary="Survey" 
                        secondary={task.surveyNumber} 
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemText 
                        primary="Vessel" 
                        secondary={task.vesselName} 
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemText 
                        primary="Assigné à" 
                        secondary={
                          task.user ? (
                            <Box display="flex" alignItems="center" gap={1}>
                              <Avatar sx={{ width: 24, height: 24 }}>
                                {task.user.fullName?.charAt(0)}
                              </Avatar>
                              {task.user.fullName}
                            </Box>
                          ) : 'Non assigné'
                        } 
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemText 
                        primary="Date d'assignation" 
                        secondary={new Date(task.assignmentDate).toLocaleString()} 
                      />
                    </ListItem>
                    {task.completionDate && (
                      <ListItem>
                        <ListItemText 
                          primary="Date de complétion" 
                          secondary={new Date(task.completionDate).toLocaleString()} 
                        />
                      </ListItem>
                    )}
                    <Divider />
                    <ListItem>
                      <ListItemText 
                        primary="Notes" 
                        secondary={task.notes || 'Aucune note'} 
                      />
                    </ListItem>
                  </List>
                </Box>
              )}
              
              {/* Ici vous pouvez ajouter les autres onglets spécifiques à chaque type de tâche */}
            </>
          )}
        </Box>
      </DialogContent>

      <DialogActions>
        {task.status === 'PendingValidation' && currentUserRole === 'TeamLead' && (
          <>
            <Button 
              onClick={handleApprove} 
              color="success" 
              variant="contained"
              startIcon={<Check />}
              disabled={loading}
            >
              Approuver
            </Button>
            <Button 
              onClick={handleReject} 
              color="error" 
              variant="outlined"
              startIcon={<Close />}
              disabled={loading}
            >
              Rejeter
            </Button>
          </>
        )}
        <Button onClick={onClose}>Fermer</Button>
      </DialogActions>
    </Dialog>
  );
};

export default TaskDetailsDialog;