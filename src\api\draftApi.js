// src/api/draftApi.js
import api from './axiosConfig';

export const getSurveys = async () => {
  const response = await api.get('/surveys');
  return response.data;
};

export const getSurveyById = async (id) => {
  const response = await api.get(`/surveys/${id}`);
  return response.data;
};

export const createSurvey = async (surveyData) => {
  const response = await api.post('/surveys', surveyData);
  return response.data;
};

export const updateSurvey = async (id, surveyData) => {
  await api.put(`/surveys/${id}`, surveyData);
};

export const finalizeSurvey = async (id) => {
  await api.post(`/surveys/${id}/finalize`);
};

export const recalculateSurvey = async (id) => {
  await api.post(`/surveys/${id}/recalculate`);
};

export const getCargoWeight = async (id) => {
  const response = await api.get(`/surveys/${id}/cargoweight`);
  return response.data;
};

export const getSurveysByVessel = async (vesselId) => {
  const response = await api.get(`/surveys/vessel/${vesselId}`);
  return response.data;
};

export const getSurveysByPort = async (portId, startDate, endDate) => {
  const params = {};
  if (startDate) params.startDate = startDate;
  if (endDate) params.endDate = endDate;
  
  const response = await api.get(`/surveys/port/${portId}`, { params });
  return response.data;
};

// Méthodes pour les sous-ressources
export const addDraughtMarkReading = async (surveyId, readingData) => {
  const response = await api.post(`/surveys/${surveyId}/draughtmarkreadings`, readingData);
  return response.data;
};

export const addBallast = async (surveyId, ballastData) => {
  const response = await api.post(`/surveys/${surveyId}/ballasts`, ballastData);
  return response.data;
};

export const addFreshWaterTank = async (surveyId, tankData) => {
  const response = await api.post(`/surveys/${surveyId}/freshwatertanks`, tankData);
  return response.data;
};

export const addSeaWaterDensity = async (surveyId, densityData) => {
  const response = await api.post(`/surveys/${surveyId}/seawaterdensity`, densityData);
  return response.data;
};
export const deleteSurvey = async (id) => {
  await api.delete(`/surveys/${id}`);
};
export const createSurveyWithDetails = async (surveyData) => {
  const response = await api.post('/surveys', surveyData);
  return response.data;
};

export const updateSurveyDetails = async (id, surveyData) => {
  const response = await api.put(`/surveys/${id}`, surveyData);
  return response.data;
};

export const getSurveyDetails = async (id) => {
  const response = await api.get(`/surveys/${id}`);
  return response.data;
};
export const getDraftsByUserId = async (userId) => {
  const response = await api.get(`/surveys/by-user/${userId}`);
  return response.data;
};