// In DraftSurvey.Domain/Entities/HoldInspection.cs

using System;
// Add System.ComponentModel.DataAnnotations.Schema if you plan to use [ForeignKey] attributes here later.

namespace DraftSurvey.Domain.Entities
{
    public class HoldInspection
    {
        public Guid Id { get; set; }
        public Guid InspectionId { get; set; }
        public Inspection Inspection { get; set; } // Navigation property back to Inspection

        public int HoldNumber { get; set; }
        public string Condition { get; set; } // E.g., "Clean", "NeedsCleaning", "Damaged"
        public string Notes { get; set; }

        // CORRECTED PROPERTY NAME:
        public bool IsApproved { get; set; } // Indicates if the hold inspection is approved
        public string RejectionReason { get; set; } // Reason if not approved
    }
}