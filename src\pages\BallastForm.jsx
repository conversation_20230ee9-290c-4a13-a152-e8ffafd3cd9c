import React, { useState, useEffect } from 'react';
import { 
  Box, 
  TextField, 
  Button, 
  Typography, 
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Alert
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import DeleteIcon from '@mui/icons-material/Delete';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import { createBallast, getBallastsBySurvey, deleteBallast } from '../api/ballastsApi';

const BallastForm = ({ surveyId, onComplete }) => {
  const [ballasts, setBallasts] = useState([]);
  const [formData, setFormData] = useState({
    tankName: '',
    volume: '',
    density: '1.025',
    draftSurveyId: surveyId
  });
  const [completed, setCompleted] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchBallasts = async () => {
      try {
        const data = await getBallastsBySurvey(surveyId);
        setBallasts(data);
      } catch (err) {
        setError('Erreur lors du chargement des ballasts');
      }
    };
    fetchBallasts();
  }, [surveyId]);

  const handleChange = (e) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      const newBallast = await createBallast({
        ...formData,
        volume: parseFloat(formData.volume),
        density: parseFloat(formData.density)
      });
      setBallasts([...ballasts, newBallast]);
      setFormData({
        tankName: '',
        volume: '',
        density: '1.025',
        draftSurveyId: surveyId
      });
      setError(null);
    } catch (err) {
      setError('Erreur lors de la création du ballast');
    }
  };

  const handleDelete = async (id) => {
    try {
      await deleteBallast(id);
      setBallasts(ballasts.filter(b => b.id !== id));
      setError(null);
    } catch (err) {
      setError('Erreur lors de la suppression du ballast');
    }
  };

  const handleMarkComplete = () => {
    setCompleted(true);
    onComplete();
  };

  return (
    <Paper sx={{ p: 3, mb: 3 }}>
      <Typography variant="h6" gutterBottom>Gestion des Ballasts</Typography>
      
      {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>}

      <Box component="form" onSubmit={handleSubmit} sx={{ mb: 3 }}>
        <TextField
          name="tankName"
          label="Nom du ballast"
          value={formData.tankName}
          onChange={handleChange}
          fullWidth
          margin="normal"
          required
        />
        
        <TextField
          name="volume"
          label="Volume (m³)"
          type="number"
          value={formData.volume}
          onChange={handleChange}
          fullWidth
          margin="normal"
          required
        />
        
        <TextField
          name="density"
          label="Densité (t/m³)"
          type="number"
          value={formData.density}
          onChange={handleChange}
          fullWidth
          margin="normal"
          required
        />
        
        <Button 
          type="submit" 
          variant="contained" 
          startIcon={<AddIcon />}
          sx={{ mt: 2, mr: 2 }}
        >
          Ajouter Ballast
        </Button>
      </Box>

      <TableContainer>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Nom</TableCell>
              <TableCell>Volume (m³)</TableCell>
              <TableCell>Densité (t/m³)</TableCell>
              <TableCell>Poids (t)</TableCell>
              <TableCell>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {ballasts.map((ballast) => (
              <TableRow key={ballast.id}>
                <TableCell>{ballast.tankName}</TableCell>
                <TableCell>{ballast.volume}</TableCell>
                <TableCell>{ballast.density}</TableCell>
                <TableCell>{(ballast.volume * ballast.density).toFixed(2)}</TableCell>
                <TableCell>
                  <IconButton onClick={() => handleDelete(ballast.id)}>
                    <DeleteIcon color="error" />
                  </IconButton>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {!completed && (
        <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end' }}>
          <Button
            variant="contained"
            color="success"
            startIcon={<CheckCircleIcon />}
            onClick={handleMarkComplete}
          >
            Marquer comme Terminé
          </Button>
        </Box>
      )}

      {completed && (
        <Alert severity="success" sx={{ mt: 2 }}>
          Cette sous-tâche a été marquée comme terminée
        </Alert>
      )}
    </Paper>
  );
};

export default BallastForm;