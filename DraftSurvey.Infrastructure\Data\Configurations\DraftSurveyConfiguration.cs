using DraftSurvey.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace DraftSurvey.Infrastructure.Data.Configurations
{
    public class DraftSurveyConfiguration : IEntityTypeConfiguration<Domain.Entities.DraftSurvey>
    {
        public void Configure(EntityTypeBuilder<Domain.Entities.DraftSurvey> builder)
        {
            builder.HasKey(d => d.Id);
            builder.Property(d => d.SurveyNumber)
                .IsRequired()
                .HasMaxLength(50);

            builder.Property(d => d.Status)
                .HasConversion<string>()
                .HasMaxLength(20);

            // Relations
            builder.HasOne(d => d.Vessel)
                .WithMany(v => v.DraftSurveys)
                .HasForeignKey(d => d.VesselId);

            builder.HasOne(d => d.Escale)
                .WithMany(e => e.DraftSurveys)
                .HasForeignKey(d => d.EscaleId);
        }
    }
}