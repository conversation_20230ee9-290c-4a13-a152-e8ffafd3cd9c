using DraftSurvey.Application.DTOs;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace DraftSurvey.Application.Interfaces
{
    public interface ISurveyService
    {
        Task<Guid> CreateSurveyAsync(SurveyDto surveyDto);
        Task UpdateSurveyAsync(Guid surveyId, SurveyDto surveyDto);
        Task FinalizeSurveyAsync(Guid surveyId);
        Task<SurveyDto> GetSurveyByIdAsync(Guid id);
        Task<List<SurveyDto>> GetSurveysByEscaleAsync(Guid escaleId);
        Task RecalculateSurveyAsync(Guid surveyId);
    }

}