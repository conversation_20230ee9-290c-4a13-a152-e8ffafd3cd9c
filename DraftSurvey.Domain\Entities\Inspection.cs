// In C:\d\DraftSurveySolution\DraftSurvey.Domain\Entities\Inspection.cs

using System;
using System.Collections.Generic; // Make sure this is present
using System.Linq;

namespace DraftSurvey.Domain.Entities
{
    public class Inspection
    {
        public Guid Id { get; set; }
        public DateTime InspectionDate { get; set; }
        public string InspectorName { get; set; }
        public string Notes { get; set; }

        // Relations
        public Guid EscaleId { get; set; }
        public Escale Escale { get; set; }

        public ICollection<HoldInspection> HoldInspections { get; set; } = new List<HoldInspection>();

        // >>> IMPORTANT: Ensure this collection is present <<<
        public ICollection<InspectionReport> InspectionReports { get; set; } = new List<InspectionReport>();

        public bool AllHoldsApproved => HoldInspections?.All(h => h.IsApproved) ?? false;
    }
}