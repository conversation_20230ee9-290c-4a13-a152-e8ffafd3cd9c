﻿using DraftSurvey.Application.DTOs;
using DraftSurvey.Application.Interfaces;
using MediatR;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace DraftSurvey.Application.Features.Users.Queries
{
    public class GetUsersByPortQueryHandler
        : IRequestHandler<GetUsersByPortQuery, List<UserResponseDto>>  // Changé ici
    {
        private readonly IUserService _userService;

        public GetUsersByPortQueryHandler(IUserService userService)
        {
            _userService = userService;
        }

        public async Task<List<UserResponseDto>> Handle(  // Et ici
            GetUsersByPortQuery request,
            CancellationToken cancellationToken)
        {
            return await _userService.GetUsersByPortAsync(request.PortId);
        }
    }
}