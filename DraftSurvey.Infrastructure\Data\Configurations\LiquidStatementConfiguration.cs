﻿// Dans DraftSurvey.Infrastructure/Data/Configurations/LiquidStatementConfiguration.cs
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using DraftSurvey.Domain.Entities;

namespace DraftSurvey.Infrastructure.Data.Configurations
{
    public class LiquidStatementConfiguration : IEntityTypeConfiguration<LiquidStatement>
    {
        public void Configure(EntityTypeBuilder<LiquidStatement> builder)
        {
            builder.HasKey(ls => ls.Id);

            builder.Property(ls => ls.TankName)
                .HasMaxLength(100);

            builder.Property(ls => ls.Notes)
                .HasMaxLength(500);

            // Relations
            builder.HasOne(ls => ls.DraftSurvey)
                .WithMany(ds => ds.LiquidStatements) // Assurez-vous d'avoir ICollection<LiquidStatement> dans DraftSurvey
                .HasForeignKey(ls => ls.DraftSurveyId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.HasOne(ls => ls.Liquid)
                .WithMany(l => l.Statements)
                .HasForeignKey(ls => ls.LiquidId)
                .OnDelete(DeleteBehavior.Restrict); // Ne pas supprimer un type de liquide si des statements y sont liés

            // Types de colonnes
            builder.Property(ls => ls.Volume).HasColumnType("float");
            builder.Property(ls => ls.MeasuredDensity).HasColumnType("float");
        }
    }
}