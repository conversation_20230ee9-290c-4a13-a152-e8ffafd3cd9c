import api from './axiosConfig';

const BASE_URL = '/vessels';

// Helper function to extract and format error messages
const getErrorMessage = (error) => {
  const responseData = error.response?.data;
  const errors = responseData?.errors; // For validation errors
  const title = responseData?.title;   // For general API errors

  if (errors) {
    // If there are validation errors, flatten and join them
    return Object.values(errors).flat().join('\n');
  } else if (title) {
    // If there's a title (e.g., from ProblemDetails), use it
    return title;
  } else {
    // Fallback to generic error message
    return error.message || "An unknown error occurred";
  }
};

export const getAllVessels = async () => {
  try {
    const response = await api.get(BASE_URL);
    return response.data;
  } catch (error) {
    throw new Error(getErrorMessage(error) || "Failed to fetch vessels");
  }
};

export const getVesselById = async (id) => {
  try {
    const response = await api.get(`${BASE_URL}/${id}`);
    return response.data;
  } catch (error) {
    throw new Error(getErrorMessage(error) || `Failed to fetch vessel ${id}`);
  }
};

export const searchVesselsByName = async (name) => {
  try {
    if (!name.trim()) {
      // Return all vessels if search term is empty, as per VesselsPage.jsx logic
      const allVessels = await getAllVessels();
      return allVessels;
    }
    const response = await api.get(`${BASE_URL}/search`, {
      params: { name: name.trim() }
    });
    return response.data;
  } catch (error) {
    throw new Error(getErrorMessage(error) || "Search failed");
  }
};

export const createVessel = async (vesselData) => {
  try {
    const response = await api.post(BASE_URL, vesselData);
    return response.data;
  } catch (error) {
    throw new Error(getErrorMessage(error) || "Failed to create vessel");
  }
};

export const updateVessel = async (id, vesselData) => {
  try {
    const response = await api.put(`${BASE_URL}/${id}`, vesselData);
    return response.data;
  } catch (error) {
    // Use the helper for consistent error messages, including validation and not found
    throw new Error(getErrorMessage(error) || `Failed to update vessel with ID ${id}`);
  }
};

export const updateVesselHydrostaticData = async (id, data) => {
  try {
    const response = await api.put(`${BASE_URL}/${id}/hydrostatic`, data);
    return response.data;
  } catch (error) {
    // Use the helper for consistent error messages, including validation and not found
    throw new Error(getErrorMessage(error) || `Failed to update hydrostatic data for vessel ${id}`);
  }
};

export const deleteVessel = async (id) => {
  try {
    const response = await api.delete(`${BASE_URL}/${id}`);
    return response.data;
  } catch (error) {
    // Use the helper for consistent error messages, including not found
    throw new Error(getErrorMessage(error) || `Failed to delete vessel with ID ${id}`);
  }
};