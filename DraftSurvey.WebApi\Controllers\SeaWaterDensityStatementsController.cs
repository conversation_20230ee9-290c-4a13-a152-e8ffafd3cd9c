﻿// DraftSurvey.WebApi/Controllers/SeaWaterDensityStatementsController.cs
using AutoMapper;
using DraftSurvey.Application.DTOs;
using DraftSurvey.Domain.Entities;
using DraftSurvey.Domain.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;

namespace DraftSurvey.WebApi.Controllers
{
    [Authorize]
    [ApiController]
    [Route("api/seawaterdensitystatements")]
    public class SeaWaterDensityStatementsController : ControllerBase
    {
        private readonly ISeaWaterDensityStatementRepository _repository;
        private readonly IDraftSurveyRepository _surveyRepository; // Pour vérifier l'existence du DraftSurvey
        private readonly IMapper _mapper;
        private readonly ILogger<SeaWaterDensityStatementsController> _logger;

        public SeaWaterDensityStatementsController(
            ISeaWaterDensityStatementRepository repository,
            IDraftSurveyRepository surveyRepository,
            I<PERSON>apper mapper,
            ILogger<SeaWaterDensityStatementsController> logger)
        {
            _repository = repository;
            _surveyRepository = surveyRepository;
            _mapper = mapper;
            _logger = logger;
        }

        // GET: api/seawaterdensitystatements/{id}
        // Permet de récupérer une déclaration de densité par son ID unique
        [HttpGet("{id}")]
        [Authorize(Roles = "Admin,TeamLead,Surveyor")]
        public async Task<ActionResult<SeaWaterDensityStatementDto>> GetById(Guid id)
        {
            _logger.LogInformation($"Attempting to retrieve sea water density statement with ID: {id}");
            var densityStatement = await _repository.GetByIdAsync(id);

            if (densityStatement == null)
            {
                _logger.LogWarning($"Sea water density statement with ID: {id} not found.");
                return NotFound();
            }

            return Ok(_mapper.Map<SeaWaterDensityStatementDto>(densityStatement));
        }

        // GET: api/seawaterdensitystatements/by-survey/{surveyId}
        // Permet de récupérer la déclaration de densité associée à un DraftSurvey
        [HttpGet("by-survey/{surveyId}")]
        [Authorize(Roles = "Admin,TeamLead,Surveyor")]
        public async Task<ActionResult<SeaWaterDensityStatementDto>> GetBySurveyId(Guid surveyId)
        {
            _logger.LogInformation($"Attempting to retrieve sea water density statement for Survey ID: {surveyId}");
            var densityStatement = await _repository.GetBySurveyIdAsync(surveyId);

            if (densityStatement == null)
            {
                _logger.LogWarning($"Sea water density statement for Survey ID: {surveyId} not found.");
                return NotFound();
            }

            return Ok(_mapper.Map<SeaWaterDensityStatementDto>(densityStatement));
        }

        // POST: api/seawaterdensitystatements
        // Permet de créer une nouvelle déclaration de densité pour un DraftSurvey
        [HttpPost]
        [Authorize(Roles = "Admin,TeamLead,Surveyor")]
        public async Task<ActionResult<SeaWaterDensityStatementDto>> Create([FromBody] SeaWaterDensityStatementCreateDto dto)
        {
            _logger.LogInformation($"Attempting to create a new sea water density statement for DraftSurvey ID: {dto.DraftSurveyId}");

            // Vérifier si le DraftSurvey existe
            var survey = await _surveyRepository.GetByIdAsync(dto.DraftSurveyId);
            if (survey == null)
            {
                _logger.LogWarning($"Draft Survey with ID: {dto.DraftSurveyId} not found for sea water density statement creation.");
                return BadRequest("Draft Survey not found.");
            }

            // Vérifier si une déclaration de densité existe déjà pour ce DraftSurvey (relation One-to-One)
            var existingDensityStatement = await _repository.GetBySurveyIdAsync(dto.DraftSurveyId);
            if (existingDensityStatement != null)
            {
                _logger.LogWarning($"A sea water density statement already exists for DraftSurvey ID: {dto.DraftSurveyId}. Use PUT to update.");
                return Conflict("A sea water density statement already exists for this survey. Please use PUT to update it.");
            }

            var seaWaterDensityStatement = _mapper.Map<SeaWaterDensityStatement>(dto);
            // L'Id sera généré automatiquement par la base de données
            // seaWaterDensityStatement.Id = Guid.NewGuid(); // Pas nécessaire avec AddAsync, EF Core le gérera

            await _repository.AddAsync(seaWaterDensityStatement);
            await _repository.SaveChangesAsync(); // Sauvegarder les changements après l'ajout

            _logger.LogInformation($"Sea water density statement created successfully with ID: {seaWaterDensityStatement.Id} for DraftSurvey ID: {seaWaterDensityStatement.DraftSurveyId}");
            return CreatedAtAction(nameof(GetById), new { id = seaWaterDensityStatement.Id }, _mapper.Map<SeaWaterDensityStatementDto>(seaWaterDensityStatement));
        }

        // PUT: api/seawaterdensitystatements/{id}
        // Permet de mettre à jour une déclaration de densité existante
        [HttpPut("{id}")]
        [Authorize(Roles = "Admin,TeamLead,Surveyor")]
        public async Task<IActionResult> Update(Guid id, [FromBody] SeaWaterDensityStatementUpdateDto dto)
        {
            _logger.LogInformation($"Attempting to update sea water density statement with ID: {id}");
            var seaWaterDensityStatement = await _repository.GetByIdAsync(id);

            if (seaWaterDensityStatement == null)
            {
                _logger.LogWarning($"Sea water density statement with ID: {id} not found for update.");
                return NotFound();
            }

            _mapper.Map(dto, seaWaterDensityStatement); // Mappe les propriétés du DTO vers l'entité existante

            await _repository.UpdateAsync(seaWaterDensityStatement);
            await _repository.SaveChangesAsync(); // Sauvegarder les changements après la mise à jour

            _logger.LogInformation($"Sea water density statement with ID: {id} updated successfully.");
            return NoContent(); // 204 No Content for successful update
        }

        // DELETE: api/seawaterdensitystatements/{id}
        // Permet de supprimer une déclaration de densité
        [HttpDelete("{id}")]
        [Authorize(Roles = "Admin,TeamLead")]
        public async Task<IActionResult> Delete(Guid id)
        {
            _logger.LogInformation($"Attempting to delete sea water density statement with ID: {id}");
            var seaWaterDensityStatement = await _repository.GetByIdAsync(id);

            if (seaWaterDensityStatement == null)
            {
                _logger.LogWarning($"Sea water density statement with ID: {id} not found for deletion.");
                return NotFound();
            }

            await _repository.DeleteAsync(id);
            await _repository.SaveChangesAsync(); // Sauvegarder les changements après la suppression

            _logger.LogInformation($"Sea water density statement with ID: {id} deleted successfully.");
            return NoContent(); // 204 No Content for successful deletion
        }
    }
}