import api from './axiosConfig';

export const getInspectionsBySurvey = async (surveyId) => {
  try {
    const response = await api.get(`/inspection/by-survey/${surveyId}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching inspections:', error);
    throw error;
  }
};

export const createInspection = async (inspectionData) => {
  try {
    const response = await api.post('/inspection', inspectionData);
    return response.data;
  } catch (error) {
    console.error('Error creating inspection:', error);
    throw error;
  }
};

export const addHoldInspection = async (inspectionId, holdData) => {
  try {
    const response = await api.post(`/inspection/${inspectionId}/holds`, holdData);
    return response.data;
  } catch (error) {
    console.error('Error adding hold inspection:', error);
    throw error;
  }
};