// src/components/UserRoleDropdown.jsx
import React from "react";
import PropTypes from "prop-types";
import { Chip } from "@mui/material";

// Configuration des couleurs selon les rôles reconnus
const roleColors = {
  Admin: {
    bgColor: "#28A745", // vert
    textColor: "#ffffff"
  },
  PortAgent: {
    bgColor: "#17A2B8", // bleu turquoise
    textColor: "#ffffff"
  },
  Inspector: {
    bgColor: "#6F42C1", // violet
    textColor: "#ffffff"
  },
  Client: {
    bgColor: "#FD7E14", // orange
    textColor: "#000000"
  },
  TeamLead: {
    bgColor: "#FFC107", // jaune
    textColor: "#000000"
  },
  Agent: {
    bgColor: "#007BFF", // bleu
    textColor: "#ffffff"
  }
};

/**
 * Affiche un Chip coloré représentant un rôle utilisateur
 *
 * @param {string} role - Le nom du rôle (ex: 'Admin', 'PortAgent', etc.)
 */
const UserRoleDropdown = ({ role }) => {
  const roleConfig = roleColors[role];

  return (
    <Chip
      label={role || "Inconnu"}
      sx={{
        backgroundColor: roleConfig?.bgColor || "#6c757d", // gris par défaut
        color: roleConfig?.textColor || "#ffffff",
        fontWeight: "bold",
        minWidth: 100,
        justifyContent: "center",
        textTransform: "capitalize"
      }}
    />
  );
};

UserRoleDropdown.propTypes = {
  role: PropTypes.string
};

export default UserRoleDropdown;
