// In DraftSurvey.Infrastructure/Repositories/EscaleRepository.cs

using DraftSurvey.Domain.Entities;
using DraftSurvey.Domain.Interfaces;
using DraftSurvey.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq; // Make sure System.Linq is present
using System.Threading.Tasks;

namespace DraftSurvey.Infrastructure.Repositories
{
    public class EscaleRepository : IEscaleRepository
    {
        private readonly DraftSurveyDbContext _context;

        public EscaleRepository(DraftSurveyDbContext context)
        {
            _context = context;
        }

        public async Task<Escale> GetByIdAsync(Guid id)
        {
            return await _context.Escales
                .Include(e => e.Vessel)
                .Include(e => e.Port)
                .Include(e => e.AvisChargements) // NEW: Include AvisChargements
                .Include(e => e.Inspections)    // NEW: Include Inspections
                .FirstOrDefaultAsync(e => e.Id == id);
        }

        public async Task<IEnumerable<Escale>> GetAllAsync()
        {
            return await _context.Escales
                .Include(e => e.Vessel)
                .Include(e => e.Port)
                .Include(e => e.AvisChargements) // NEW: Include AvisChargements
                .Include(e => e.Inspections)    // NEW: Include Inspections
                .ToListAsync();
        }

        public async Task<IEnumerable<Escale>> GetActiveEscalesAsync()
        {
            return await _context.Escales
                .Include(e => e.Vessel)
                .Where(e => e.DepartureDate == null)
                .ToListAsync();
        }

        public async Task<IEnumerable<Escale>> GetByPortAndPeriodAsync(Guid portId, DateTime start, DateTime end)
        {
            return await _context.Escales
                .Include(e => e.Vessel)
                .Where(e => e.PortId == portId &&
                           e.ArrivalDate >= start &&
                           (e.DepartureDate == null || e.DepartureDate <= end))
                .ToListAsync();
        }

        public async Task AddAsync(Escale escale)
        {
            await _context.Escales.AddAsync(escale);
            // Removed SaveChangesAsync() - handled by UnitOfWork
        }

        public async Task CompleteEscaleAsync(Guid escaleId)
        {
            var escale = await GetByIdAsync(escaleId);
            if (escale == null)
                throw new InvalidOperationException("Escale not found");

            if (escale.DepartureDate != null)
                throw new InvalidOperationException("Escale already completed");

            escale.DepartureDate = DateTime.UtcNow;
            _context.Escales.Update(escale);
            // Removed SaveChangesAsync() - handled by UnitOfWork
        }

        public async Task SaveChangesAsync()
        {
            await _context.SaveChangesAsync();
        }
        public async Task<IEnumerable<Escale>> GetByUserIdAsync(Guid userId)
        {
            var escaleIds = await _context.DraftSurveys
                .Where(ds => ds.TaskAssignments.Any(ta => ta.UserId == userId))
                .Select(ds => ds.EscaleId)
                .Distinct()
                .ToListAsync();

            return await _context.Escales
                .Include(e => e.Vessel)
                .Include(e => e.Port)
                .Where(e => escaleIds.Contains(e.Id))
                .ToListAsync();
        }
    }
}