﻿// Dans DraftSurvey.Domain/Entities/SeaWaterDensityStatement.cs
using System;

namespace DraftSurvey.Domain.Entities
{
    public class SeaWaterDensityStatement
    {
        public Guid Id { get; set; }
        public Guid DraftSurveyId { get; set; }
        public DraftSurvey DraftSurvey { get; set; }
        public double DensityValue { get; set; }
        public DateTime MeasurementDate { get; set; }

        public DateTime MeasurementTime { get; set; }
        public double Density { get; set; } // En t/m³ ou kg/m³
        public double Temperature { get; set; } // En degrés Celsius
        public string MeasurementMethod { get; set; } // Ex: "Hydrometer", "Refractometer"
        public string Location { get; set; } // Ex: "Alongside", "At Anchorage", "Midship"
        public string Notes { get; set; }
        public double GetCorrectedDensity()
        {
            // Correction de densité selon la température
            return Temperature > 20
                ? Density + (Temperature - 20) * 0.0002
                : Density - (20 - Temperature) * 0.0002;
        }
    }
}