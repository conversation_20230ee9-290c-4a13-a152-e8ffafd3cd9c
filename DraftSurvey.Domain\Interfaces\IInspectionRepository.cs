using DraftSurvey.Domain.Entities;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;

public interface IInspectionRepository
{
    Task<Inspection> GetByIdAsync(Guid id);
    Task<IEnumerable<Inspection>> GetAllAsync();
    Task<IEnumerable<Inspection>> GetByEscaleIdAsync(Guid escaleId);
    Task AddAsync(Inspection inspection);
    Task UpdateAsync(Inspection inspection);
    Task DeleteAsync(Guid id);

    // Hold Inspection methods
    Task<HoldInspection> GetHoldInspectionByIdAsync(Guid id);
    Task<IEnumerable<HoldInspection>> GetHoldInspectionsByInspectionIdAsync(Guid inspectionId);
    Task AddHoldInspectionAsync(HoldInspection holdInspection);
    Task UpdateHoldInspectionAsync(HoldInspection holdInspection);
    Task DeleteHoldInspectionAsync(Guid id);

    // Report methods
    Task<InspectionReport> GetReportByIdAsync(Guid id);
    Task AddReportAsync(InspectionReport report);
    Task UpdateReportAsync(InspectionReport report);
    Task DeleteReportAsync(Guid id);

    Task<bool> AllHoldsApprovedAsync(Guid inspectionId);
    Task CompleteInspectionAsync(Guid inspectionId);
    Task SaveChangesAsync();
}