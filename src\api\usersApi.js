// src/api/usersApi.js
import api from './axiosConfig'; // chemin correct vers le fichier avec l'instance axios


export const getUsers = async () => {
  const response = await api.get('/users');
  return response.data;
};

export const getUserById = async (id) => {
  const response = await api.get(`/users/${id}`);
  return response.data;
};

export const createUser = async (userData) => {
  const response = await api.post('/users', userData);
  return response.data;
};

export const updateUser = async (id, userData) => {
  await api.put(`/users/${id}`, userData);
};

export const deleteUser = async (id) => {
  await api.delete(`/users/${id}`);
};

export const changePassword = async (id, passwordData) => {
  await api.post(`/users/${id}/change-password`, passwordData);
};

export const getUsersByRole = async (roleName) => {
  const response = await api.get(`/users/by-role/${roleName}`);
  return response.data;
};

export const getUsersByPort = async (portId) => {
  const response = await api.get(`/users/by-port/${portId}`);
  return response.data;
};