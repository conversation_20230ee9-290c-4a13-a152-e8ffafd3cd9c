﻿// Dans DraftSurvey.Domain/Entities/DraughtSurveyDocument.cs
using System;

namespace DraftSurvey.Domain.Entities
{
    public class DraughtSurveyDocument
    {
        public Guid Id { get; set; }
        public Guid DraftSurveyId { get; set; }
        public DraftSurvey DraftSurvey { get; set; }
        public Guid DocumentId { get; set; }
        public Document Document { get; set; }

        public DateTime AttachedDate { get; set; }
        public string UsageType { get; set; } // Ex: "Input Data", "Final Report", "Supporting Document"
        public string Notes { get; set; }
    }
}