﻿using AutoMapper;
using DraftSurvey.Application.Mappings;
using DraftSurvey.Infrastructure.Services.Auth;
using DraftSurvey.Domain.Interfaces;
using DraftSurvey.Infrastructure.Repositories;
using DraftSurvey.Infrastructure.Data;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using System.Text;
using DraftSurvey.Domain.Entities;

var builder = WebApplication.CreateBuilder(args);

// Configuration de base
builder.Services.AddControllers();

// Configuration AutoMapper
builder.Services.AddAutoMapper(typeof(ApplicationMappingProfile).Assembly);

// Configuration Swagger
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new OpenApiInfo
    {
        Title = "DraftSurvey API",
        Version = "v1",
        Description = "API for DraftSurvey Application",
        Contact = new OpenApiContact { Name = "Development Team" }
    });

    var securityScheme = new OpenApiSecurityScheme
    {
        Name = "JWT Authentication",
        Description = "Enter JWT Bearer token",
        In = ParameterLocation.Header,
        Type = SecuritySchemeType.Http,
        Scheme = "bearer",
        BearerFormat = "JWT",
        Reference = new OpenApiReference
        {
            Id = "Bearer",
            Type = ReferenceType.SecurityScheme
        }
    };

    c.AddSecurityDefinition(securityScheme.Reference.Id, securityScheme);
    c.AddSecurityRequirement(new OpenApiSecurityRequirement
    {
        { securityScheme, Array.Empty<string>() }
    });
});

// Configuration de la base de données
var connectionString = builder.Configuration.GetConnectionString("DefaultConnection");
builder.Services.AddDbContext<DraftSurveyDbContext>(options =>
    options.UseSqlServer(connectionString, sqlOptions =>
    {
        sqlOptions.EnableRetryOnFailure(
            maxRetryCount: 5,
            maxRetryDelay: TimeSpan.FromSeconds(30),
            errorNumbersToAdd: null);
    }));

// Configuration CORS
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowFrontend",
        builder => builder.WithOrigins("http://localhost:3000")
                          .AllowAnyMethod()
                          .AllowAnyHeader()
                          .AllowCredentials());
});

// Configuration JWT avec validation
var jwtSettings = builder.Configuration.GetSection("JwtSettings").Get<DraftSurvey.Infrastructure.Services.Auth.JwtSettings>();
if (jwtSettings == null || string.IsNullOrEmpty(jwtSettings.Secret))
{
    throw new InvalidOperationException("JWT configuration is missing or invalid");
}

builder.Services.Configure<DraftSurvey.Infrastructure.Services.Auth.JwtSettings>(builder.Configuration.GetSection("JwtSettings"));

// Enregistrement des services
builder.Services.AddScoped<IAuthService, AuthService>();
builder.Services.AddScoped<IUserRepository, UserRepository>();
builder.Services.AddScoped<IRoleRepository, RoleRepository>();
builder.Services.AddScoped<IVesselRepository, VesselRepository>();
builder.Services.AddScoped<IPortRepository, PortRepository>();
builder.Services.AddScoped<IDraftSurveyRepository, DraftSurveyRepository>();
builder.Services.AddScoped<IEscaleRepository, EscaleRepository>();
builder.Services.AddScoped<IBallastRepository, BallastRepository>();
builder.Services.AddScoped<IDraughtMarkReadingRepository, DraughtMarkReadingRepository>();
builder.Services.AddScoped<ITaskAssignmentRepository, TaskAssignmentRepository>();
builder.Services.AddScoped<ISeaWaterDensityStatementRepository, SeaWaterDensityStatementRepository>();
builder.Services.AddScoped<IDraughtMarksCorrectionRepository, DraughtMarksCorrectionRepository>();
builder.Services.AddScoped<IFreshWaterTankRepository, FreshWaterTankRepository>();
builder.Services.AddScoped<ILiquidStatementRepository, LiquidStatementRepository>();
builder.Services.AddScoped<IInspectionRepository, InspectionRepository>();

// Configuration de l'authentification
builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
    .AddJwtBearer(options =>
    {
        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuerSigningKey = true,
            IssuerSigningKey = new SymmetricSecurityKey(Encoding.ASCII.GetBytes(jwtSettings.Secret)),
            ValidateIssuer = true,
            ValidIssuer = jwtSettings.Issuer,
            ValidateAudience = true,
            ValidAudience = jwtSettings.Audience,
            ValidateLifetime = true,
            ClockSkew = TimeSpan.Zero,
            RequireExpirationTime = true
        };
    });

var app = builder.Build();

// Middleware pipeline
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "DraftSurvey API v1");
        c.RoutePrefix = string.Empty;
    });
}

app.UseHttpsRedirection();
app.UseRouting();

// Middleware CORS - doit être après UseRouting et avant UseAuthentication
app.UseCors("AllowFrontend");

app.UseAuthentication();
app.UseAuthorization();

app.MapControllers();

// Initialisation des données
using (var scope = app.Services.CreateScope())
{
    var services = scope.ServiceProvider;
    var logger = services.GetRequiredService<ILogger<Program>>();
    var dbContext = services.GetRequiredService<DraftSurveyDbContext>();

    try
    {
        logger.LogInformation("Vérification des utilisateurs système...");

        var systemUsers = new[] { "admin", "testadmin" };
        foreach (var username in systemUsers)
        {
            var user = await dbContext.Users
                .FirstOrDefaultAsync(u => u.Username == username);

            if (user != null && !user.PasswordHash.StartsWith("$2a$"))
            {
                user.SetPassword("Admin123");
                await dbContext.SaveChangesAsync();
                logger.LogInformation($"Mot de passe de {username} mis à jour");
            }
        }
    }
    catch (Exception ex)
    {
        logger.LogError(ex, "Erreur lors de l'initialisation");
    }
}

app.Run();
