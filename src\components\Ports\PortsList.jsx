import React, { useState, useEffect } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  CircularProgress,
  Alert,
  Chip,
  Box,
  Typography,
  Avatar,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  useTheme // Import useTheme
} from "@mui/material";
import { getPorts, deletePort } from "../../api/portsApi";
import EditIcon from "@mui/icons-material/Edit";
import DeleteOutlineIcon from "@mui/icons-material/DeleteOutline";
import WarningAmberIcon from "@mui/icons-material/WarningAmber";

const PortList = ({ onEdit, refreshTrigger }) => {
  const theme = useTheme(); // Access the theme

  const [ports, setPorts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const [openConfirm, setOpenConfirm] = useState(false);
  const [portToDelete, setPortToDelete] = useState(null);

  useEffect(() => {
    const fetchPorts = async () => {
      setLoading(true);
      setError(null); // Clear previous errors
      try {
        const data = await getPorts();
        setPorts(data);
      } catch (err) {
        setError("Erreur lors du chargement des ports.");
        console.error("Error fetching ports:", err);
      } finally {
        setLoading(false);
      }
    };

    fetchPorts();
  }, [refreshTrigger]); // Fetch data when refreshTrigger changes

  const handleDelete = async () => {
    if (!portToDelete) return; // Should not happen if dialog is open
    setLoading(true); // Indicate deletion is in progress
    setError(null);
    try {
      await deletePort(portToDelete);
      setPorts(prev => prev.filter(port => port.id !== portToDelete));
      handleCloseConfirm();
    } catch (err) {
      setError(err.response?.data?.message || "Erreur lors de la suppression du port.");
      console.error("Error deleting port:", err);
    } finally {
      setLoading(false); // Reset loading state
    }
  };

  const handleOpenConfirm = (id) => {
    setPortToDelete(id);
    setOpenConfirm(true);
  };

  const handleCloseConfirm = () => {
    setPortToDelete(null);
    setOpenConfirm(false);
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" py={4}>
        <CircularProgress color="primary" />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ my: 2 }}>
        {error}
      </Alert>
    );
  }

  return (
    <>
      <TableContainer component={Paper} elevation={1} sx={{
        borderRadius: theme.shape.borderRadius,
        overflow: 'hidden',
        border: `1px solid ${theme.palette.grey[200]}`,
      }}>
        <Table aria-label="port list table">
          <TableHead>
            <TableRow sx={{ bgcolor: theme.palette.grey[50] }}>
              <TableCell sx={{ fontWeight: 'bold', color: theme.palette.text.primary }}>Port</TableCell>
              <TableCell sx={{ fontWeight: 'bold', color: theme.palette.text.primary }}>Code</TableCell>
              <TableCell sx={{ fontWeight: 'bold', color: theme.palette.text.primary }}>Pays</TableCell>
              <TableCell sx={{ fontWeight: 'bold', color: theme.palette.text.primary }}>Densité</TableCell>
              <TableCell sx={{ fontWeight: 'bold', color: theme.palette.text.primary }}>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {ports.length > 0 ? (
              ports.map((port) => (
                <TableRow key={port.id} hover>
                  <TableCell>
                    <Box display="flex" alignItems="center">
                      <Avatar sx={{
                        bgcolor: theme.palette.info.light, // Using info.light for port avatars
                        color: theme.palette.info.contrastText,
                        mr: 2
                      }}>
                        {port.name?.charAt(0)?.toUpperCase()}
                      </Avatar>
                      <Box>
                        <Typography fontWeight="medium" color={theme.palette.text.primary}>{port.name}</Typography>
                        <Typography variant="body2" color={theme.palette.text.secondary}>
                          ID: {port.id}
                        </Typography>
                      </Box>
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={port.code}
                      color="primary"
                      variant="outlined"
                      sx={{ fontWeight: 'bold' }}
                    />
                  </TableCell>
                  <TableCell color={theme.palette.text.primary}>{port.country}</TableCell>
                  <TableCell color={theme.palette.text.primary}>{port.standardWaterDensity}</TableCell>
                  <TableCell>
                    <IconButton
                      onClick={() => onEdit(port.id)}
                      color="primary"
                      aria-label="edit"
                      sx={{ '&:hover': { bgcolor: theme.palette.action.hover } }}
                    >
                      <EditIcon />
                    </IconButton>
                    <IconButton
                      onClick={() => handleOpenConfirm(port.id)}
                      color="error"
                      aria-label="delete"
                      sx={{ '&:hover': { bgcolor: theme.palette.action.hover } }}
                    >
                      <DeleteOutlineIcon />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={5} align="center">
                  <Typography color="textSecondary" sx={{ py: 2 }}>Aucun port trouvé.</Typography>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Confirmation Dialog */}
      <Dialog open={openConfirm} onClose={handleCloseConfirm} maxWidth="xs" fullWidth>
        <DialogTitle sx={{
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          pt: 4,
          pb: 2,
          color: theme.palette.error.main,
          textAlign: "center"
        }}>
          <WarningAmberIcon sx={{ fontSize: 60, mb: 1 }} />
          <Typography variant="h5" component="div" fontWeight="bold">
            Confirmer la suppression
          </Typography>
        </DialogTitle>
        <DialogContent sx={{ textAlign: "center", px: 3, pb: 2 }}>
          <Typography variant="body1" color="textSecondary">
            Êtes-vous sûr de vouloir supprimer ce port ? Cette action est irréversible.
          </Typography>
        </DialogContent>
        <DialogActions sx={{ display: "flex", justifyContent: "center", pb: 3, pt: 2, borderTop: `1px solid ${theme.palette.divider}` }}>
          <Button
            onClick={handleCloseConfirm}
            variant="outlined"
            color="primary"
            sx={{ mr: 1, '&:hover': { borderColor: theme.palette.primary.dark, backgroundColor: theme.palette.primary.light + '10' } }}
          >
            Annuler
          </Button>
          <Button
            onClick={handleDelete}
            variant="contained"
            color="error"
            disabled={loading} // Disable during deletion
            startIcon={loading ? <CircularProgress size={20} color="inherit" /> : null}
            sx={{ '&:hover': { backgroundColor: theme.palette.error.dark } }}
          >
            {loading ? "Suppression..." : "Oui, supprimer !"}
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default PortList;