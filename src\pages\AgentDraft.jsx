import React, { useEffect, useState } from "react";
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  Table,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
  TableContainer,
  Paper,
  TextField,
  MenuItem,
  CssBaseline,
  Drawer,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  alpha,
  useTheme,
  useMediaQuery,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Button,
  Chip,
  CircularProgress
} from "@mui/material";
import DashboardIcon from "@mui/icons-material/Dashboard";
import DescriptionIcon from "@mui/icons-material/Description";
import SailingIcon from "@mui/icons-material/Sailing";
import TaskIcon from "@mui/icons-material/Task";
import VisibilityIcon from "@mui/icons-material/Visibility";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import CancelIcon from "@mui/icons-material/Cancel";
import Navbar from "../components/Navbar/Navbar";
import { useNavigate, useLocation } from "react-router-dom";
import { 
  getDraftsByUserId, 
  getSurveyDetails,
  finalizeSurvey,
  recalculateSurvey,
  deleteSurvey
} from "../api/draftApi";

const drawerWidth = 240;

const AgentDraft = () => {
  const [drafts, setDrafts] = useState([]);
  const [filter, setFilter] = useState("all");
  const [selectedDraft, setSelectedDraft] = useState(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [loading, setLoading] = useState(true);
  const [detailLoading, setDetailLoading] = useState(false);
  const [actionLoading, setActionLoading] = useState(false);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("md"));
  const navigate = useNavigate();
  const location = useLocation();
  const storedUser = localStorage.getItem("userData");
  const currentUser = storedUser ? JSON.parse(storedUser) : null;
  const [drawerOpen, setDrawerOpen] = useState(false);

  const toggleDrawer = () => setDrawerOpen(!drawerOpen);

  useEffect(() => {
    const fetchDrafts = async () => {
      setLoading(true);
      try {
        if (currentUser?.userId) {
          const data = await getDraftsByUserId(currentUser.userId);
          setDrafts(data);
        }
      } catch (error) {
        console.error("Error fetching drafts:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchDrafts();
  }, [currentUser?.userId]);

  const handleViewDetails = async (draftId) => {
    setDetailLoading(true);
    try {
      const draftDetails = await getSurveyDetails(draftId);
      setSelectedDraft(draftDetails);
      setOpenDialog(true);
    } catch (error) {
      console.error("Error fetching draft details:", error);
    } finally {
      setDetailLoading(false);
    }
  };

  const handleFinalize = async (draftId) => {
    setActionLoading(true);
    try {
      await finalizeSurvey(draftId);
      // Refresh the list
      const updatedDrafts = drafts.map(d => 
        d.id === draftId ? { ...d, status: "final" } : d
      );
      setDrafts(updatedDrafts);
    } catch (error) {
      console.error("Error finalizing draft:", error);
    } finally {
      setActionLoading(false);
    }
  };

  const handleRecalculate = async (draftId) => {
    setActionLoading(true);
    try {
      await recalculateSurvey(draftId);
      // Refresh the details if this draft is currently open
      if (selectedDraft?.id === draftId) {
        const updatedDetails = await getSurveyDetails(draftId);
        setSelectedDraft(updatedDetails);
      }
    } catch (error) {
      console.error("Error recalculating draft:", error);
    } finally {
      setActionLoading(false);
    }
  };

  const handleDelete = async (draftId) => {
    setActionLoading(true);
    try {
      await deleteSurvey(draftId);
      // Remove from the list
      const updatedDrafts = drafts.filter(d => d.id !== draftId);
      setDrafts(updatedDrafts);
      setOpenDialog(false);
    } catch (error) {
      console.error("Error deleting draft:", error);
    } finally {
      setActionLoading(false);
    }
  };

  const filteredDrafts =
    filter === "all" ? drafts : drafts.filter((d) => d.status === filter);

  const countByStatus = (status) =>
    drafts.filter((d) => d.status === status).length;

  const getStatusColor = (status) => {
    switch (status) {
      case "final": return "success";
      case "initial": return "warning";
      case "rejected": return "error";
      default: return "default";
    }
  };

  const drawerItems = [
    { title: "Tableau de bord", icon: <DashboardIcon />, path: "/agentdashboard" },
    { title: "Tâches", icon: <TaskIcon />, path: "/mytasks" },
    { title: "Escales", icon: <SailingIcon />, path: "/myportcalls" },
    { title: "Drafts", icon: <DescriptionIcon />, path: "/mydrafts" }
  ];

  const drawer = (
    <Box sx={{ height: "100%", display: "flex", flexDirection: "column" }}>
      <List sx={{ flexGrow: 1, py: 0 }}>
        {drawerItems.map((item, index) => (
          <ListItem
            button
            key={index}
            onClick={() => {
              navigate(item.path);
              if (isMobile) setDrawerOpen(false);
            }}
            selected={location.pathname === item.path}
            sx={{
              px: 3,
              py: 1.5,
              borderLeft: "3px solid transparent",
              color: theme.palette.text.primary,
              "&:hover": { bgcolor: theme.palette.grey[100] },
              "&.Mui-selected": {
                bgcolor: alpha(theme.palette.primary.main, 0.08),
                borderLeft: `3px solid ${theme.palette.primary.main}`,
                "& .MuiListItemIcon-root": { color: theme.palette.primary.main },
                "& .MuiListItemText-primary": {
                  fontWeight: "bold",
                  color: theme.palette.primary.main
                }
              }
            }}
          >
            <ListItemIcon sx={{ minWidth: 40 }}>{item.icon}</ListItemIcon>
            <ListItemText primary={item.title} />
          </ListItem>
        ))}
      </List>
    </Box>
  );

  return (
    <Box sx={{ display: "flex", minHeight: "100vh", bgcolor: theme.palette.grey[50] }}>
      <CssBaseline />
      <Navbar toggleDashboardDrawer={toggleDrawer} />

      <Box component="nav" sx={{ width: { md: drawerWidth }, flexShrink: { md: 0 } }}>
        <Drawer
          variant={isMobile ? "temporary" : "permanent"}
          open={isMobile ? drawerOpen : true}
          onClose={toggleDrawer}
          ModalProps={{ keepMounted: true }}
          sx={{
            "& .MuiDrawer-paper": {
              width: drawerWidth,
              boxSizing: "border-box",
              mt: { xs: "56px", sm: "64px" },
              height: { xs: "calc(100% - 56px)", sm: "calc(100% - 64px)" },
              borderRight: `1px solid ${theme.palette.divider}`
            }
          }}
        >
          {drawer}
        </Drawer>
      </Box>

      <Box
        component="main"
        sx={{
          flexGrow: 1,
          p: 3,
          width: { md: `calc(100% - ${drawerWidth}px)` },
          mt: { xs: "56px", sm: "64px" }
        }}
      >
        <Typography variant="h4" fontWeight="bold" mb={4}>
          Mes Drafts
        </Typography>

        <Grid container spacing={3} mb={4}>
          <Grid item xs={12} md={3}>
            <Card sx={{ bgcolor: "#E1F5FE" }}>
              <CardContent>
                <Typography variant="body2">Total</Typography>
                <Typography variant="h4" fontWeight="bold" color="primary.main">
                  {drafts.length}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={3}>
            <Card sx={{ bgcolor: "#FFF9C4" }}>
              <CardContent>
                <Typography variant="body2">En cours</Typography>
                <Typography variant="h4" fontWeight="bold" color="warning.main">
                  {countByStatus("initial")}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={3}>
            <Card sx={{ bgcolor: "#C8E6C9" }}>
              <CardContent>
                <Typography variant="body2">Validés</Typography>
                <Typography variant="h4" fontWeight="bold" color="success.main">
                  {countByStatus("final")}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={3}>
            <Card sx={{ bgcolor: "#FFCDD2" }}>
              <CardContent>
                <Typography variant="body2">Rejetés</Typography>
                <Typography variant="h4" fontWeight="bold" color="error.main">
                  {countByStatus("rejected")}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        <Box mb={2} width={220}>
          <TextField
            select
            label="Filtrer par statut"
            fullWidth
            value={filter}
            onChange={(e) => setFilter(e.target.value)}
            size="small"
          >
            <MenuItem value="all">Tous</MenuItem>
            <MenuItem value="initial">En cours</MenuItem>
            <MenuItem value="final">Validés</MenuItem>
            <MenuItem value="rejected">Rejetés</MenuItem>
          </TextField>
        </Box>

        {loading ? (
          <Box display="flex" justifyContent="center" py={5}>
            <CircularProgress />
          </Box>
        ) : (
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow sx={{ backgroundColor: "#F5F5F6" }}>
                  <TableCell>Référence</TableCell>
                  <TableCell>Navire</TableCell>
                  <TableCell>Escale</TableCell>
                  <TableCell>Date</TableCell>
                  <TableCell>Poids net</TableCell>
                  <TableCell>Statut</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {filteredDrafts.length > 0 ? (
                  filteredDrafts.map((draft) => (
                    <TableRow key={draft.id} hover>
                      <TableCell>{draft.surveyNumber}</TableCell>
                      <TableCell>{draft.vesselName || "—"}</TableCell>
                      <TableCell>{draft.escaleReference || "—"}</TableCell>
                      <TableCell>
                        {new Date(draft.createdDate).toLocaleDateString()}
                      </TableCell>
                      <TableCell>
                        {draft.netCargoWeight ? `${draft.netCargoWeight.toFixed(2)} t` : "—"}
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={draft.status}
                          color={getStatusColor(draft.status)}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        <IconButton 
                          onClick={() => handleViewDetails(draft.id)}
                          color="primary"
                          size="small"
                        >
                          <VisibilityIcon />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={7} align="center" sx={{ fontStyle: "italic", color: "#999" }}>
                      Aucun draft trouvé
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </TableContainer>
        )}

        {/* Dialog pour les détails du draft */}
        <Dialog 
          open={openDialog} 
          onClose={() => setOpenDialog(false)} 
          maxWidth="md" 
          fullWidth
        >
          <DialogTitle>Détails du Draft Survey</DialogTitle>
          <DialogContent>
            {detailLoading ? (
              <Box display="flex" justifyContent="center" py={5}>
                <CircularProgress />
              </Box>
            ) : selectedDraft ? (
              <>
                <Grid container spacing={2} sx={{ mb: 3 }}>
                  <Grid item xs={12} md={6}>
                    <Typography variant="subtitle1">
                      <strong>Référence:</strong> {selectedDraft.surveyNumber}
                    </Typography>
                    <Typography variant="subtitle1">
                      <strong>Statut:</strong> 
                      <Chip 
                        label={selectedDraft.status} 
                        color={getStatusColor(selectedDraft.status)}
                        sx={{ ml: 1 }}
                      />
                    </Typography>
                    <Typography variant="subtitle1">
                      <strong>Navire:</strong> {selectedDraft.vesselName || "N/A"}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Typography variant="subtitle1">
                      <strong>Escale:</strong> {selectedDraft.escaleReference || "N/A"}
                    </Typography>
                    <Typography variant="subtitle1">
                      <strong>Créé le:</strong> {new Date(selectedDraft.createdDate).toLocaleString()}
                    </Typography>
                    <Typography variant="subtitle1">
                      <strong>Poids net:</strong> {selectedDraft.netCargoWeight ? `${selectedDraft.netCargoWeight.toFixed(2)} t` : "N/A"}
                    </Typography>
                  </Grid>
                </Grid>

                {/* Lectures des tirants d'eau */}
                <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
                  Lectures des tirants d'eau
                </Typography>
                {selectedDraft.draughtMarkReading ? (
                  <TableContainer component={Paper} sx={{ mb: 3 }}>
                    <Table size="small">
                      <TableHead>
                        <TableRow>
                          <TableCell>Position</TableCell>
                          <TableCell align="right">Bâbord</TableCell>
                          <TableCell align="right">Tribord</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        <TableRow>
                          <TableCell>Avant</TableCell>
                          <TableCell align="right">{selectedDraft.draughtMarkReading.portForward}</TableCell>
                          <TableCell align="right">{selectedDraft.draughtMarkReading.starboardForward}</TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell>Milieu</TableCell>
                          <TableCell align="right">{selectedDraft.draughtMarkReading.portMidship}</TableCell>
                          <TableCell align="right">{selectedDraft.draughtMarkReading.starboardMidship}</TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell>Arrière</TableCell>
                          <TableCell align="right">{selectedDraft.draughtMarkReading.portAft}</TableCell>
                          <TableCell align="right">{selectedDraft.draughtMarkReading.starboardAft}</TableCell>
                        </TableRow>
                      </TableBody>
                    </Table>
                  </TableContainer>
                ) : (
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                    Aucune lecture de tirant d'eau disponible
                  </Typography>
                )}

                {/* Ballasts */}
                <Typography variant="h6" gutterBottom>
                  Ballasts ({selectedDraft.ballasts?.length || 0})
                </Typography>
                {selectedDraft.ballasts?.length > 0 ? (
                  <TableContainer component={Paper} sx={{ mb: 3 }}>
                    <Table size="small">
                      <TableHead>
                        <TableRow>
                          <TableCell>Nom</TableCell>
                          <TableCell align="right">Volume</TableCell>
                          <TableCell align="right">Densité</TableCell>
                          <TableCell align="right">Poids</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {selectedDraft.ballasts.map((ballast) => (
                          <TableRow key={ballast.id}>
                            <TableCell>{ballast.tankName}</TableCell>
                            <TableCell align="right">{ballast.volume}</TableCell>
                            <TableCell align="right">{ballast.density}</TableCell>
                            <TableCell align="right">{(ballast.volume * ballast.density).toFixed(2)}</TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                ) : (
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                    Aucun ballast enregistré
                  </Typography>
                )}

                {/* Résultats globaux */}
                {selectedDraft.globalCalculation && (
                  <>
                    <Typography variant="h6" gutterBottom>
                      Calculs globaux
                    </Typography>
                    <TableContainer component={Paper} sx={{ mb: 3 }}>
                      <Table size="small">
                        <TableBody>
                          <TableRow>
                            <TableCell><strong>Déplacement initial</strong></TableCell>
                            <TableCell align="right">{selectedDraft.globalCalculation.initialDisplacement.toFixed(2)}</TableCell>
                          </TableRow>
                          <TableRow>
                            <TableCell><strong>Correction de tirant d'eau 1</strong></TableCell>
                            <TableCell align="right">{selectedDraft.globalCalculation.firstTrimCorrection.toFixed(2)}</TableCell>
                          </TableRow>
                          <TableRow>
                            <TableCell><strong>Correction de tirant d'eau 2</strong></TableCell>
                            <TableCell align="right">{selectedDraft.globalCalculation.secondTrimCorrection.toFixed(2)}</TableCell>
                          </TableRow>
                          <TableRow>
                            <TableCell><strong>Poids net de la cargaison</strong></TableCell>
                            <TableCell align="right">{selectedDraft.globalCalculation.netCargoWeight.toFixed(2)}</TableCell>
                          </TableRow>
                        </TableBody>
                      </Table>
                    </TableContainer>
                  </>
                )}
              </>
            ) : (
              <Typography variant="body2" color="text.secondary">
                Impossible de charger les détails
              </Typography>
            )}
          </DialogContent>
          <DialogActions>
            {selectedDraft?.status === "initial" && (
              <>
                <Button 
                  onClick={() => handleRecalculate(selectedDraft.id)}
                  disabled={actionLoading}
                  startIcon={<CheckCircleIcon />}
                >
                  Recalculer
                </Button>
                <Button 
                  onClick={() => handleFinalize(selectedDraft.id)}
                  disabled={actionLoading}
                  color="success"
                  startIcon={<CheckCircleIcon />}
                >
                  Finaliser
                </Button>
              </>
            )}
            <Button 
              onClick={() => handleDelete(selectedDraft?.id)}
              disabled={actionLoading}
              color="error"
              startIcon={<CancelIcon />}
            >
              Supprimer
            </Button>
            <Button onClick={() => setOpenDialog(false)}>Fermer</Button>
          </DialogActions>
        </Dialog>
      </Box>
    </Box>
  );
};

export default AgentDraft;