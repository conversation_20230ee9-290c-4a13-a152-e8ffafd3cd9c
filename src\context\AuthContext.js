// src/context/AuthContext.jsx
import React, { createContext, useContext, useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  login as loginService,
  validateToken as validateTokenService,
  logout as logoutService
} from '../api/auth';

const AuthContext = createContext();

export const AuthProvider = ({ children }) => {
  const [state, setState] = useState({
    isAuthenticated: false,
    user: null,
    token: null,
    isLoading: true,
    error: null
  });

  const navigate = useNavigate();

  const validateToken = async () => {
    try {
      const isValid = await validateTokenService();
      if (!isValid) {
        logout();
      }
      return isValid;
    } catch (error) {
      logout();
      return false;
    }
  };

  useEffect(() => {
    const initializeAuth = async () => {
      try {
        const authData = JSON.parse(localStorage.getItem('auth'));
        if (authData?.token) {
          const isValid = await validateToken();
          setState({
            isAuthenticated: isValid,
            user: isValid ? authData.user : null,
            token: isValid ? authData.token : null,
            isLoading: false,
            error: null
          });
        } else {
          setState(prev => ({ ...prev, isLoading: false }));
        }
      } catch (error) {
        setState({
          isAuthenticated: false,
          user: null,
          token: null,
          isLoading: false,
          error: error.message
        });
      }
    };

    initializeAuth();
  }, []);

  const login = async (username, password) => {
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }));
      
      const authData = await loginService(username, password);
      localStorage.setItem('auth', JSON.stringify(authData));

      setState({
        isAuthenticated: true,
        user: authData.user,
        token: authData.token,
        isLoading: false,
        error: null
      });

      // Role-based redirection
      const role = authData.user.role?.name || authData.user.roleName;
      if (role === 'Admin' || role === 'TeamLead') navigate('/dashboard');
      else if (role === 'Agent') navigate('/agentdashboard');
      else navigate('/');

    } catch (error) {
      setState({
        isAuthenticated: false,
        user: null,
        token: null,
        isLoading: false,
        error: error.message
      });
      throw error;
    }
  };

  const logout = () => {
    logoutService();
    localStorage.removeItem('auth');
    setState({
      isAuthenticated: false,
      user: null,
      token: null,
      isLoading: false,
      error: null
    });
    navigate('/login');
  };

  return (
    <AuthContext.Provider value={{
      ...state,
      login,
      logout,
      validateToken
    }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};