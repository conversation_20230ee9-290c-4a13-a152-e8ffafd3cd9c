import React, { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';
import {
  getTasksByUserId,
  markTaskAsCompleted,
  submitTaskForValidation
} from '../api/taskAssignmentsApi';
import {
  Table,
  Button,
  Modal,
  Form,
  Input,
  message,
  Tag,
  Divider,
  Descriptions,
  Spin,
  Select,
  Row,
  Col,
  Card
} from 'antd';
import { FilterOutlined, ReloadOutlined, ArrowLeftOutlined } from '@ant-design/icons';
import Navbar from "../components/Navbar/Navbar";
import TaskFormSwitcher from './TaskFormSwitcher';

const { Option } = Select;

const AgentTask = () => {
  const { user } = useAuth();
  const [tasks, setTasks] = useState([]);
  const [filteredTasks, setFilteredTasks] = useState([]);
  const [selectedTask, setSelectedTask] = useState(null);
  const [isCompleteModalVisible, setIsCompleteModalVisible] = useState(false);
  const [isSubmitModalVisible, setIsSubmitModalVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [completeForm] = Form.useForm();
  const [submitForm] = Form.useForm();
  const [filterType, setFilterType] = useState(undefined);
  const [filterStatus, setFilterStatus] = useState(undefined);
  const [currentView, setCurrentView] = useState('list'); // 'list' or 'form'
  const [taskInProgress, setTaskInProgress] = useState(null);

  // Fetch tasks on user ID change
  useEffect(() => {
    if (user?.id) {
      fetchTasks(user.id);
    }
  }, [user]);

  // Apply filters whenever tasks, filterType, or filterStatus change
  useEffect(() => {
    applyFilters();
  }, [tasks, filterType, filterStatus]);

  const fetchTasks = async (userId) => {
    try {
      setLoading(true);
      const tasksData = await getTasksByUserId(userId);
      setTasks(tasksData);
    } catch (error) {
      console.error('Error fetching tasks:', error);
      message.error('Impossible de récupérer les tâches');
    } finally {
      setLoading(false);
    }
  };

  const applyFilters = () => {
    let currentFilteredTasks = tasks;

    if (filterType) {
      currentFilteredTasks = currentFilteredTasks.filter(task => task.taskType === filterType);
    }

    if (filterStatus) {
      currentFilteredTasks = currentFilteredTasks.filter(task => task.status === filterStatus);
    }
    setFilteredTasks(currentFilteredTasks);
  };

  const handleOpenTaskForm = (task) => {
    setTaskInProgress(task);
    setCurrentView('form');
  };

  const handleTaskComplete = () => {
    setCurrentView('list');
    fetchTasks(user.id);
  };

  const handleCompleteTask = (task) => {
    setSelectedTask(task);
    completeForm.resetFields();
    setIsCompleteModalVisible(true);
  };

  const handleSubmitTask = (task) => {
    setSelectedTask(task);
    submitForm.resetFields();
    setIsSubmitModalVisible(true);
  };

  const onCompleteTask = async (values) => {
    try {
      setLoading(true);
      await markTaskAsCompleted(selectedTask.id, {
        isCompleted: true,
        completionNotes: values.notes
      });
      message.success('Tâche marquée comme terminée avec succès !');
      setIsCompleteModalVisible(false);
      fetchTasks(user.id);
    } catch (error) {
      message.error('Erreur lors de la complétion de la tâche.');
    } finally {
      setLoading(false);
    }
  };

  const onSubmitTask = async (values) => {
    try {
      setLoading(true);
      await submitTaskForValidation(selectedTask.id, {
        notes: values.notes
      });
      message.success('Tâche soumise pour validation avec succès !');
      setIsSubmitModalVisible(false);
      fetchTasks(user.id);
    } catch (error) {
      message.error('Erreur lors de la soumission de la tâche.');
    } finally {
      setLoading(false);
    }
  };

  const getTaskTypeDisplay = (type) => {
    const typeMap = {
      Ballast: 'Ballast Management',
      DraughtMarkReading: 'Draught Marks Reading',
      DraughtMarksCorrection: 'Draught Marks Correction',
      DraughtSurveyGlobalCalculation: 'Global Calculation',
      FreshWaterTank: 'Fresh Water Tank',
      GlobalCalculation: 'Global Calculation',
      Inspection: 'Hold Inspection',
      LiquidStatement: 'Liquid Statement',
      SeaWaterDensityStatement: 'Sea Water Density'
    };
    return typeMap[type] || type;
  };

  const getStatusTag = (status) => {
    const statusColors = {
      Assigned: 'blue',
      InProgress: 'orange',
      Completed: 'green',
      PendingValidation: 'gold',
      Approved: 'green',
      Rejected: 'red',
      Canceled: 'gray'
    };
    return <Tag color={statusColors[status]}>{status}</Tag>;
  };

  const columns = [
    {
      title: 'Numéro de Sondage',
      dataIndex: 'surveyNumber',
      key: 'surveyNumber',
      sorter: (a, b) => a.surveyNumber.localeCompare(b.surveyNumber),
    },
    {
      title: 'Type de Tâche',
      dataIndex: 'taskType',
      key: 'taskType',
      render: (text) => getTaskTypeDisplay(text),
      sorter: (a, b) => getTaskTypeDisplay(a.taskType).localeCompare(getTaskTypeDisplay(b.taskType)),
    },
    {
      title: 'Statut',
      dataIndex: 'status',
      key: 'status',
      render: (text) => getStatusTag(text),
      sorter: (a, b) => a.status.localeCompare(b.status),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <>
          {['Assigned', 'InProgress', 'Rejected'].includes(record.status) && (
            <Button 
              type="primary" 
              onClick={() => handleOpenTaskForm(record)}
              style={{ marginRight: 8 }}
            >
              Commencer
            </Button>
          )}
          {record.status === 'Completed' && (
            <Button type="default" onClick={() => handleSubmitTask(record)}>
              Soumettre
            </Button>
          )}
        </>
      ),
    },
  ];

  // Extract unique task types and statuses for filters
  const uniqueTaskTypes = [...new Set(tasks.map(task => task.taskType))];
  const uniqueStatuses = [...new Set(tasks.map(task => task.status))];

  return (
    <div style={{ padding: '20px' }}>
      <Navbar />
      <br />
      <br />
      <br />
      
      {currentView === 'list' ? (
        <Card
          title={<h1 style={{ marginBottom: 0 }}>Mes Tâches</h1>}
          bordered={false}
          extra={
            <Button icon={<ReloadOutlined />} onClick={() => fetchTasks(user.id)} loading={loading}>
              Rafraîchir
            </Button>
          }
        >
          <Divider orientation="left">Filtres</Divider>
          <Row gutter={[16, 16]} style={{ marginBottom: 20 }}>
            <Col xs={24} sm={12} md={8}>
              <Select
                showSearch
                placeholder={<><FilterOutlined /> Type</>}
                optionFilterProp="children"
                onChange={(value) => setFilterType(value)}
                value={filterType}
                allowClear
                style={{ width: '100%' }}
              >
                {uniqueTaskTypes.map(type => (
                  <Option key={type} value={type}>{getTaskTypeDisplay(type)}</Option>
                ))}
              </Select>
            </Col>
            <Col xs={24} sm={12} md={8}>
              <Select
                showSearch
                placeholder={<><FilterOutlined /> Statut</>}
                optionFilterProp="children"
                onChange={(value) => setFilterStatus(value)}
                value={filterStatus}
                allowClear
                style={{ width: '100%' }}
              >
                {uniqueStatuses.map(status => (
                  <Option key={status} value={status}>{status}</Option>
                ))}
              </Select>
            </Col>
          </Row>

          <Spin spinning={loading && tasks.length === 0} tip="Chargement...">
            <Table
              columns={columns}
              dataSource={filteredTasks}
              rowKey="id"
              pagination={{ pageSize: 10 }}
              expandable={{
                expandedRowRender: record => (
                  <Descriptions bordered column={1} size="small" style={{ margin: '10px 0' }}>
                    <Descriptions.Item label="Date d'Affectation">
                      {new Date(record.assignmentDate).toLocaleString()}
                    </Descriptions.Item>
                    <Descriptions.Item label="Date de Complétion">
                      {record.completionDate
                        ? new Date(record.completionDate).toLocaleString()
                        : 'Non complétée'}
                    </Descriptions.Item>
                    <Descriptions.Item label="Notes">
                      {record.notes || 'Aucune note'}
                    </Descriptions.Item>
                  </Descriptions>
                ),
                rowExpandable: record => record.notes || record.completionDate,
              }}
              locale={{ emptyText: 'Aucune tâche trouvée' }}
            />
          </Spin>
        </Card>
      ) : (
        <Card
          title={
            <>
              <Button 
                icon={<ArrowLeftOutlined />} 
                onClick={() => setCurrentView('list')}
                style={{ marginRight: 16 }}
              />
              {getTaskTypeDisplay(taskInProgress?.taskType)}
            </>
          }
          bordered={false}
        >
          <TaskFormSwitcher 
            taskType={taskInProgress?.taskType} 
            surveyId={taskInProgress?.draftSurveyId}
            onComplete={handleTaskComplete}
          />
        </Card>
      )}

      {/* Modal de Complétion */}
      <Modal
        title={`Compléter Tâche : ${getTaskTypeDisplay(selectedTask?.taskType)}`}
        open={isCompleteModalVisible}
        onCancel={() => setIsCompleteModalVisible(false)}
        footer={null}
        centered
      >
        <Form form={completeForm} layout="vertical" onFinish={onCompleteTask}>
          <Form.Item
            name="notes"
            label="Remarques de complétion"
            rules={[{ required: true, message: 'Veuillez entrer des remarques.' }]}
          >
            <Input.TextArea rows={4} placeholder="Détails de la complétion..." />
          </Form.Item>
          <Form.Item style={{ textAlign: 'right', marginBottom: 0 }}>
            <Button onClick={() => setIsCompleteModalVisible(false)} style={{ marginRight: 8 }}>
              Annuler
            </Button>
            <Button type="primary" htmlType="submit" loading={loading}>
              Terminer
            </Button>
          </Form.Item>
        </Form>
      </Modal>

      {/* Modal de Soumission */}
      <Modal
        title={`Soumettre Tâche : ${getTaskTypeDisplay(selectedTask?.taskType)}`}
        open={isSubmitModalVisible}
        onCancel={() => setIsSubmitModalVisible(false)}
        footer={null}
        centered
      >
        <Form form={submitForm} layout="vertical" onFinish={onSubmitTask}>
          <Form.Item
            name="notes"
            label="Notes de soumission"
            rules={[{ required: true, message: 'Veuillez entrer des remarques.' }]}
          >
            <Input.TextArea rows={4} placeholder="Notes pour la validation..." />
          </Form.Item>
          <Form.Item style={{ textAlign: 'right', marginBottom: 0 }}>
            <Button onClick={() => setIsSubmitModalVisible(false)} style={{ marginRight: 8 }}>
              Annuler
            </Button>
            <Button type="primary" htmlType="submit" loading={loading}>
              Soumettre
            </Button>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default AgentTask;