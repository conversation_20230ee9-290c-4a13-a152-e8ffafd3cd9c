export const fetchEscale = async (escaleId) => {
    try {
      const response = await fetch(`https://example.com/api/escales/${escaleId}`);
      if (!response.ok) throw new Error("Erreur lors de la récupération de l’escale");
      const escale = await response.json();
      
      return {
        ...escale,
        drafts: escale.drafts || [], // Assurer que drafts est toujours un tableau
      };
    } catch (error) {
      console.error("Erreur API fetchEscale:", error);
      return null;
    }
  };
