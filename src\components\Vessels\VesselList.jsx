import React, { useState } from "react";
import {
  Table, TableBody, TableCell, TableContainer,
  TableHead, TableRow, Paper, IconButton,
  CircularProgress, Alert, Chip, Box, Typography,
  Avatar, Dialog, DialogTitle, DialogContent, DialogActions, Button,
  useTheme, TextField, InputAdornment
} from "@mui/material";
import EditIcon from "@mui/icons-material/Edit";
import DeleteOutlineIcon from "@mui/icons-material/DeleteOutline";
import WarningAmberIcon from "@mui/icons-material/WarningAmber";
import DirectionsBoatIcon from '@mui/icons-material/DirectionsBoat';
import SearchIcon from '@mui/icons-material/Search';
import { searchVesselsByName } from "../../api/vesselsApi";

const VesselList = ({ vessels, onEdit, onDelete, onSearchResults }) => {
  const theme = useTheme();
  const [openConfirm, setOpenConfirm] = useState(false);
  const [vesselToDelete, setVesselToDelete] = useState(null);
  const [deletionLoading, setDeletionLoading] = useState(false);
  const [deletionError, setDeletionError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');

  const handleSearch = async () => {
    try {
      const results = await searchVesselsByName(searchTerm);
      onSearchResults(results);
    } catch (error) {
      console.error("Search error:", error);
    }
  };

  const handleDeleteClick = (id) => {
    setVesselToDelete(id);
    setOpenConfirm(true);
  };

  const handleCloseConfirm = () => {
    setVesselToDelete(null);
    setOpenConfirm(false);
    setDeletionError(null);
  };

  const handleConfirmDelete = async () => {
    if (!vesselToDelete) return;
    setDeletionLoading(true);
    setDeletionError(null);
    try {
      await onDelete(vesselToDelete);
      handleCloseConfirm();
    } catch (err) {
      setDeletionError(err.response?.data?.message || err.message || "Failed to delete vessel.");
      console.error("Deletion error:", err);
    } finally {
      setDeletionLoading(false);
    }
  };

  return (
    <>
      <Box sx={{ mb: 2 }}>
        <TextField
          fullWidth
          label="Rechercher par nom"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
          InputProps={{
            endAdornment: (
              <InputAdornment position="end">
                <IconButton onClick={handleSearch}>
                  <SearchIcon />
                </IconButton>
              </InputAdornment>
            )
          }}
        />
      </Box>

      <TableContainer component={Paper} elevation={1} sx={{
        borderRadius: theme.shape.borderRadius,
        overflow: 'hidden',
        border: `1px solid ${theme.palette.grey[200]}`,
        maxHeight: 'calc(100vh - 300px)',
        overflowY: 'auto'
      }}>
        <Table stickyHeader aria-label="vessel list table">
          <TableHead>
            <TableRow sx={{ bgcolor: theme.palette.grey[50] }}>
              <TableCell sx={{ fontWeight: 'bold' }}>Navire</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>IMO</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>Type</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>Dimensions</TableCell>
              <TableCell sx={{ fontWeight: 'bold', textAlign: 'center' }}>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {vessels.length === 0 ? (
              <TableRow>
                <TableCell colSpan={5} sx={{ textAlign: 'center', py: 3 }}>
                  Aucun navire trouvé.
                </TableCell>
              </TableRow>
            ) : (
              vessels.map((vessel) => (
                <TableRow key={vessel.id} hover>
                  <TableCell>
                    <Box display="flex" alignItems="center">
                      <Avatar sx={{ bgcolor: theme.palette.primary.light, mr: 2 }}>
                        {vessel.name?.charAt(0)?.toUpperCase() || <DirectionsBoatIcon fontSize="small" />}
                      </Avatar>
                      <Box>
                        <Typography fontWeight="medium">{vessel.name}</Typography>
                        <Typography variant="body2" color="text.secondary">
                          ID: {vessel.id.substring(0, 8)}...
                        </Typography>
                      </Box>
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Chip label={vessel.imoNumber} color="primary" variant="outlined" size="small" />
                  </TableCell>
                  <TableCell>{vessel.type || 'N/A'}</TableCell>
                  <TableCell>
                    <Typography>L: {vessel.length || 'N/A'} m</Typography>
                    <Typography variant="body2" color="text.secondary">
                      W: {vessel.width || 'N/A'} m / D: {vessel.draft || 'N/A'} m
                    </Typography>
                  </TableCell>
                  <TableCell sx={{ textAlign: 'center' }}>
                    <IconButton onClick={() => onEdit(vessel)} color="primary">
                      <EditIcon />
                    </IconButton>
                    <IconButton onClick={() => handleDeleteClick(vessel.id)} color="error">
                      <DeleteOutlineIcon />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </TableContainer>

      <Dialog open={openConfirm} onClose={handleCloseConfirm}>
        <DialogTitle sx={{ textAlign: 'center' }}>
          <WarningAmberIcon color="error" sx={{ fontSize: 60, mb: 1 }} />
          <Typography variant="h5" fontWeight="bold">Confirmer la suppression</Typography>
        </DialogTitle>
        <DialogContent sx={{ textAlign: 'center' }}>
          {deletionError && <Alert severity="error" sx={{ mb: 2 }}>{deletionError}</Alert>}
          <Typography>Êtes-vous sûr de vouloir supprimer ce navire ?</Typography>
        </DialogContent>
        <DialogActions sx={{ justifyContent: 'center', pb: 3 }}>
          <Button onClick={handleCloseConfirm} variant="outlined" disabled={deletionLoading}>
            Annuler
          </Button>
          <Button 
            onClick={handleConfirmDelete} 
            variant="contained" 
            color="error"
            disabled={deletionLoading}
            startIcon={deletionLoading ? <CircularProgress size={20} /> : null}
          >
            {deletionLoading ? "Suppression..." : "Confirmer"}
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default VesselList;