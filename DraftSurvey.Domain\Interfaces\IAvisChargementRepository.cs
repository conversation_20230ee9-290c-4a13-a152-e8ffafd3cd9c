﻿// In DraftSurvey.Domain/Interfaces/IAvisChargementRepository.cs
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using DraftSurvey.Domain.Entities;

namespace DraftSurvey.Domain.Interfaces
{
    public interface IAvisChargementRepository
    {
        Task<AvisChargement> GetByIdAsync(Guid id);
        Task<IEnumerable<AvisChargement>> GetByEscaleIdAsync(Guid escaleId);
        Task AddAsync(AvisChargement avisChargement);
        Task UpdateAsync(AvisChargement avisChargement);
        Task DeleteAsync(Guid id);
        Task SaveChangesAsync();
    }
}