﻿// In DraftSurvey.Domain/Interfaces/IDraughtMarksCorrectionRepository.cs
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using DraftSurvey.Domain.Entities;

namespace DraftSurvey.Domain.Interfaces
{
    public interface IDraughtMarksCorrectionRepository
    {
        Task<DraughtMarksCorrection> GetByIdAsync(Guid id);
        Task<IEnumerable<DraughtMarksCorrection>> GetBySurveyIdAsync(Guid surveyId);
        Task AddAsync(DraughtMarksCorrection correction);
        Task UpdateAsync(DraughtMarksCorrection correction);
        Task DeleteAsync(Guid id);
        Task SaveChangesAsync();
    }
}