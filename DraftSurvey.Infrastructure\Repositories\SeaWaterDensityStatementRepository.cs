﻿// In DraftSurvey.Infrastructure/Repositories/SeaWaterDensityStatementRepository.cs
using DraftSurvey.Domain.Entities;
using DraftSurvey.Domain.Interfaces;
using DraftSurvey.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using System;
using System.Threading.Tasks;

namespace DraftSurvey.Infrastructure.Repositories
{
    public class SeaWaterDensityStatementRepository : ISeaWaterDensityStatementRepository
    {
        private readonly DraftSurveyDbContext _context;

        public SeaWaterDensityStatementRepository(DraftSurveyDbContext context)
        {
            _context = context;
        }

        // Ajoutez l'implémentation de GetByIdAsync ici
        public async Task<SeaWaterDensityStatement> GetByIdAsync(Guid id)
        {
            return await _context.SeaWaterDensityStatements.FindAsync(id);
        }

        public async Task<SeaWaterDensityStatement> GetBySurveyIdAsync(Guid surveyId)
        {
            return await _context.SeaWaterDensityStatements
                .FirstOrDefaultAsync(s => s.DraftSurveyId == surveyId);
        }

        public async Task AddAsync(SeaWaterDensityStatement statement)
        {
            await _context.SeaWaterDensityStatements.AddAsync(statement);
            // No SaveChangesAsync() here (as per your original code's comment, though it's typically called after Add/Update/Delete)
        }

        public async Task UpdateAsync(SeaWaterDensityStatement statement)
        {
            _context.SeaWaterDensityStatements.Update(statement);
            // No SaveChangesAsync() here
        }

        public async Task DeleteAsync(Guid id)
        {
            var statement = await GetByIdAsync(id); // Utilise la nouvelle méthode GetByIdAsync
            if (statement != null)
            {
                _context.SeaWaterDensityStatements.Remove(statement);
                // No SaveChangesAsync() here
            }
        }

        public async Task SaveChangesAsync()
        {
            await _context.SaveChangesAsync();
        }
    }
}