import React, { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  Typography,
  Button,
  Box,
  Avatar,
  CircularProgress,
  Chip
} from "@mui/material";
import {
  Email,
  Work,
  LocationOn,
  Edit
} from "@mui/icons-material";
import ProfileEditForm from "../components/Profile/ProfileEditForm";
import { useAuth } from "../context/AuthContext";
import Navbar from "../components/Navbar/Navbar"; // Your Navbar

const ProfilePage = () => {
  const [editModalOpen, setEditModalOpen] = useState(false);
  const { isAuthenticated, user, isLoading, validateToken } = useAuth();

  useEffect(() => {
    const checkAuth = async () => {
      if (isAuthenticated) {
        await validateToken();
      }
    };
    checkAuth();
  }, [isAuthenticated, validateToken]);

  const getPortDisplay = () => {
    if (!user) return "No port assigned";
    const portName = user.port?.name || user.portName;
    return portName ? (
      <Chip
        label={portName}
        sx={{
          backgroundColor: 'primary.light',
          color: 'white',
          fontWeight: 600,
          letterSpacing: 0.5
        }}
      />
    ) : "No port assigned";
  };

  if (isLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="80vh">
        <CircularProgress />
      </Box>
    );
  }

  if (!isAuthenticated || !user) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="80vh">
        <Typography variant="h6">Session expired. Please log in again.</Typography>
      </Box>
    );
  }

  return (
    <>
    <br /><br /><br /><br />
      <Navbar />
      <Box
      
        sx={{
          maxWidth: 800,
          mx: 'auto',
          p: 3,
          animation: 'fadeIn 0.6s ease-in-out',
          '@keyframes fadeIn': {
            '0%': { opacity: 0, transform: 'translateY(10px)' },
            '100%': { opacity: 1, transform: 'translateY(0)' },
          },
        }}
      >
        <Card
          sx={{
            borderRadius: 3,
            boxShadow: 6,
            transition: 'transform 0.2s ease-in-out',
            '&:hover': { transform: 'scale(1.01)' },
            background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
          }}
        >
          <CardContent sx={{ p: 4 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 4 }}>
              <Avatar
                sx={{
                  width: 80,
                  height: 80,
                  mr: 3,
                  background: 'linear-gradient(135deg, #6a11cb 0%, #2575fc 100%)',
                  color: '#fff',
                  fontWeight: 'bold',
                  border: '2px solid white',
                }}
              >
                {user.fullName?.charAt(0) || 'U'}
              </Avatar>
              <Box>
                <Typography variant="h5" sx={{ fontWeight: 700, color: 'primary.dark' }}>
                  {user.fullName}
                </Typography>
                <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                  {user.role?.name || "No role assigned"}
                </Typography>
              </Box>
            </Box>

            <ProfileField
              icon={<Email color="primary" />}
              label="Email"
              value={user.email}
            />

            <ProfileField
              icon={<Work color="primary" />}
              label="Role"
              value={user.role?.name || "No role assigned"}
            />

            <ProfileField
              icon={<LocationOn color="primary" />}
              label="Assigned Port"
              value={getPortDisplay()}
            />

            <Box sx={{ mt: 4, display: 'flex', justifyContent: 'flex-end' }}>
              <Button
                variant="contained"
                startIcon={<Edit />}
                onClick={() => setEditModalOpen(true)}
                sx={{
                  background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',
                  boxShadow: '0 3px 5px 2px rgba(33, 203, 243, .3)',
                  fontWeight: 'bold',
                }}
              >
                Edit Profile
              </Button>
            </Box>
          </CardContent>
        </Card>
      </Box>

      <ProfileEditForm
        open={editModalOpen}
        onClose={() => setEditModalOpen(false)}
        user={user}
        onSave={(updatedUser) => {
          setEditModalOpen(false);
        }}
      />
    </>
  );
};

const ProfileField = ({ icon, label, value }) => (
  <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
    <Box sx={{ mr: 2, color: 'primary.main' }}>{icon}</Box>
    <Box sx={{ flexGrow: 1 }}>
      <Typography variant="subtitle2" color="text.secondary">
        {label}
      </Typography>
      {typeof value === 'string' ? (
        <Typography variant="body1">{value}</Typography>
      ) : value}
    </Box>
  </Box>
);

export default ProfilePage;
