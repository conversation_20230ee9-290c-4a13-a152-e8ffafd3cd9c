import api from './axiosConfig';

export const getAllEscales = async () => {
  const response = await api.get('/escales');
  return response.data;
};

export const getActiveEscales = async () => {
  const response = await api.get('/escales/active');
  return response.data;
};

export const getEscaleById = async (id) => {
  const response = await api.get(`/escales/${id}`);
  return response.data;
};

export const createEscale = async (escaleData) => {
  const response = await api.post('/escales', escaleData);
  return response.data;
};

export const completeEscale = async (id) => {
  await api.put(`/escales/${id}/complete`);
};

export const getVessels = async () => {
  const response = await api.get('/vessels');
  return response.data;
};

export const getPorts = async () => {
  const response = await api.get('/ports');
  return response.data;
};

export const getDraftsForEscale = async (escaleId) => {
  const response = await api.get(`/escales/${escaleId}/drafts`);
  return response.data;
};
export const getVesselById = async (vesselId) => {
  const response = await api.get(`/vessels/${vesselId}`);
  return response.data;
};

export const getPortById = async (portId) => {
  const response = await api.get(`/ports/${portId}`);
  return response.data;
};
export const updateEscale = async (id, escaleData) => {
  const response = await api.put(`/escales/${id}`, escaleData);
  return response.data;
};
export const getEscalesByUserId = async (userId) => {
  const response = await api.get(`/escales/by-user/${userId}`);
  return response.data;
};