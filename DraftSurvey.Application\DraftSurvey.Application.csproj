﻿<Project Sdk="Microsoft.NET.Sdk">

  <ItemGroup>
    <ProjectReference Include="..\DraftSurvey.Domain\DraftSurvey.Domain.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="12.0.1" />
    <PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="12.0.1" />
    <PackageReference Include="FluentValidation" Version="11.11.0" />
    <PackageReference Include="MediatR" Version="12.5.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.4" />
  </ItemGroup>

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>
	<ItemGroup>
		<ProjectReference Include="..\DraftSurvey.Infrastructure\DraftSurvey.Infrastructure.csproj" />
	</ItemGroup>

</Project>
