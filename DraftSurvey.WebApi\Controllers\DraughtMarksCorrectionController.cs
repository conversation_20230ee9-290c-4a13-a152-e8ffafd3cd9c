﻿using AutoMapper;
using DraftSurvey.Application.DTOs;
using DraftSurvey.Domain.Entities;
using DraftSurvey.Domain.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace DraftSurvey.WebApi.Controllers
{
    [Authorize]
    [ApiController]
    [Route("api/[controller]")]
    public class DraughtMarksCorrectionController : ControllerBase
    {
        private readonly IDraughtMarksCorrectionRepository _repository;
        private readonly IMapper _mapper;

        public DraughtMarksCorrectionController(IDraughtMarksCorrectionRepository repository, IMapper mapper)
        {
            _repository = repository;
            _mapper = mapper;
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<DraughtMarksCorrectionDto>> GetById(Guid id)
        {
            var correction = await _repository.GetByIdAsync(id);
            if (correction == null) return NotFound();
            return Ok(_mapper.Map<DraughtMarksCorrectionDto>(correction));
        }

        [HttpGet("by-survey/{surveyId}")]
        public async Task<ActionResult<IEnumerable<DraughtMarksCorrectionDto>>> GetBySurvey(Guid surveyId)
        {
            var corrections = await _repository.GetBySurveyIdAsync(surveyId);
            return Ok(_mapper.Map<IEnumerable<DraughtMarksCorrectionDto>>(corrections));
        }

        [HttpPost]
        [Authorize(Roles = "Admin,TeamLead,Inspector")]
        public async Task<ActionResult<DraughtMarksCorrectionDto>> Create([FromBody] DraughtMarksCorrectionCreateDto dto)
        {
            var correction = new DraughtMarksCorrection
            {
                Id = Guid.NewGuid(),
                Value = dto.Value,
                CorrectionType = dto.CorrectionType,
                Unit = dto.Unit,
                AppliedToLocation = dto.AppliedToLocation,
                Notes = dto.Notes,
                CorrectionDate = dto.CorrectionDate ?? DateTime.UtcNow,
                DraftSurveyId = dto.DraftSurveyId
            };

            await _repository.AddAsync(correction);
            await _repository.SaveChangesAsync();

            var resultDto = _mapper.Map<DraughtMarksCorrectionDto>(correction);
            return CreatedAtAction(nameof(GetById), new { id = correction.Id }, resultDto);
        }

        [HttpPut("{id}")]
        [Authorize(Roles = "Admin,TeamLead,Inspector")]
        public async Task<IActionResult> Update(Guid id, [FromBody] DraughtMarksCorrectionUpdateDto dto)
        {
            var correction = await _repository.GetByIdAsync(id);
            if (correction == null) return NotFound();

            correction.Value = dto.Value;
            correction.CorrectionType = dto.CorrectionType;
            correction.Unit = dto.Unit;
            correction.AppliedToLocation = dto.AppliedToLocation;
            correction.Notes = dto.Notes;
            correction.CorrectionDate = dto.CorrectionDate ?? correction.CorrectionDate;

            await _repository.UpdateAsync(correction);
            await _repository.SaveChangesAsync();

            return NoContent();
        }

        [HttpDelete("{id}")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> Delete(Guid id)
        {
            var correction = await _repository.GetByIdAsync(id);
            if (correction == null) return NotFound();

            await _repository.DeleteAsync(id);
            await _repository.SaveChangesAsync();
            return NoContent();
        }
    }
}
