import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>alogTitle,
  DialogContent,
  TextField,
  DialogActions,
  Button,
  Alert,
  CircularProgress,
  Box,
  IconButton, // Import IconButton
  Typography, // Import Typography for DialogTitle
  useTheme // Import useTheme
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close"; // Import CloseIcon
import { createPort, updatePort, getPortById } from "../../api/portsApi";

const PortForm = ({ open, onClose, onSubmit, portId }) => {
  const theme = useTheme(); // Access the theme

  const [formData, setFormData] = useState({
    name: "",
    code: "",
    country: "",
    standardWaterDensity: "1.025"
  });
  const [error, setError] = useState(null);
  const [loading, setLoading] = useState(false); // For form submission
  const [isFetching, setIsFetching] = useState(false); // For initial data fetch

  useEffect(() => {
    const fetchPortData = async () => {
      if (portId && open) {
        setIsFetching(true);
        setError(null); // Clear previous errors
        try {
          const port = await getPortById(portId);
          setFormData({
            name: port.name || "",
            code: port.code || "",
            country: port.country || "",
            standardWaterDensity: (port.standardWaterDensity || "1.025").toString()
          });
        } catch (err) {
          setError(err.response?.data?.message || "Impossible de charger les données du port.");
          console.error("Error fetching port data:", err);
        } finally {
          setIsFetching(false);
        }
      } else {
        // Reset form for new port creation
        setFormData({
          name: "",
          code: "",
          country: "",
          standardWaterDensity: "1.025"
        });
        setError(null); // Clear error on new form open
      }
    };

    fetchPortData();
  }, [portId, open]); // Dependency array

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true); // For form submission
    setError(null);

    try {
      // Basic validation
      if (!formData.name.trim() || !formData.country.trim()) {
        throw new Error("Le nom du port et le pays sont obligatoires.");
      }
      if (!portId && !formData.code.trim()) {
        throw new Error("Le code du port est obligatoire pour un nouveau port.");
      }

      const density = parseFloat(formData.standardWaterDensity.replace(',', '.'));
      if (isNaN(density) || density < 1.0 || density > 1.1) {
        throw new Error("La densité doit être un nombre entre 1.000 et 1.100.");
      }

      if (portId) {
        await updatePort(portId, {
          name: formData.name,
          country: formData.country,
          standardWaterDensity: density
        });
      } else {
        await createPort({
          name: formData.name,
          code: formData.code,
          country: formData.country,
          standardWaterDensity: density
        });
      }

      onSubmit(); // Trigger refresh on parent
      onClose(); // Close dialog
    } catch (err) {
      setError(err.response?.data?.message || err.message || "Échec de l'opération.");
      console.error("Detailed error:", err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle sx={{
        display: "flex",
        justifyContent: "space-between",
        alignItems: "center",
        bgcolor: "black", // Use theme primary color
        color: "white",
        py: 2,
        borderBottom: `1px solid ${theme.palette.divider}`,
        boxShadow: theme.shadows[1],
      }}>
        <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
          {portId ? `Modifier le port` : "Créer un nouveau port"}
        </Typography>
        <IconButton onClick={onClose} sx={{ color: "white" }}>
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ p: 3, bgcolor: theme.palette.background.paper }}>
        {isFetching ? (
          <Box display="flex" justifyContent="center" py={4}>
            <CircularProgress color="primary" />
          </Box>
        ) : (
          <>
            {error && (
              <Alert severity="error" sx={{ mb: 3 }}>
                {error}
              </Alert>
            )}

            <Box component="form" noValidate> {/* Added noValidate to prevent browser validation popups */}
              <TextField
                margin="normal"
                fullWidth
                label="Nom du port *"
                name="name"
                value={formData.name}
                onChange={handleChange}
                required
                disabled={loading}
                variant="outlined" // Consistent outlined variant
                sx={{ mb: 2 }}
              />

              <TextField
                margin="normal"
                fullWidth
                label="Code du port *"
                name="code"
                value={formData.code}
                onChange={handleChange}
                required={!portId} // Required only for new ports
                inputProps={{ maxLength: 10 }}
                disabled={loading || !!portId} // Disabled when editing existing port
                variant="outlined"
                sx={{ mb: 2 }}
                helperText={portId ? "Le code du port ne peut pas être modifié." : ""}
              />

              <TextField
                margin="normal"
                fullWidth
                label="Pays *"
                name="country"
                value={formData.country}
                onChange={handleChange}
                required
                disabled={loading}
                variant="outlined"
                sx={{ mb: 2 }}
              />

              <TextField
                margin="normal"
                fullWidth
                label="Densité standard de l'eau"
                name="standardWaterDensity"
                value={formData.standardWaterDensity}
                onChange={handleChange}
                type="text" // Keep as text to allow user to type comma, then parse
                inputProps={{
                  inputMode: "decimal",
                  pattern: "[0-9]*[.,]?[0-9]+" // Allow comma or dot for decimal
                }}
                disabled={loading}
                variant="outlined"
                helperText="Valeur entre 1.000 et 1.100 (utiliser un point ou une virgule pour la décimale)"
              />
            </Box>
          </>
        )}
      </DialogContent>

      <DialogActions sx={{ p: 3, borderTop: `1px solid ${theme.palette.divider}` }}>
        <Button
          onClick={onClose}
          disabled={loading}
          variant="outlined"
          color="error" // Use error color for cancel
          sx={{ mr: 2, '&:hover': { borderColor: theme.palette.error.dark, backgroundColor: theme.palette.error.light + '10' } }}
        >
          Annuler
        </Button>
        <Button
          onClick={handleSubmit}
          variant="contained"
          color="black" // Use primary color for submit
          disabled={loading || isFetching}
          startIcon={loading ? <CircularProgress size={20} color="inherit" /> : null}
          sx={{ '&:hover': { backgroundColor: theme.palette.primary.dark } }}
        >
          {portId ? "Enregistrer les modifications" : "Créer le port"}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default PortForm;