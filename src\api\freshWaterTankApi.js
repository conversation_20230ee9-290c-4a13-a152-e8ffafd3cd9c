import api from './axiosConfig';

export const getTanksBySurvey = async (surveyId) => {
  try {
    const response = await api.get(`/freshwatertank/by-survey/${surveyId}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching tanks:', error);
    throw error;
  }
};

export const createTank = async (tankData) => {
  try {
    const response = await api.post('/freshwatertank', tankData);
    return response.data;
  } catch (error) {
    console.error('Error creating tank:', error);
    throw error;
  }
};

export const updateTank = async (id, tankData) => {
  try {
    await api.put(`/freshwatertank/${id}`, tankData);
  } catch (error) {
    console.error('Error updating tank:', error);
    throw error;
  }
};

export const deleteTank = async (id) => {
  try {
    await api.delete(`/freshwatertank/${id}`);
  } catch (error) {
    console.error('Error deleting tank:', error);
    throw error;
  }
};