﻿using DraftSurvey.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace DraftSurvey.Infrastructure.Data.Configurations
{
    public class DraughtMarkReadingConfiguration : IEntityTypeConfiguration<DraughtMarkReading>
    {
        public void Configure(EntityTypeBuilder<DraughtMarkReading> builder)
        {
            builder.HasKey(d => d.Id);

            // Relation avec DraftSurvey
            builder.HasOne(d => d.DraftSurvey)
                   .WithOne(d => d.DraughtMarkReading)
                   .HasForeignKey<DraughtMarkReading>(d => d.DraftSurveyId)
                   .OnDelete(DeleteBehavior.Cascade);
        }
    }
}