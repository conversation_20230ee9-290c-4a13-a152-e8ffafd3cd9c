﻿using DraftSurvey.Domain.Entities;
using DraftSurvey.Domain.Interfaces;
using DraftSurvey.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace DraftSurvey.Infrastructure.Repositories
{
    public class BallastRepository : IBallastRepository
    {
        private readonly DraftSurveyDbContext _context;

        public BallastRepository(DraftSurveyDbContext context)
        {
            _context = context;
        }

        public async Task<Ballast> GetByIdAsync(Guid id)
        {
            return await _context.Ballasts.FindAsync(id);
        }

        public async Task<IEnumerable<Ballast>> GetBySurveyIdAsync(Guid surveyId)
        {
            return await _context.Ballasts
                .Where(b => b.DraftSurveyId == surveyId)
                .ToListAsync();
        }

        public async Task AddAsync(Ballast ballast)
        {
            await _context.Ballasts.AddAsync(ballast);
        }

        public async Task UpdateAsync(Ballast ballast)
        {
            _context.Ballasts.Update(ballast);
        }

        public async Task UpdateVolumeAsync(Guid id, double volume)
        {
            var ballast = await GetByIdAsync(id);
            if (ballast != null)
            {
                ballast.Volume = volume;
                await UpdateAsync(ballast);
            }
        }

        public async Task DeleteAsync(Guid id)
        {
            var ballast = await GetByIdAsync(id);
            if (ballast != null)
            {
                _context.Ballasts.Remove(ballast);
            }
        }

        public async Task<double> GetTotalVolumeBySurveyAsync(Guid surveyId)
        {
            return await _context.Ballasts
                .Where(b => b.DraftSurveyId == surveyId)
                .SumAsync(b => b.Volume);
        }

        public async Task SaveChangesAsync()
        {
            await _context.SaveChangesAsync();
        }
    }
}