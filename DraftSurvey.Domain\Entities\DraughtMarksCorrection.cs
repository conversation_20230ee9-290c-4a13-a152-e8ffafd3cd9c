// Dans DraftSurvey.Domain/Entities/DraughtMarksCorrection.cs
using System;

namespace DraftSurvey.Domain.Entities
{
    public class DraughtMarksCorrection
    {
        public Guid Id { get; set; }
        public Guid DraftSurveyId { get; set; }
        public DraftSurvey DraftSurvey { get; set; }

        public string CorrectionType { get; set; } // Ex: "Trim", "Density", "Hogging/Sagging", "List"
        public double Value { get; set; } // Valeur de la correction en mètres/centimètres/tonnes
        public string Unit { get; set; } // Unité de la valeur
        public string AppliedToLocation { get; set; } // Ex: "Fore", "Mid", "Aft", "All"
        public DateTime CorrectionDate { get; set; }
        public string Notes { get; set; }
    }
}