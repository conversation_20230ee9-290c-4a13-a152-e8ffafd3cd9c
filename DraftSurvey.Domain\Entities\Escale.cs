using System;
using System.Collections.Generic;
using System.Linq;  // Pour .All()
namespace DraftSurvey.Domain.Entities
{
    public class Escale
    {
        public Guid Id { get; set; }
        public string Reference { get; set; }
        public DateTime ArrivalDate { get; set; }
        public DateTime? DepartureDate { get; set; }

        // Relations
        public Guid VesselId { get; set; }
        public Vessel Vessel { get; set; }

        public Guid PortId { get; set; }
        public Port Port { get; set; }

        public ICollection<DraftSurvey> DraftSurveys { get; set; } = new List<DraftSurvey>();
        public ICollection<Inspection> Inspections { get; set; } = new List<Inspection>();
        public ICollection<AvisChargement> AvisChargements { get; set; } = new List<AvisChargement>();

        public bool IsActive => DepartureDate == null;

        public void CompleteEscale()
        {
            if (DraftSurveys.All(s => s.Status == DraftSurveyStatus.Final))
            {
                DepartureDate = DateTime.UtcNow;
            }
            else
            {
                throw new InvalidOperationException("All surveys must be finalized");
            }
        }
    }
}