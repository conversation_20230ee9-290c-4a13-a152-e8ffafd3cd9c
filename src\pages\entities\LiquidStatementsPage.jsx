import React, { useState, useEffect } from 'react';
import EntityPageLayout from '../../components/Shared/EntityPageLayout';
import { Beaker, TrendingUp, TrendingDown, Thermometer, Droplets } from 'lucide-react';

const LiquidStatementsPage = () => {
  const [liquidStatements, setLiquidStatements] = useState([]);
  const [loading, setLoading] = useState(true);

  // Mock data - replace with actual API call
  useEffect(() => {
    const mockLiquidStatements = [
      {
        id: 1,
        statementNumber: "LS-2024-001",
        vesselName: "MSC OSCAR",
        tankName: "Fuel Oil Tank #1",
        liquidType: "Heavy Fuel Oil",
        volume: 1250.5,
        density: 0.985,
        temperature: 45.2,
        viscosity: 380,
        status: "Active",
        lastMeasurement: "2024-01-15T10:30:00Z",
        location: "Port Side",
        quality: "Good",
        notes: "Regular fuel consumption monitoring"
      },
      {
        id: 2,
        statementNumber: "LS-2024-002",
        vesselName: "EVER GIVEN",
        tankName: "Diesel Tank #2",
        liquidType: "Marine Gas Oil",
        volume: 850.0,
        density: 0.845,
        temperature: 25.8,
        viscosity: 5.5,
        status: "Low Level",
        lastMeasurement: "2024-01-15T09:45:00Z",
        location: "Starboard Side",
        quality: "Excellent",
        notes: "Requires refueling at next port"
      },
      {
        id: 3,
        statementNumber: "LS-2024-003",
        vesselName: "MAERSK MADRID",
        tankName: "Lubricating Oil Tank",
        liquidType: "Lubricating Oil",
        volume: 450.2,
        density: 0.920,
        temperature: 35.5,
        viscosity: 68,
        status: "Normal",
        lastMeasurement: "2024-01-15T11:15:00Z",
        location: "Engine Room",
        quality: "Good",
        notes: "Engine lubrication system operational"
      },
      {
        id: 4,
        statementNumber: "LS-2024-004",
        vesselName: "MSC OSCAR",
        tankName: "Fresh Water Tank #3",
        liquidType: "Fresh Water",
        volume: 2100.0,
        density: 1.000,
        temperature: 18.2,
        viscosity: 1.0,
        status: "Full",
        lastMeasurement: "2024-01-15T08:20:00Z",
        location: "Center Line",
        quality: "Excellent",
        notes: "Potable water supply for crew"
      },
      {
        id: 5,
        statementNumber: "LS-2024-005",
        vesselName: "EVER GIVEN",
        tankName: "Hydraulic Oil Tank",
        liquidType: "Hydraulic Oil",
        volume: 125.8,
        density: 0.875,
        temperature: 42.1,
        viscosity: 46,
        status: "Critical",
        lastMeasurement: "2024-01-15T12:00:00Z",
        location: "Deck Machinery",
        quality: "Poor",
        notes: "Oil contamination detected - immediate replacement required"
      }
    ];

    setTimeout(() => {
      setLiquidStatements(mockLiquidStatements);
      setLoading(false);
    }, 1000);
  }, []);

  const columns = [
    {
      header: 'Statement Details',
      key: 'statementNumber',
      render: (statement) => (
        <div className="flex items-center">
          <div className="flex-shrink-0 h-10 w-10">
            <div className="h-10 w-10 rounded-full bg-cyan-100 flex items-center justify-center">
              <Beaker className="h-5 w-5 text-cyan-600" />
            </div>
          </div>
          <div className="ml-4">
            <div className="text-sm font-medium text-gray-900">{statement.statementNumber}</div>
            <div className="text-sm text-gray-500">{statement.vesselName}</div>
          </div>
        </div>
      )
    },
    {
      header: 'Tank & Liquid Type',
      key: 'tank',
      render: (statement) => (
        <div>
          <div className="text-sm text-gray-900">{statement.tankName}</div>
          <div className="text-sm text-gray-500">{statement.liquidType}</div>
        </div>
      )
    },
    {
      header: 'Volume',
      key: 'volume',
      render: (statement) => (
        <div className="text-sm">
          <div className="text-gray-900 flex items-center">
            <Droplets className="h-4 w-4 text-blue-500 mr-1" />
            {statement.volume.toFixed(1)} L
          </div>
          <div className="text-gray-500">Density: {statement.density.toFixed(3)}</div>
        </div>
      )
    },
    {
      header: 'Properties',
      key: 'properties',
      render: (statement) => (
        <div className="text-sm">
          <div className="text-gray-900 flex items-center">
            <Thermometer className="h-4 w-4 text-red-500 mr-1" />
            {statement.temperature.toFixed(1)}°C
          </div>
          <div className="text-gray-500">Viscosity: {statement.viscosity}</div>
        </div>
      )
    },
    {
      header: 'Status',
      key: 'status',
      render: (statement) => {
        const statusColors = {
          'Full': 'bg-green-100 text-green-800',
          'Normal': 'bg-blue-100 text-blue-800',
          'Active': 'bg-blue-100 text-blue-800',
          'Low Level': 'bg-yellow-100 text-yellow-800',
          'Critical': 'bg-red-100 text-red-800',
          'Empty': 'bg-gray-100 text-gray-800'
        };
        return (
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusColors[statement.status] || 'bg-gray-100 text-gray-800'}`}>
            {statement.status}
          </span>
        );
      }
    },
    {
      header: 'Quality',
      key: 'quality',
      render: (statement) => {
        const qualityColors = {
          'Excellent': 'text-green-600',
          'Good': 'text-blue-600',
          'Fair': 'text-yellow-600',
          'Poor': 'text-red-600'
        };
        return (
          <div className={`text-sm font-medium ${qualityColors[statement.quality] || 'text-gray-600'}`}>
            {statement.quality}
          </div>
        );
      }
    },
    {
      header: 'Location',
      key: 'location',
      render: (statement) => (
        <div className="text-sm text-gray-900">{statement.location}</div>
      )
    },
    {
      header: 'Last Measurement',
      key: 'lastMeasurement',
      render: (statement) => (
        <div className="text-sm text-gray-500">
          {new Date(statement.lastMeasurement).toLocaleDateString()} <br />
          {new Date(statement.lastMeasurement).toLocaleTimeString()}
        </div>
      )
    }
  ];

  const handleAdd = () => {
    console.log('Add new liquid statement');
  };

  const handleEdit = (statement) => {
    console.log('Edit liquid statement:', statement);
  };

  const handleDelete = (statement) => {
    console.log('Delete liquid statement:', statement);
  };

  const handleView = (statement) => {
    console.log('View liquid statement:', statement);
  };

  const totalVolume = liquidStatements.reduce((sum, statement) => sum + statement.volume, 0);
  const criticalStatements = liquidStatements.filter(s => s.status === 'Critical').length;
  const lowLevelStatements = liquidStatements.filter(s => s.status === 'Low Level').length;
  const averageTemperature = liquidStatements.reduce((sum, s) => sum + s.temperature, 0) / liquidStatements.length || 0;

  return (
    <EntityPageLayout
      title="Liquid Statements Management"
      description="Monitor and manage liquid inventories, fuel consumption, and fluid quality across your fleet."
      data={liquidStatements}
      columns={columns}
      onAdd={handleAdd}
      onEdit={handleEdit}
      onDelete={handleDelete}
      onView={handleView}
      loading={loading}
      searchPlaceholder="Search liquid statements by tank, type, vessel..."
    >
      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Beaker className="h-8 w-8 text-cyan-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Total Statements</p>
              <p className="text-2xl font-semibold text-gray-900">{liquidStatements.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Droplets className="h-8 w-8 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Total Volume</p>
              <p className="text-2xl font-semibold text-gray-900">{totalVolume.toLocaleString()} L</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <TrendingDown className="h-8 w-8 text-yellow-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Low Level</p>
              <p className="text-2xl font-semibold text-gray-900">{lowLevelStatements}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <TrendingUp className="h-8 w-8 text-red-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Critical</p>
              <p className="text-2xl font-semibold text-gray-900">{criticalStatements}</p>
            </div>
          </div>
        </div>
      </div>
    </EntityPageLayout>
  );
};

export default LiquidStatementsPage;
