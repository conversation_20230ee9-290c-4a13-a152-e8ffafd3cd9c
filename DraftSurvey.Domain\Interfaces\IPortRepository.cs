using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using DraftSurvey.Domain.Entities;

namespace DraftSurvey.Domain.Interfaces
{
    public interface IPortRepository
    {
        Task<Port> GetByIdAsync(Guid id);
        Task<IEnumerable<Port>> GetAllAsync();
        Task AddAsync(Port port);
        Task UpdateAsync(Port port);
        Task DeleteAsync(Guid id);

        Task<int> GetEscalesCountAsync(Guid portId);
        Task<int> GetUsersCountAsync(Guid portId);
        Task UpdateWaterDensityAsync(Guid portId, double newDensity);
        Task<IEnumerable<Port>> SearchAsync(string searchTerm);
    }
}