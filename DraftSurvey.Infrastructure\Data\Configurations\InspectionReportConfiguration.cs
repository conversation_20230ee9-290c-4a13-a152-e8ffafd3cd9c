﻿// Dans DraftSurvey.Infrastructure/Data/Configurations/InspectionReportConfiguration.cs
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using DraftSurvey.Domain.Entities;

namespace DraftSurvey.Infrastructure.Data.Configurations
{
    public class InspectionReportConfiguration : IEntityTypeConfiguration<InspectionReport>
    {
        public void Configure(EntityTypeBuilder<InspectionReport> builder)
        {
            builder.HasKey(ir => ir.Id);

            builder.Property(ir => ir.ReportContent)
                .HasColumnType("nvarchar(max)"); // Peut être un texte très long ou un chemin

            builder.Property(ir => ir.ReportType)
                .HasMaxLength(100);

            builder.Property(ir => ir.Status)
                .IsRequired()
                .HasMaxLength(50);

            // Relations
            builder.HasOne(ir => ir.Inspection)
                .WithMany(i => i.InspectionReports) // Assurez-vous d'avoir ICollection<InspectionReport> dans Inspection
                .HasForeignKey(ir => ir.InspectionId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.HasOne(ir => ir.GeneratedByUser)
                .WithMany()
                .HasForeignKey(ir => ir.GeneratedByUserId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.HasOne(ir => ir.ApprovedByUser)
                .WithMany()
                .HasForeignKey(ir => ir.ApprovedByUserId)
                .IsRequired(false) // ApprovedByUserId est nullable
                .OnDelete(DeleteBehavior.Restrict);
        }
    }
}