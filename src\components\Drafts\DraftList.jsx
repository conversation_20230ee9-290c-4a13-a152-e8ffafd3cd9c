import React from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Chip,
  Typography,
  Avatar,
  Box,
  Tooltip,
  useTheme,
} from "@mui/material";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from "@mui/icons-material/Delete";
import VisibilityIcon from "@mui/icons-material/Visibility";
import AssignmentIcon from "@mui/icons-material/Assignment";
import { green, orange, blue, red } from "@mui/material/colors";

const DraftList = ({ drafts, onEdit, onDelete, onView, onAssignTasks }) => {
  const theme = useTheme();

  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case "finalized":
        return green[500];
      case "inprogress":
        return orange[500];
      case "initial":
        return blue[500];
      default:
        return red[500];
    }
  };

  const getVesselInitials = (name) => {
    if (!name) return "";
    return name
      .split(" ")
      .map((w) => w[0])
      .join("")
      .toUpperCase();
  };

  return (
    <TableContainer component={Paper} elevation={3} sx={{ mt: 2, borderRadius: theme.shape.borderRadius }}>
      <Table sx={{ minWidth: 650 }} aria-label="liste des drafts">
        <TableHead>
          <TableRow sx={{ bgcolor: theme.palette.grey[100] }}>
            <TableCell sx={{ fontWeight: "bold" }}>Numéro</TableCell>
            <TableCell sx={{ fontWeight: "bold" }}>Navire</TableCell>
            <TableCell sx={{ fontWeight: "bold" }}>Escale</TableCell>
            <TableCell sx={{ fontWeight: "bold" }}>Statut</TableCell>
            <TableCell sx={{ fontWeight: "bold" }}>Poids Cargo</TableCell>
            <TableCell sx={{ fontWeight: "bold" }}>Actions</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {drafts.map((draft) => (
            <TableRow key={draft.id} hover>
              <TableCell>
                <Typography fontWeight="medium">{draft.surveyNumber}</Typography>
                <Typography variant="body2" color="textSecondary">
                  {new Date(draft.createdDate).toLocaleDateString()}
                </Typography>
              </TableCell>
              <TableCell>
                <Box display="flex" alignItems="center">
                  <Avatar
                    sx={{
                      bgcolor: theme.palette.primary.light,
                      color: theme.palette.primary.contrastText,
                      mr: 2,
                      width: 32,
                      height: 32,
                      fontSize: "0.875rem",
                    }}
                  >
                    {getVesselInitials(draft.vesselName)}
                  </Avatar>
                  <Typography>{draft.vesselName}</Typography>
                </Box>
              </TableCell>
              <TableCell>{draft.escaleReference || "N/A"}</TableCell>
              <TableCell>
                <Chip
                  label={draft.status}
                  sx={{
                    backgroundColor: getStatusColor(draft.status),
                    color: "white",
                    minWidth: 80,
                  }}
                />
              </TableCell>
              <TableCell>
                {draft.netCargoWeight ? `${draft.netCargoWeight.toFixed(2)} t` : "N/A"}
              </TableCell>
              <TableCell>
                <Box display="flex" alignItems="center">
                  <Tooltip title="Voir détails">
                    <IconButton onClick={() => onView?.(draft)} color="primary" size="small" sx={{ mr: 1 }}>
                      <VisibilityIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="Assigner tâches">
                    <IconButton onClick={() => onAssignTasks?.(draft)} color="secondary" size="small" sx={{ mr: 1 }}>
                      <AssignmentIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="Modifier">
                    <IconButton onClick={() => onEdit?.(draft)} color="info" size="small" sx={{ mr: 1 }}>
                      <EditIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="Supprimer">
                    <IconButton onClick={() => onDelete?.(draft)} color="error" size="small">
                      <DeleteIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                </Box>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );
};

export default DraftList;
