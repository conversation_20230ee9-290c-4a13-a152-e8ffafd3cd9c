import React from "react";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableContainer, 
  TableHead, 
  TableRow, 
  Paper, 
  IconButton,
  Chip,
  Typography,
  Avatar,
  Box
} from "@mui/material";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from "@mui/icons-material/Delete";
import { blue, green, orange } from "@mui/material/colors";

const UserList = ({ users, onEdit, onDelete }) => {
  const getRoleColor = (roleName) => {
    switch(roleName?.toLowerCase()) {
      case 'admin': return blue[500];
      case 'teamlead': return orange[500];
      default: return green[500];
    }
  };

  return (
    <TableContainer component={Paper} elevation={3}>
      <Table>
        <TableHead>
          <TableRow sx={{ bgcolor: '#f5f5f5' }}>
            <TableCell sx={{ fontWeight: 'bold' }}>Utilisateur</TableCell>
            <TableCell sx={{ fontWeight: 'bold' }}>Email</TableCell>
            <TableCell sx={{ fontWeight: 'bold' }}>R<PERSON>le</TableCell>
            <TableCell sx={{ fontWeight: 'bold' }}>Port</TableCell>
            <TableCell sx={{ fontWeight: 'bold' }}>Statut</TableCell>
            <TableCell sx={{ fontWeight: 'bold' }}>Actions</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {users.map((user) => (
            <TableRow key={user.id} hover>
              <TableCell>
                <Box display="flex" alignItems="center">
                  <Avatar sx={{ bgcolor: blue[100], color: blue[800], mr: 2 }}>
                    {user.fullName?.charAt(0) || user.username?.charAt(0)}
                  </Avatar>
                  <Box>
                    <Typography fontWeight="medium">{user.fullName}</Typography>
                    <Typography variant="body2" color="textSecondary">
                      @{user.username}
                    </Typography>
                  </Box>
                </Box>
              </TableCell>
              <TableCell>{user.email}</TableCell>
              <TableCell>
                <Chip 
                  label={user.role?.name || user.roleName} 
                  sx={{ 
                    backgroundColor: getRoleColor(user.role?.name || user.roleName),
                    color: 'white'
                  }} 
                />
              </TableCell>
              <TableCell>
                {user.port?.name || 'N/A'}
              </TableCell>
              <TableCell>
                <Chip 
                  label={user.isActive ? "Actif" : "Inactif"} 
                  color={user.isActive ? "success" : "error"} 
                  size="small"
                />
              </TableCell>
              <TableCell>
                <IconButton 
                  onClick={() => onEdit(user)} 
                  color="primary"
                  aria-label="edit"
                >
                  <EditIcon />
                </IconButton>
                <IconButton 
                  onClick={() => onDelete(user.id)} 
                  color="error"
                  aria-label="delete"
                >
                  <DeleteIcon />
                </IconButton>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );
};

export default UserList;