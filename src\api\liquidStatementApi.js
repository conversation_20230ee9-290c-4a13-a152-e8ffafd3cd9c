import api from './axiosConfig';

export const getLiquidStatementsBySurvey = async (surveyId) => {
  try {
    const response = await api.get(`/LiquidStatement/by-survey/${surveyId}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching liquid statements:', error);
    throw error;
  }
};

export const getLiquidStatementById = async (id) => {
  try {
    const response = await api.get(`/LiquidStatement/${id}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching liquid statement:', error);
    throw error;
  }
};

export const createLiquidStatement = async (statementData) => {
  try {
    const response = await api.post('/LiquidStatement', statementData);
    return response.data;
  } catch (error) {
    console.error('Error creating liquid statement:', error);
    throw error;
  }
};

export const updateLiquidStatement = async (id, statementData) => {
  try {
    const response = await api.put(`/LiquidStatement/${id}`, statementData);
    return response.data;
  } catch (error) {
    console.error('Error updating liquid statement:', error);
    throw error;
  }
};

export const deleteLiquidStatement = async (id) => {
  try {
    await api.delete(`/LiquidStatement/${id}`);
  } catch (error) {
    console.error('Error deleting liquid statement:', error);
    throw error;
  }
};

export const getAvailableLiquids = async () => {
  try {
    const response = await api.get('/liquids');
    return response.data;
  } catch (error) {
    console.error('Error fetching available liquids:', error);
    throw error;
  }
};