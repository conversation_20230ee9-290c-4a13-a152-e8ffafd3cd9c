import React, { useState, useEffect } from 'react';
import { 
  Table, TableBody, TableCell, TableContainer, 
  TableHead, TableRow, Paper, Button, IconButton,
  Chip, CircularProgress, Box, Typography
} from '@mui/material';
import { Link } from 'react-router-dom';
import DownloadIcon from '@mui/icons-material/Download';
import EditIcon from '@mui/icons-material/Edit';
import { getVesselById, getPortById } from '../../api/escaleApi';

const EscaleList = ({ escales, onSelectEscale, onEditEscale, onDownloadReport }) => {
  const [details, setDetails] = useState({});
  const [loading, setLoading] = useState({});

  useEffect(() => {
    const fetchDetails = async () => {
      for (const escale of escales) {
        if (!details[escale.id]) {
          setLoading(prev => ({ ...prev, [escale.id]: true }));
          try {
            const [vessel, port] = await Promise.all([
              getVesselById(escale.vesselId),
              getPortById(escale.portId)
            ]);
            setDetails(prev => ({
              ...prev,
              [escale.id]: { vessel, port }
            }));
          } catch (error) {
            console.error(`Error fetching details for escale ${escale.id}:`, error);
          } finally {
            setLoading(prev => ({ ...prev, [escale.id]: false }));
          }
        }
      }
    };

    fetchDetails();
  }, [escales]);

  return (
    <TableContainer>
      <Table>
        <TableHead>
          <TableRow sx={{ backgroundColor: '#f8f9fa' }}>
            <TableCell sx={{ fontWeight: 'bold' }}>Référence</TableCell>
            <TableCell sx={{ fontWeight: 'bold' }}>Navire</TableCell>
            <TableCell sx={{ fontWeight: 'bold' }}>Port</TableCell>
            <TableCell sx={{ fontWeight: 'bold' }}>Date Arrivée</TableCell>
            <TableCell sx={{ fontWeight: 'bold' }}>Statut</TableCell>
            <TableCell sx={{ fontWeight: 'bold' }}>Actions</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {escales.map((escale) => (
            <TableRow 
              key={escale.id} 
              hover 
              sx={{ '&:hover': { backgroundColor: '#f8f9fa' } }}
            >
              <TableCell>
                <Button 
                  onClick={() => onSelectEscale(escale)}
                  sx={{ 
                    fontWeight: "bold", 
                    color: "#007BFF", 
                    textTransform: "none",
                    '&:hover': {
                      textDecoration: 'underline'
                    }
                  }}
                >
                  {escale.reference}
                </Button>
              </TableCell>
              <TableCell>
                {loading[escale.id] ? (
                  <CircularProgress size={20} />
                ) : (
                  details[escale.id]?.vessel?.name || 'N/A'
                )}
              </TableCell>
              <TableCell>
                {loading[escale.id] ? (
                  <CircularProgress size={20} />
                ) : (
                  details[escale.id]?.port?.name || 'N/A'
                )}
              </TableCell>
              <TableCell>
                {new Date(escale.arrivalDate).toLocaleDateString()}
              </TableCell>
              <TableCell>
                <Chip 
                  label={escale.departureDate ? "Terminée" : "Active"} 
                  color={escale.departureDate ? "default" : "success"} 
                  variant="outlined"
                />
              </TableCell>
              <TableCell>
                <IconButton 
                  onClick={() => onDownloadReport(escale)} 
                  color="primary"
                  sx={{ '&:hover': { backgroundColor: 'rgba(0, 123, 255, 0.1)' } }}
                >
                  <DownloadIcon />
                </IconButton>
                <IconButton 
                  onClick={() => onEditEscale(escale)}
                  color="secondary"
                  sx={{ '&:hover': { backgroundColor: 'rgba(108, 117, 125, 0.1)' } }}
                >
                  <EditIcon />
                </IconButton>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );
};

export default EscaleList;