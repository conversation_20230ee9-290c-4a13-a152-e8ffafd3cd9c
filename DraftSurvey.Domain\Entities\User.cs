using System;
using System.Collections.Generic;
using BCrypt.Net;

namespace DraftSurvey.Domain.Entities
{
    public class User
    {
        public Guid Id { get; set; }
        public string Username { get; set; }
        public string PasswordHash { get; set; }
        public string Email { get; set; }
        public string FullName { get; set; }
        public bool IsActive { get; set; } = true;

        // Relations
        public Guid RoleId { get; set; }
        public Role Role { get; set; }
        public Guid? PortId { get; set; }
        public Port Port { get; set; }
        public ICollection<TaskAssignment> TaskAssignments { get; set; } = new List<TaskAssignment>();

        public bool VerifyPassword(string password)
        {
            try
            {
                return BCrypt.Net.BCrypt.Verify(password, PasswordHash);
            }
            catch
            {
                return false;
            }
        }

        public void SetPassword(string password)
        {
            PasswordHash = BCrypt.Net.BCrypt.HashPassword(password, workFactor: 12);
        }

        public bool CanManageUsers() => Role?.CanManageUsers ?? false;
        public bool CanManageAllPorts() => Role?.CanManageAllPorts ?? false;
        public bool CanManagePort(Guid portId) => CanManageAllPorts() || (PortId.HasValue && PortId.Value == portId);
    }
}