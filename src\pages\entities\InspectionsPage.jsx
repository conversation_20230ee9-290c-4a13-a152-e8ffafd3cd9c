import React, { useState, useEffect } from 'react';
import EntityPageLayout from '../../components/Shared/EntityPageLayout';
import { ClipboardCheck, AlertCircle, CheckCircle, Clock, User, Calendar } from 'lucide-react';

const InspectionsPage = () => {
  const [inspections, setInspections] = useState([]);
  const [loading, setLoading] = useState(true);

  // Mock data - replace with actual API call
  useEffect(() => {
    const mockInspections = [
      {
        id: 1,
        inspectionNumber: "INS-2024-001",
        vesselName: "MSC OSCAR",
        inspectorName: "<PERSON>",
        inspectionType: "Pre-loading",
        status: "Completed",
        priority: "High",
        scheduledDate: "2024-01-15T09:00:00Z",
        completedDate: "2024-01-15T11:30:00Z",
        location: "Hold #1",
        findings: 3,
        deficiencies: 0,
        notes: "All cargo holds inspected and approved for loading",
        score: 95
      },
      {
        id: 2,
        inspectionNumber: "INS-2024-002",
        vesselName: "EVER GIVEN",
        inspectorName: "<PERSON>",
        inspectionType: "Safety",
        status: "In Progress",
        priority: "Medium",
        scheduledDate: "2024-01-15T14:00:00Z",
        completedDate: null,
        location: "Engine Room",
        findings: 1,
        deficiencies: 1,
        notes: "Safety equipment inspection ongoing",
        score: null
      },
      {
        id: 3,
        inspectionNumber: "INS-2024-003",
        vesselName: "MAERSK MADRID",
        inspectorName: "Michael Chen",
        inspectionType: "Post-discharge",
        status: "Scheduled",
        priority: "Low",
        scheduledDate: "2024-01-16T08:00:00Z",
        completedDate: null,
        location: "All Holds",
        findings: 0,
        deficiencies: 0,
        notes: "Post-discharge cleanliness inspection",
        score: null
      },
      {
        id: 4,
        inspectionNumber: "INS-2024-004",
        vesselName: "MSC OSCAR",
        inspectorName: "Emma Rodriguez",
        inspectionType: "Hull",
        status: "Completed",
        priority: "High",
        scheduledDate: "2024-01-14T10:00:00Z",
        completedDate: "2024-01-14T16:45:00Z",
        location: "Hull Exterior",
        findings: 2,
        deficiencies: 1,
        notes: "Minor paint damage detected, repair recommended",
        score: 78
      },
      {
        id: 5,
        inspectionNumber: "INS-2024-005",
        vesselName: "EVER GIVEN",
        inspectorName: "David Kim",
        inspectionType: "Environmental",
        status: "Failed",
        priority: "Critical",
        scheduledDate: "2024-01-13T13:00:00Z",
        completedDate: "2024-01-13T17:20:00Z",
        location: "Ballast System",
        findings: 5,
        deficiencies: 3,
        notes: "Ballast water treatment system requires immediate attention",
        score: 45
      }
    ];

    setTimeout(() => {
      setInspections(mockInspections);
      setLoading(false);
    }, 1000);
  }, []);

  const columns = [
    {
      header: 'Inspection Details',
      key: 'inspectionNumber',
      render: (inspection) => (
        <div className="flex items-center">
          <div className="flex-shrink-0 h-10 w-10">
            <div className="h-10 w-10 rounded-full bg-purple-100 flex items-center justify-center">
              <ClipboardCheck className="h-5 w-5 text-purple-600" />
            </div>
          </div>
          <div className="ml-4">
            <div className="text-sm font-medium text-gray-900">{inspection.inspectionNumber}</div>
            <div className="text-sm text-gray-500">{inspection.vesselName}</div>
          </div>
        </div>
      )
    },
    {
      header: 'Type & Location',
      key: 'type',
      render: (inspection) => (
        <div>
          <div className="text-sm text-gray-900">{inspection.inspectionType}</div>
          <div className="text-sm text-gray-500">{inspection.location}</div>
        </div>
      )
    },
    {
      header: 'Inspector',
      key: 'inspector',
      render: (inspection) => (
        <div className="flex items-center text-sm">
          <User className="h-4 w-4 text-gray-400 mr-2" />
          <span className="text-gray-900">{inspection.inspectorName}</span>
        </div>
      )
    },
    {
      header: 'Status',
      key: 'status',
      render: (inspection) => {
        const statusConfig = {
          'Completed': { color: 'bg-green-100 text-green-800', icon: CheckCircle },
          'In Progress': { color: 'bg-blue-100 text-blue-800', icon: Clock },
          'Scheduled': { color: 'bg-yellow-100 text-yellow-800', icon: Calendar },
          'Failed': { color: 'bg-red-100 text-red-800', icon: AlertCircle }
        };
        const config = statusConfig[inspection.status] || statusConfig['Scheduled'];
        const IconComponent = config.icon;
        
        return (
          <div className="flex items-center">
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
              <IconComponent className="w-3 h-3 mr-1" />
              {inspection.status}
            </span>
          </div>
        );
      }
    },
    {
      header: 'Priority',
      key: 'priority',
      render: (inspection) => {
        const priorityColors = {
          'Critical': 'bg-red-100 text-red-800 border-red-200',
          'High': 'bg-orange-100 text-orange-800 border-orange-200',
          'Medium': 'bg-yellow-100 text-yellow-800 border-yellow-200',
          'Low': 'bg-green-100 text-green-800 border-green-200'
        };
        return (
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${priorityColors[inspection.priority] || 'bg-gray-100 text-gray-800 border-gray-200'}`}>
            {inspection.priority}
          </span>
        );
      }
    },
    {
      header: 'Findings',
      key: 'findings',
      render: (inspection) => (
        <div className="text-sm">
          <div className="text-gray-900">Findings: {inspection.findings}</div>
          <div className="text-red-600">Deficiencies: {inspection.deficiencies}</div>
        </div>
      )
    },
    {
      header: 'Score',
      key: 'score',
      render: (inspection) => {
        if (inspection.score === null) return <span className="text-gray-400">-</span>;
        
        const scoreColor = inspection.score >= 80 ? 'text-green-600' : 
                          inspection.score >= 60 ? 'text-yellow-600' : 'text-red-600';
        
        return (
          <div className="text-center">
            <div className={`text-lg font-semibold ${scoreColor}`}>
              {inspection.score}%
            </div>
          </div>
        );
      }
    },
    {
      header: 'Scheduled Date',
      key: 'scheduledDate',
      render: (inspection) => (
        <div className="text-sm text-gray-500">
          {new Date(inspection.scheduledDate).toLocaleDateString()} <br />
          {new Date(inspection.scheduledDate).toLocaleTimeString()}
        </div>
      )
    }
  ];

  const handleAdd = () => {
    console.log('Add new inspection');
  };

  const handleEdit = (inspection) => {
    console.log('Edit inspection:', inspection);
  };

  const handleDelete = (inspection) => {
    console.log('Delete inspection:', inspection);
  };

  const handleView = (inspection) => {
    console.log('View inspection:', inspection);
  };

  const completedInspections = inspections.filter(i => i.status === 'Completed').length;
  const inProgressInspections = inspections.filter(i => i.status === 'In Progress').length;
  const failedInspections = inspections.filter(i => i.status === 'Failed').length;
  const averageScore = inspections.filter(i => i.score !== null).reduce((sum, i) => sum + i.score, 0) / 
                     inspections.filter(i => i.score !== null).length || 0;

  return (
    <EntityPageLayout
      title="Inspections Management"
      description="Track and manage vessel inspections, monitor compliance, and ensure safety standards across your fleet."
      data={inspections}
      columns={columns}
      onAdd={handleAdd}
      onEdit={handleEdit}
      onDelete={handleDelete}
      onView={handleView}
      loading={loading}
      searchPlaceholder="Search inspections by number, vessel, inspector..."
    >
      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <ClipboardCheck className="h-8 w-8 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Total Inspections</p>
              <p className="text-2xl font-semibold text-gray-900">{inspections.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Completed</p>
              <p className="text-2xl font-semibold text-gray-900">{completedInspections}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Clock className="h-8 w-8 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">In Progress</p>
              <p className="text-2xl font-semibold text-gray-900">{inProgressInspections}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <AlertCircle className="h-8 w-8 text-red-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Average Score</p>
              <p className="text-2xl font-semibold text-gray-900">{averageScore.toFixed(1)}%</p>
            </div>
          </div>
        </div>
      </div>
    </EntityPageLayout>
  );
};

export default InspectionsPage;
