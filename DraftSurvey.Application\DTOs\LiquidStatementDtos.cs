﻿// LiquidStatement DTOs
public class LiquidStatementDto
{
    public Guid Id { get; set; }
    public Guid DraftSurveyId { get; set; }
    public Guid LiquidId { get; set; }
    public double Volume { get; set; }
    public double? MeasuredDensity { get; set; }
    public double CalculatedWeight { get; set; }
    public DateTime MeasurementTime { get; set; }
    public string TankName { get; set; }
    public string Notes { get; set; }
}

public class LiquidStatementCreateDto
{
    public Guid DraftSurveyId { get; set; }
    public Guid LiquidId { get; set; }
    public double Volume { get; set; }
    public double? MeasuredDensity { get; set; }
    public DateTime MeasurementTime { get; set; }
    public string TankName { get; set; }
    public string Notes { get; set; }
}

public class LiquidStatementUpdateDto
{
    public double Volume { get; set; }
    public double? MeasuredDensity { get; set; }
    public DateTime MeasurementTime { get; set; }
    public string TankName { get; set; }
    public string Notes { get; set; }
}