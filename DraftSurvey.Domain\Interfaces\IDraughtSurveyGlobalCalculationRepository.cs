﻿// In DraftSurvey.Domain/Interfaces/IDraughtSurveyGlobalCalculationRepository.cs
using System;
using System.Threading.Tasks;
using DraftSurvey.Domain.Entities;

namespace DraftSurvey.Domain.Interfaces
{
    public interface IDraughtSurveyGlobalCalculationRepository
    {
        Task<DraughtSurveyGlobalCalculation> GetBySurveyIdAsync(Guid surveyId); // Singular relation
        Task AddAsync(DraughtSurveyGlobalCalculation calculation); // Can be added
        Task UpdateAsync(DraughtSurveyGlobalCalculation calculation); // Or updated
        Task SaveChangesAsync();
        // Delete usually handled by cascade delete from DraftSurvey or by updating the Survey
    }
}