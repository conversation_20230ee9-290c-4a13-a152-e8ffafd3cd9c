import React, { useState, useEffect } from 'react';
import EntityPageLayout from '../../components/Shared/EntityPageLayout';
import { Ship, Anchor, Calendar, MapPin, Users, Gauge } from 'lucide-react';

const VesselsPage = () => {
  const [vessels, setVessels] = useState([]);
  const [loading, setLoading] = useState(true);

  // Mock data - replace with actual API call
  useEffect(() => {
    const mockVessels = [
      {
        id: 1,
        name: "MSC OSCAR",
        imoNumber: "9349774",
        type: "Container Ship",
        flag: "Panama",
        dwt: 197362,
        loa: 395.4,
        beam: 59.0,
        draft: 16.0,
        status: "In Port",
        lastPort: "Rotterdam",
        nextPort: "Hamburg",
        eta: "2024-01-15T14:30:00Z",
        captain: "<PERSON>",
        agent: "Maritime Services Ltd"
      },
      {
        id: 2,
        name: "EVE<PERSON> GIVEN",
        imoNumber: "9811000",
        type: "Container Ship",
        flag: "Panama",
        dwt: 199629,
        loa: 400.0,
        beam: 58.8,
        draft: 15.7,
        status: "At Sea",
        lastPort: "Suez",
        nextPort: "Rotterdam",
        eta: "2024-01-18T08:00:00Z",
        captain: "Ahmed Hassan",
        agent: "Global Shipping Co"
      },
      {
        id: 3,
        name: "MAERSK MADRID",
        imoNumber: "9778693",
        type: "Container Ship",
        flag: "Denmark",
        dwt: 214286,
        loa: 399.0,
        beam: 58.6,
        draft: 16.5,
        status: "Loading",
        lastPort: "Singapore",
        nextPort: "Los Angeles",
        eta: "2024-01-20T12:00:00Z",
        captain: "Lars Nielsen",
        agent: "Maersk Line"
      }
    ];

    setTimeout(() => {
      setVessels(mockVessels);
      setLoading(false);
    }, 1000);
  }, []);

  const columns = [
    {
      header: 'Vessel Name',
      key: 'name',
      render: (vessel) => (
        <div className="flex items-center">
          <div className="flex-shrink-0 h-10 w-10">
            <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
              <Ship className="h-5 w-5 text-blue-600" />
            </div>
          </div>
          <div className="ml-4">
            <div className="text-sm font-medium text-gray-900">{vessel.name}</div>
            <div className="text-sm text-gray-500">IMO: {vessel.imoNumber}</div>
          </div>
        </div>
      )
    },
    {
      header: 'Type & Flag',
      key: 'type',
      render: (vessel) => (
        <div>
          <div className="text-sm text-gray-900">{vessel.type}</div>
          <div className="text-sm text-gray-500">{vessel.flag}</div>
        </div>
      )
    },
    {
      header: 'Specifications',
      key: 'specs',
      render: (vessel) => (
        <div className="text-sm">
          <div className="text-gray-900">DWT: {vessel.dwt.toLocaleString()} t</div>
          <div className="text-gray-500">LOA: {vessel.loa}m × {vessel.beam}m</div>
        </div>
      )
    },
    {
      header: 'Status',
      key: 'status',
      render: (vessel) => {
        const statusColors = {
          'In Port': 'bg-green-100 text-green-800',
          'At Sea': 'bg-blue-100 text-blue-800',
          'Loading': 'bg-yellow-100 text-yellow-800',
          'Anchored': 'bg-gray-100 text-gray-800'
        };
        return (
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusColors[vessel.status] || 'bg-gray-100 text-gray-800'}`}>
            {vessel.status}
          </span>
        );
      }
    },
    {
      header: 'Route',
      key: 'route',
      render: (vessel) => (
        <div className="text-sm">
          <div className="text-gray-900 flex items-center">
            <MapPin className="h-3 w-3 mr-1 text-gray-400" />
            {vessel.lastPort} → {vessel.nextPort}
          </div>
          <div className="text-gray-500 flex items-center mt-1">
            <Calendar className="h-3 w-3 mr-1 text-gray-400" />
            ETA: {new Date(vessel.eta).toLocaleDateString()}
          </div>
        </div>
      )
    },
    {
      header: 'Captain',
      key: 'captain',
      render: (vessel) => (
        <div className="text-sm">
          <div className="text-gray-900 flex items-center">
            <Users className="h-3 w-3 mr-1 text-gray-400" />
            {vessel.captain}
          </div>
          <div className="text-gray-500">{vessel.agent}</div>
        </div>
      )
    }
  ];

  const handleAdd = () => {
    console.log('Add new vessel');
  };

  const handleEdit = (vessel) => {
    console.log('Edit vessel:', vessel);
  };

  const handleDelete = (vessel) => {
    console.log('Delete vessel:', vessel);
  };

  const handleView = (vessel) => {
    console.log('View vessel:', vessel);
  };

  return (
    <EntityPageLayout
      title="Vessels Management"
      description="Manage and monitor all vessels in your fleet with comprehensive tracking and detailed specifications."
      data={vessels}
      columns={columns}
      onAdd={handleAdd}
      onEdit={handleEdit}
      onDelete={handleDelete}
      onView={handleView}
      loading={loading}
      searchPlaceholder="Search vessels by name, IMO, type..."
    >
      {/* Custom content can be added here if needed */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Ship className="h-8 w-8 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Total Vessels</p>
              <p className="text-2xl font-semibold text-gray-900">{vessels.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Anchor className="h-8 w-8 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">In Port</p>
              <p className="text-2xl font-semibold text-gray-900">
                {vessels.filter(v => v.status === 'In Port').length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Gauge className="h-8 w-8 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">At Sea</p>
              <p className="text-2xl font-semibold text-gray-900">
                {vessels.filter(v => v.status === 'At Sea').length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Users className="h-8 w-8 text-yellow-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Loading</p>
              <p className="text-2xl font-semibold text-gray-900">
                {vessels.filter(v => v.status === 'Loading').length}
              </p>
            </div>
          </div>
        </div>
      </div>
    </EntityPageLayout>
  );
};

export default VesselsPage;
