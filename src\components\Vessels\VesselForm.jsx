import React, { useState, useEffect } from "react";
import {
  <PERSON>alog, DialogTitle, DialogContent, TextField,
  DialogActions, Button, Alert, CircularProgress, Box, Grid,
  IconButton, Typography, useTheme, Tabs, Tab
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import { 
  getVesselById, 
  createVessel, 
  updateVessel,
  updateVesselHydrostaticData
} from "../../api/vesselsApi";

const VesselForm = ({ open, onClose, onSubmit, vesselId }) => {
  const theme = useTheme();
  const [activeTab, setActiveTab] = useState(0);
  const [formData, setFormData] = useState({
    name: "",
    imoNumber: "",
    type: "",
    length: "",
    width: "",
    draft: "",
    displacementFactor: "",
    tpc: ""
  });
  const [error, setError] = useState(null);
  const [loading, setLoading] = useState(false);
  const [isFetching, setIsFetching] = useState(false);

  useEffect(() => {
    const fetchVesselData = async () => {
      if (vesselId && open) {
        setIsFetching(true);
        setError(null);
        try {
          const vessel = await getVesselById(vesselId);
          setFormData({
            name: vessel.name || "",
            imoNumber: vessel.imoNumber || "",
            type: vessel.type || "",
            length: vessel.length || "",
            width: vessel.width || "",
            draft: vessel.draft || "",
            displacementFactor: vessel.displacementFactor || "",
            tpc: vessel.tpc || ""
          });
        } catch (err) {
          setError(err.message || "Failed to load vessel data.");
        } finally {
          setIsFetching(false);
        }
      } else {
        setFormData({
          name: "",
          imoNumber: "",
          type: "",
          length: "",
          width: "",
          draft: "",
          displacementFactor: "",
          tpc: ""
        });
      }
    };

    fetchVesselData();
  }, [vesselId, open]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      const numericFields = ['length', 'width', 'draft'];
      const parsedData = { ...formData };

      numericFields.forEach(field => {
        parsedData[field] = parseFloat(parsedData[field]) || 0;
      });

      if (vesselId) {
        await updateVessel(vesselId, parsedData);
      } else {
        await createVessel(parsedData);
      }
      onSubmit();
      onClose();
    } catch (err) {
      setError(err.response?.data?.message || err.message || "Operation failed.");
    } finally {
      setLoading(false);
    }
  };

  const handleSubmitHydrostatic = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      await updateVesselHydrostaticData(vesselId, {
        displacementFactor: parseFloat(formData.displacementFactor),
        tpc: parseFloat(formData.tpc)
      });
      onSubmit();
      onClose();
    } catch (err) {
      setError(err.response?.data?.message || err.message || "Failed to update hydrostatic data.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle sx={{
        display: "flex",
        justifyContent: "space-between",
        alignItems: "center",
        bgcolor: theme.palette.primary.main,
        color: "white",
        py: 2
      }}>
        <Typography variant="h6" fontWeight="bold">
          {vesselId ? `Modifier le navire` : "Créer un nouveau navire"}
        </Typography>
        <IconButton onClick={onClose} sx={{ color: "white" }}>
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <Tabs value={activeTab} onChange={handleTabChange} sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tab label="Informations générales" />
        {vesselId && <Tab label="Données hydrostatiques" />}
      </Tabs>

      <DialogContent sx={{ p: 3 }}>
        {isFetching ? (
          <Box display="flex" justifyContent="center" py={4}>
            <CircularProgress />
          </Box>
        ) : (
          <>
            {error && <Alert severity="error" sx={{ mb: 3 }}>{error}</Alert>}

            {activeTab === 0 ? (
              <Box component="form" onSubmit={handleSubmit}>
                <Grid container spacing={2}>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Nom du navire *"
                      name="name"
                      value={formData.name}
                      onChange={handleChange}
                      required
                      margin="normal"
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Numéro IMO *"
                      name="imoNumber"
                      value={formData.imoNumber}
                      onChange={handleChange}
                      required={!vesselId}
                      disabled={!!vesselId}
                      margin="normal"
                      helperText={vesselId ? "Non modifiable" : ""}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Type de navire *"
                      name="type"
                      value={formData.type}
                      onChange={handleChange}
                      required
                      margin="normal"
                    />
                  </Grid>
                  <Grid item xs={12} md={4}>
                    <TextField
                      fullWidth
                      label="Longueur (m) *"
                      name="length"
                      value={formData.length}
                      onChange={handleChange}
                      required
                      type="number"
                      margin="normal"
                    />
                  </Grid>
                  <Grid item xs={12} md={4}>
                    <TextField
                      fullWidth
                      label="Largeur (m) *"
                      name="width"
                      value={formData.width}
                      onChange={handleChange}
                      required
                      type="number"
                      margin="normal"
                    />
                  </Grid>
                  <Grid item xs={12} md={4}>
                    <TextField
                      fullWidth
                      label="Tirant d'eau (m) *"
                      name="draft"
                      value={formData.draft}
                      onChange={handleChange}
                      required
                      type="number"
                      margin="normal"
                    />
                  </Grid>
                </Grid>
                <DialogActions sx={{ mt: 3, justifyContent: 'flex-end' }}>
                  <Button onClick={onClose} variant="outlined">
                    Annuler
                  </Button>
                  <Button
                    type="submit"
                    variant="contained"
                    color="primary"
                    disabled={loading}
                    startIcon={loading ? <CircularProgress size={20} /> : null}
                  >
                    {vesselId ? "Enregistrer" : "Créer"}
                  </Button>
                </DialogActions>
              </Box>
            ) : (
              <Box component="form" onSubmit={handleSubmitHydrostatic}>
                <Grid container spacing={2}>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Facteur de déplacement"
                      name="displacementFactor"
                      value={formData.displacementFactor}
                      onChange={handleChange}
                      type="number"
                      margin="normal"
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="TPC"
                      name="tpc"
                      value={formData.tpc}
                      onChange={handleChange}
                      type="number"
                      margin="normal"
                    />
                  </Grid>
                </Grid>
                <DialogActions sx={{ mt: 3, justifyContent: 'flex-end' }}>
                  <Button onClick={onClose} variant="outlined">
                    Annuler
                  </Button>
                  <Button
                    type="submit"
                    variant="contained"
                    color="primary"
                    disabled={loading}
                    startIcon={loading ? <CircularProgress size={20} /> : null}
                  >
                    Enregistrer
                  </Button>
                </DialogActions>
              </Box>
            )}
          </>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default VesselForm;