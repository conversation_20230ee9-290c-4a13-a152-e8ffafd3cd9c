import instance from "./axiosConfig";

const API_URL = "/ports";

export const getPorts = async () => {
  try {
    const response = await instance.get(API_URL);
    return response.data;
  } catch (error) {
    console.error("Error fetching ports:", error);
    throw error;
  }
};

export const getPortById = async (id) => {
  try {
    const response = await instance.get(`${API_URL}/${id}`);
    return response.data;
  } catch (error) {
    console.error(`Error fetching port ${id}:`, error);
    throw error;
  }
};

export const createPort = async (portData) => {
  try {
    const payload = {
      name: portData.name,
      code: portData.code,
      country: portData.country,
      standardWaterDensity: parseFloat(portData.standardWaterDensity)
    };

    const response = await instance.post(API_URL, payload);
    return response.data;
  } catch (error) {
    console.error("Error creating port:", error);
    let errorMessage = "Failed to create port";
    
    if (error.response) {
      if (error.response.data.errors) {
        // Handle validation errors from backend
        errorMessage = Object.values(error.response.data.errors)
          .flat()
          .join('\n');
      } else {
        errorMessage = error.response.data.title || error.response.data.message || errorMessage;
      }
    }
    
    throw new Error(errorMessage);
  }
};

export const updatePort = async (id, portData) => {
  try {
    const payload = {
      name: portData.name,
      country: portData.country,
      standardWaterDensity: parseFloat(portData.standardWaterDensity)
    };

    const response = await instance.put(`${API_URL}/${id}`, payload);
    return response.data;
  } catch (error) {
    console.error(`Error updating port ${id}:`, error);
    let errorMessage = "Failed to update port";
    
    if (error.response) {
      if (error.response.data.errors) {
        errorMessage = Object.values(error.response.data.errors)
          .flat()
          .join('\n');
      } else {
        errorMessage = error.response.data.title || error.response.data.message || errorMessage;
      }
    }
    
    throw new Error(errorMessage);
  }
};

export const deletePort = async (id) => {
  try {
    await instance.delete(`${API_URL}/${id}`);
  } catch (error) {
    console.error(`Error deleting port ${id}:`, error);
    throw new Error(error.response?.data?.message || "Failed to delete port");
  }
};