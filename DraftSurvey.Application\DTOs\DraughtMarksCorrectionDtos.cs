﻿using System;
using System.ComponentModel.DataAnnotations;

namespace DraftSurvey.Application.DTOs
{
    public class DraughtMarksCorrectionDto
    {
        public Guid Id { get; set; }

        public double Value { get; set; }

        public string CorrectionType { get; set; }

        public string Unit { get; set; }

        public string AppliedToLocation { get; set; }

        public string Notes { get; set; }

        public DateTime CorrectionDate { get; set; }

        public Guid DraftSurveyId { get; set; }
    }

    public class DraughtMarksCorrectionCreateDto
    {
        [Required]
        public double Value { get; set; }

        [Required]
        public string CorrectionType { get; set; }

        [Required]
        public string Unit { get; set; }

        [Required]
        public string AppliedToLocation { get; set; }

        public string Notes { get; set; }

        public DateTime? CorrectionDate { get; set; } = DateTime.UtcNow;

        [Required]
        public Guid DraftSurveyId { get; set; }
    }

    public class DraughtMarksCorrectionUpdateDto
    {
        [Required]
        public double Value { get; set; }

        [Required]
        public string CorrectionType { get; set; }

        [Required]
        public string Unit { get; set; }

        [Required]
        public string AppliedToLocation { get; set; }

        public string Notes { get; set; }

        public DateTime? CorrectionDate { get; set; }
    }
}
