import React from 'react';
import {
  <PERSON>,
  Typography,
  Card,
  CardContent,
  Grid,
  LinearProgress,
  Chip,
  Avatar,
  IconButton,
  useTheme,
  Paper,
  Divider,
  Button,
} from '@mui/material';
import { createAlphaColor } from '../../utils/colorUtils';
import {
  TrendingUp,
  TrendingDown,
  ShoppingCart,
  Visibility as Eye,
  MoreVert,
  ArrowUpward,
  ArrowDownward,
  DirectionsBoat,
  Assessment,
  Inventory,
  LocalShipping,
} from '@mui/icons-material';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, AreaChart, Area } from 'recharts';

const ModernDashboardContent = () => {
  const theme = useTheme();

  // Sample data for charts
  const chartData = [
    { name: '01 Jun', value: 400 },
    { name: '02 Jun', value: 300 },
    { name: '03 Jun', value: 500 },
    { name: '04 Jun', value: 280 },
    { name: '05 Jun', value: 590 },
    { name: '06 Jun', value: 320 },
    { name: '07 Jun', value: 490 },
    { name: '08 Jun', value: 600 },
    { name: '09 Jun', value: 450 },
    { name: '10 Jun', value: 700 },
    { name: '11 Jun', value: 650 },
    { name: '12 Jun', value: 800 },
  ];

  const topProducts = [
    { name: 'Maersk Neko Poster', sold: 1240, growth: 16.2, positive: true },
    { name: 'Echoes Necklace', sold: 1145, growth: 15.9, positive: true },
    { name: 'Spiky Ring', sold: 1073, growth: 8.5, positive: true },
    { name: 'Pastel Petals Poster', sold: 1022, growth: 2.3, positive: true },
    { name: 'Il Limone', sold: 962, growth: -0.7, positive: false },
    { name: 'Ringed Earring', sold: 1901, growth: -1.1, positive: false },
  ];

  const countries = [
    { name: 'United States', percentage: 38.6, flag: '🇺🇸' },
    { name: 'Germany', percentage: 29.8, flag: '🇩🇪' },
    { name: 'France', percentage: 18.2, flag: '🇫🇷' },
    { name: 'United Kingdom', percentage: 13.4, flag: '🇬🇧' },
  ];

  return (
    <Box sx={{ p: 3, bgcolor: '#f8fafc', minHeight: '100vh' }}>
      {/* Header */}
      <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h4" fontWeight="600" color="text.primary">
          Overview
        </Typography>
        <Box sx={{ display: 'flex', gap: 2 }}>
          <Button variant="outlined" size="small">Monthly</Button>
          <Button variant="outlined" size="small">Sales target</Button>
        </Box>
      </Box>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} lg={3}>
          <Card sx={{ 
            p: 3, 
            borderRadius: 3, 
            border: 'none',
            boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
            '&:hover': { boxShadow: '0 4px 12px rgba(0,0,0,0.15)' }
          }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <Box sx={{
                p: 1.5,
                borderRadius: 2,
                bgcolor: createAlphaColor(theme.palette.primary.main, 0.1),
                mr: 2
              }}>
                <DirectionsBoat sx={{ color: theme.palette.primary.main }} />
              </Box>
              <IconButton size="small" sx={{ ml: 'auto' }}>
                <MoreVert />
              </IconButton>
            </Box>
            <Typography variant="h4" fontWeight="700" sx={{ mb: 1 }}>
              $82,373.21
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <ArrowUpward sx={{ fontSize: 16, color: 'success.main' }} />
              <Typography variant="body2" color="success.main" fontWeight="600">
                +3.4%
              </Typography>
              <Typography variant="body2" color="text.secondary">
                from last month
              </Typography>
            </Box>
            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
              Total worth
            </Typography>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} lg={3}>
          <Card sx={{ 
            p: 3, 
            borderRadius: 3, 
            border: 'none',
            boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
            '&:hover': { boxShadow: '0 4px 12px rgba(0,0,0,0.15)' }
          }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <Box sx={{ 
                p: 1.5, 
                borderRadius: 2, 
                bgcolor: createAlphaColor(theme.palette.success.main, 0.1),
                mr: 2
              }}>
                <ShoppingCart sx={{ color: theme.palette.success.main }} />
              </Box>
              <IconButton size="small" sx={{ ml: 'auto' }}>
                <MoreVert />
              </IconButton>
            </Box>
            <Typography variant="h4" fontWeight="700" sx={{ mb: 1 }}>
              7,234
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <ArrowDownward sx={{ fontSize: 16, color: 'error.main' }} />
              <Typography variant="body2" color="error.main" fontWeight="600">
                -2.8%
              </Typography>
              <Typography variant="body2" color="text.secondary">
                from last month
              </Typography>
            </Box>
            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
              Total Order
            </Typography>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} lg={3}>
          <Card sx={{ 
            p: 3, 
            borderRadius: 3, 
            border: 'none',
            boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
            '&:hover': { boxShadow: '0 4px 12px rgba(0,0,0,0.15)' }
          }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <Box sx={{ 
                p: 1.5, 
                borderRadius: 2, 
                bgcolor: createAlphaColor(theme.palette.info.main, 0.1),
                mr: 2
              }}>
                <Eye sx={{ color: theme.palette.info.main }} />
              </Box>
              <IconButton size="small" sx={{ ml: 'auto' }}>
                <MoreVert />
              </IconButton>
            </Box>
            <Typography variant="h4" fontWeight="700" sx={{ mb: 1 }}>
              3.1M
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <ArrowUpward sx={{ fontSize: 16, color: 'success.main' }} />
              <Typography variant="body2" color="success.main" fontWeight="600">
                +4.6%
              </Typography>
              <Typography variant="body2" color="text.secondary">
                from last month
              </Typography>
            </Box>
            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
              Impression
            </Typography>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} lg={3}>
          <Card sx={{ 
            p: 3, 
            borderRadius: 3, 
            border: 'none',
            boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
            '&:hover': { boxShadow: '0 4px 12px rgba(0,0,0,0.15)' }
          }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <Box sx={{ 
                p: 1.5, 
                borderRadius: 2, 
                bgcolor: createAlphaColor(theme.palette.warning.main, 0.1),
                mr: 2
              }}>
                <Assessment sx={{ color: theme.palette.warning.main }} />
              </Box>
              <Box sx={{ 
                ml: 'auto',
                width: 60,
                height: 60,
                borderRadius: '50%',
                background: `conic-gradient(${theme.palette.primary.main} 75%, ${createAlphaColor(theme.palette.primary.main, 0.1)} 0)`,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                position: 'relative'
              }}>
                <Box sx={{
                  width: 40,
                  height: 40,
                  borderRadius: '50%',
                  bgcolor: 'background.paper',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}>
                  <Typography variant="body2" fontWeight="600">75%</Typography>
                </Box>
              </Box>
            </Box>
            <Typography variant="h4" fontWeight="700" sx={{ mb: 1 }}>
              1.3K
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
              1.8k Units
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Made this month year
            </Typography>
          </Card>
        </Grid>
      </Grid>

      {/* Chart and Top Products */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} lg={8}>
          <Card sx={{ 
            p: 3, 
            borderRadius: 3, 
            border: 'none',
            boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
            height: 400
          }}>
            <Typography variant="h6" fontWeight="600" sx={{ mb: 3 }}>
              Sales Overview
            </Typography>
            <ResponsiveContainer width="100%" height="100%">
              <AreaChart data={chartData}>
                <defs>
                  <linearGradient id="colorGradient" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor={theme.palette.primary.main} stopOpacity={0.3}/>
                    <stop offset="95%" stopColor={theme.palette.primary.main} stopOpacity={0}/>
                  </linearGradient>
                </defs>
                <CartesianGrid strokeDasharray="3 3" stroke={createAlphaColor(theme.palette.divider, 0.3)} />
                <XAxis dataKey="name" axisLine={false} tickLine={false} />
                <YAxis axisLine={false} tickLine={false} />
                <Tooltip />
                <Area 
                  type="monotone" 
                  dataKey="value" 
                  stroke={theme.palette.primary.main}
                  strokeWidth={3}
                  fill="url(#colorGradient)"
                />
              </AreaChart>
            </ResponsiveContainer>
          </Card>
        </Grid>

        <Grid item xs={12} lg={4}>
          <Card sx={{ 
            p: 3, 
            borderRadius: 3, 
            border: 'none',
            boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
            height: 400
          }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
              <Typography variant="h6" fontWeight="600">
                Top product
              </Typography>
              <Button variant="text" size="small">View all</Button>
            </Box>
            <Box sx={{ maxHeight: 320, overflow: 'auto' }}>
              {topProducts.map((product, index) => (
                <Box key={index} sx={{ 
                  display: 'flex', 
                  alignItems: 'center', 
                  mb: 2,
                  p: 1,
                  borderRadius: 2,
                  '&:hover': { bgcolor: createAlphaColor(theme.palette.primary.main, 0.05) }
                }}>
                  <Avatar sx={{ 
                    width: 40, 
                    height: 40, 
                    mr: 2,
                    bgcolor: createAlphaColor(theme.palette.primary.main, 0.1),
                    color: theme.palette.primary.main
                  }}>
                    {product.name.charAt(0)}
                  </Avatar>
                  <Box sx={{ flexGrow: 1 }}>
                    <Typography variant="body2" fontWeight="600">
                      {product.name}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Sold: {product.sold}
                    </Typography>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                    {product.positive ? (
                      <ArrowUpward sx={{ fontSize: 14, color: 'success.main' }} />
                    ) : (
                      <ArrowDownward sx={{ fontSize: 14, color: 'error.main' }} />
                    )}
                    <Typography 
                      variant="caption" 
                      color={product.positive ? 'success.main' : 'error.main'}
                      fontWeight="600"
                    >
                      {product.growth > 0 ? '+' : ''}{product.growth}%
                    </Typography>
                  </Box>
                </Box>
              ))}
            </Box>
          </Card>
        </Grid>
      </Grid>

      {/* Bottom Section */}
      <Grid container spacing={3}>
        <Grid item xs={12} lg={8}>
          <Card sx={{ 
            p: 3, 
            borderRadius: 3, 
            border: 'none',
            boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
          }}>
            <Typography variant="h6" fontWeight="600" sx={{ mb: 3 }}>
              Top countries
            </Typography>
            <Box>
              {countries.map((country, index) => (
                <Box key={index} sx={{ mb: 3 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                      <Typography variant="h6">{country.flag}</Typography>
                      <Typography variant="body2" fontWeight="600">
                        {country.name}
                      </Typography>
                    </Box>
                    <Typography variant="body2" fontWeight="600">
                      {country.percentage}%
                    </Typography>
                  </Box>
                  <LinearProgress 
                    variant="determinate" 
                    value={country.percentage} 
                    sx={{ 
                      height: 8, 
                      borderRadius: 4,
                      bgcolor: createAlphaColor(theme.palette.primary.main, 0.1),
                      '& .MuiLinearProgress-bar': {
                        borderRadius: 4,
                        bgcolor: theme.palette.primary.main
                      }
                    }}
                  />
                </Box>
              ))}
            </Box>
          </Card>
        </Grid>

        <Grid item xs={12} lg={4}>
          <Card sx={{ 
            p: 3, 
            borderRadius: 3, 
            border: 'none',
            boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
          }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
              <Typography variant="h6" fontWeight="600">
                Channel revenue
              </Typography>
              <Button variant="text" size="small">Monthly</Button>
            </Box>
            <Box>
              <Box sx={{ mb: 3 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                  <Typography variant="body2" fontWeight="600">Direct</Typography>
                  <Typography variant="body2" fontWeight="600">$300,024</Typography>
                </Box>
                <LinearProgress 
                  variant="determinate" 
                  value={85} 
                  sx={{ 
                    height: 6, 
                    borderRadius: 3,
                    bgcolor: createAlphaColor(theme.palette.success.main, 0.1),
                    '& .MuiLinearProgress-bar': {
                      borderRadius: 3,
                      bgcolor: theme.palette.success.main
                    }
                  }}
                />
              </Box>
              <Box sx={{ mb: 3 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                  <Typography variant="body2" fontWeight="600">Affiliate</Typography>
                  <Typography variant="body2" fontWeight="600">$135,965</Typography>
                </Box>
                <LinearProgress 
                  variant="determinate" 
                  value={60} 
                  sx={{ 
                    height: 6, 
                    borderRadius: 3,
                    bgcolor: createAlphaColor(theme.palette.warning.main, 0.1),
                    '& .MuiLinearProgress-bar': {
                      borderRadius: 3,
                      bgcolor: theme.palette.warning.main
                    }
                  }}
                />
              </Box>
              <Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                  <Typography variant="body2" fontWeight="600">Sponsored</Typography>
                  <Typography variant="body2" fontWeight="600">$48,251</Typography>
                </Box>
                <LinearProgress 
                  variant="determinate" 
                  value={30} 
                  sx={{ 
                    height: 6, 
                    borderRadius: 3,
                    bgcolor: createAlphaColor(theme.palette.info.main, 0.1),
                    '& .MuiLinearProgress-bar': {
                      borderRadius: 3,
                      bgcolor: theme.palette.info.main
                    }
                  }}
                />
              </Box>
            </Box>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default ModernDashboardContent;
