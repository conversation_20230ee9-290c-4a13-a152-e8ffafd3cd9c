// src/api/taskAssignmentsApi.js
import axiosInstance from './axiosConfig';

export const getAllTaskAssignments = async (include = null) => {
  try {
    const response = await axiosInstance.get('/TaskAssignments', {
      params: { include }
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching all task assignments:', error);
    throw error;
  }
};

export const getTaskAssignmentById = async (id) => {
  try {
    const response = await axiosInstance.get(`/TaskAssignments/${id}`);
    return response.data;
  } catch (error) {
    console.error(`Error fetching task assignment with ID ${id}:`, error);
    throw error;
  }
};

export const getTasksByUserId = async (userId) => {
  try {
    const response = await axiosInstance.get(`/TaskAssignments/user/${userId}`);
    return response.data;
  } catch (error) {
    console.error(`Error fetching tasks for user ${userId}:`, error);
    throw error;
  }
};

export const getTasksBySurveyId = async (surveyId) => {
  try {
    const response = await axiosInstance.get(`/TaskAssignments/survey/${surveyId}`);
    return response.data;
  } catch (error) {
    console.error(`Error fetching tasks for survey ${surveyId}:`, error);
    throw error;
  }
};

export const createTaskAssignment = async (taskData) => {
  try {
    const response = await axiosInstance.post('/TaskAssignments', taskData);
    return response.data;
  } catch (error) {
    console.error('Error creating task assignment:', error);
    throw error;
  }
};

export const updateTaskAssignment = async (id, updateData) => {
  try {
    await axiosInstance.put(`/TaskAssignments/${id}`, updateData);
  } catch (error) {
    console.error(`Error updating task assignment ${id}:`, error);
    throw error;
  }
};

export const markTaskAsCompleted = async (id, completionData) => {
  try {
    await axiosInstance.put(`/TaskAssignments/${id}/complete`, {
      TaskAssignmentId: id,
      IsCompleted: true,
      CompletionNotes: completionData.completionNotes
    });
  } catch (error) {
    console.error(`Error marking task ${id} as completed:`, error);
    throw error;
  }
};

export const submitTaskForValidation = async (id, submissionData) => {
  try {
    await axiosInstance.post(`/TaskAssignments/${id}/submit`, {
      Notes: submissionData.notes,
      Attachments: submissionData.attachments || []
    });
  } catch (error) {
    console.error(`Error submitting task ${id} for validation:`, error);
    throw error;
  }
};

export const approveTask = async (id, comment) => {
  try {
    const response = await axiosInstance.post(`/TaskAssignments/${id}/approve`, null, {
      params: { comment }
    });
    return response.data;
  } catch (error) {
    console.error(`Error approving task ${id}:`, error);
    throw error;
  }
};

export const rejectTask = async (id, rejectionData) => {
  try {
    const response = await axiosInstance.post(`/TaskAssignments/${id}/reject`, {
      Reason: rejectionData.reason,
      RequiresResubmission: rejectionData.requiresResubmission
    });
    return response.data;
  } catch (error) {
    console.error(`Error rejecting task ${id}:`, error);
    throw error;
  }
};

export const deleteTaskAssignment = async (id) => {
  try {
    await axiosInstance.delete(`/TaskAssignments/${id}`);
  } catch (error) {
    console.error(`Error deleting task assignment ${id}:`, error);
    throw error;
  }
};