import React, { useState, useEffect } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  CircularProgress,
  Box
} from '@mui/material';
import { getUsers } from '../../api/usersApi';
import { getSurveys } from '../../api/draftApi';

const TaskFormDialog = ({ open, onClose, onSave, task }) => {
  const [formData, setFormData] = useState({
    taskName: '',
    userId: '',
    draftSurveyId: '',
    status: 'Assigned'
  });
  const [users, setUsers] = useState([]);
  const [surveys, setSurveys] = useState([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (open) {
      const fetchData = async () => {
        setLoading(true);
        try {
          const [usersData, surveysData] = await Promise.all([
            getUsers(),
            getSurveys()
          ]);
          setUsers(usersData);
          setSurveys(surveysData);
          
          if (task) {
            setFormData({
              taskName: task.taskName,
              userId: task.userId,
              draftSurveyId: task.draftSurveyId,
              status: task.status
            });
          } else {
            setFormData({
              taskName: '',
              userId: '',
              draftSurveyId: '',
              status: 'Assigned'
            });
          }
        } finally {
          setLoading(false);
        }
      };
      fetchData();
    }
  }, [open, task]);

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = () => {
    if (!formData.taskName || !formData.userId || !formData.draftSurveyId) {
      alert('Veuillez remplir tous les champs obligatoires');
      return;
    }
    onSave(formData);
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>
        {task ? 'Modifier la Tâche' : 'Créer une Nouvelle Tâche'}
      </DialogTitle>
      <DialogContent>
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
            <CircularProgress />
          </Box>
        ) : (
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <TextField
                name="taskName"
                label="Nom de la tâche"
                fullWidth
                value={formData.taskName}
                onChange={handleChange}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>Agent</InputLabel>
                <Select
                  name="userId"
                  value={formData.userId}
                  onChange={handleChange}
                  label="Agent"
                >
                  {users.map(user => (
                    <MenuItem key={user.id} value={user.id}>
                      {user.fullName}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>Survey</InputLabel>
                <Select
                  name="draftSurveyId"
                  value={formData.draftSurveyId}
                  onChange={handleChange}
                  label="Survey"
                >
                  {surveys.map(survey => (
                    <MenuItem key={survey.id} value={survey.id}>
                      {survey.surveyNumber}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel>Statut</InputLabel>
                <Select
                  name="status"
                  value={formData.status}
                  onChange={handleChange}
                  label="Statut"
                >
                  <MenuItem value="Assigned">Assignée</MenuItem>
                  <MenuItem value="InProgress">En cours</MenuItem>
                  <MenuItem value="PendingValidation">En attente</MenuItem>
                  <MenuItem value="Completed">Terminée</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        )}
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Annuler</Button>
        <Button onClick={handleSubmit} variant="contained" color="primary">
          {task ? 'Mettre à jour' : 'Créer'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default TaskFormDialog;