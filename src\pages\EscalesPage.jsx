import React, { useState, useEffect } from "react";
import {
  <PERSON>rid, <PERSON>, Typography, Button,
  CircularProgress, Alert, Dialog,
  DialogTitle, DialogContent, DialogActions,
  useTheme
} from "@mui/material";
import AddIcon from "@mui/icons-material/Add";

import EscaleList from "../components/Escales/EscaleList";
import EscaleDetails from "../components/Escales/EscaleDetails";
import CreateEscaleForm from "../components/Escales/CreateEscaleForm";
import EditEscaleForm from "../components/Escales/EditEscaleForm";
import Navbar from "../components/Navbar/Navbar";

import {
  getAllEscales,
  getActiveEscales,
  createEscale,
  updateEscale
} from "../api/escaleApi";

const EscalesPage = () => {
  const theme = useTheme();

  const [escales, setEscales] = useState([]);
  const [selectedEscale, setSelectedEscale] = useState(null);
  const [editingEscale, setEditingEscale] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [openCreateDialog, setOpenCreateDialog] = useState(false);
  const [filter, setFilter] = useState("active");

  const fetchEscales = async () => {
    setLoading(true);
    setError(null);
    try {
      const data = filter === "active"
        ? await getActiveEscales()
        : await getAllEscales();
      setEscales(data);
    } catch (err) {
      setError(err.response?.data?.message || err.message || "Erreur lors du chargement des escales.");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchEscales();
  }, [filter]);

  const handleCreateEscale = async (escaleData) => {
    setLoading(true);
    setError(null);
    try {
      await createEscale(escaleData);
      setOpenCreateDialog(false);
      await fetchEscales();
    } catch (err) {
      setError(err.response?.data?.message || err.message || "Échec de la création de l'escale.");
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateEscale = async (escaleId, updatedData) => {
    setLoading(true);
    setError(null);
    try {
      await updateEscale(escaleId, updatedData);
      setEditingEscale(null);
      await fetchEscales();
    } catch (err) {
      setError(err.response?.data?.message || err.message || "Échec de la mise à jour de l'escale.");
    } finally {
      setLoading(false);
    }
  };

  const handleDownloadReport = (escale) => {
    alert(`Report download initiated for Escale Reference: ${escale.reference}`);
  };

  return (
    <>
      <Navbar />
      <Box
        sx={{
          flexGrow: 1,
          pt: { xs: '72px', sm: '80px', md: '88px' },
          pb: 4,
          px: { xs: 2, sm: 3, md: 4 },
          bgcolor: theme.palette.grey[100],
          minHeight: "100vh",
        }}
      >
        <Box
          sx={{
            width: '100%',
            maxWidth: 1400,
            mx: 'auto',
            p: { xs: 2, sm: 3, md: 4 },
            borderRadius: 3,
            bgcolor: 'white',
            boxShadow: theme.shadows[6],
            border: `1px solid ${theme.palette.divider}`,
          }}
        >
          {/* Header */}
          <Grid container spacing={2} alignItems="center" justifyContent="space-between" sx={{ mb: 3 }}>
            <Grid item xs={12} sm={6}>
              <Typography variant="h4" fontWeight="bold" color="black">
                Gestion des Escales
              </Typography>
            </Grid>
            <Grid item xs={12} sm={6} textAlign={{ xs: "left", sm: "right" }}>
              <Button
                variant={filter === "active" ? "contained" : "outlined"}
                onClick={() => setFilter("active")}
                sx={{ mr: 1 }}
              >
                Actives
              </Button>
              <Button
                variant={filter === "all" ? "contained" : "outlined"}
                onClick={() => setFilter("all")}
                sx={{ mr: 2 }}
              >
                Toutes
              </Button>
              <Button
                variant="contained"
                color="success"
                startIcon={<AddIcon />}
                onClick={() => setOpenCreateDialog(true)}
              >
                Nouvelle Escale
              </Button>
            </Grid>
          </Grid>

          {/* Body */}
          {loading ? (
            <Box display="flex" justifyContent="center" py={5}>
              <CircularProgress />
            </Box>
          ) : error ? (
            <Alert severity="error">{error}</Alert>
          ) : (
            <EscaleList
              escales={escales}
              onSelectEscale={setSelectedEscale}
              onEditEscale={setEditingEscale}
              onDownloadReport={handleDownloadReport}
            />
          )}

          {/* Dialog - Créer */}
          <Dialog
            open={openCreateDialog}
            onClose={() => setOpenCreateDialog(false)}
            maxWidth="sm"
            fullWidth
          >
            <DialogTitle sx={{ bgcolor: theme.palette.primary.main, color: "white" }}>
              Créer une nouvelle escale
            </DialogTitle>
            <DialogContent sx={{ bgcolor: theme.palette.background.paper }}>
              <CreateEscaleForm
                onSubmit={handleCreateEscale}
                onCancel={() => setOpenCreateDialog(false)}
              />
            </DialogContent>
          </Dialog>

          {/* Dialog - Détails */}
          {selectedEscale && (
            <Dialog
              open={!!selectedEscale}
              onClose={() => setSelectedEscale(null)}
              maxWidth="md"
              fullWidth
            >
              <DialogTitle sx={{bgcolor: "black", color: "white" }}>
                Détails de l’escale: {selectedEscale.reference}
              </DialogTitle>
              <DialogContent>
                <EscaleDetails escale={selectedEscale} />
              </DialogContent>
              <DialogActions>
                <Button onClick={() => setSelectedEscale(null)} color="error" variant="outlined">
                  Fermer
                </Button>
              </DialogActions>
            </Dialog>
          )}

          {/* Dialog - Modifier */}
          {editingEscale && (
            <Dialog
              open={!!editingEscale}
              onClose={() => setEditingEscale(null)}
              maxWidth="sm"
              fullWidth
            >
              <DialogTitle sx={{ bgcolor: theme.palette.primary.main, color: "white" }}>
                Modifier l’escale: {editingEscale.reference}
              </DialogTitle>
              <DialogContent>
                <EditEscaleForm
                  escale={editingEscale}
                  onSave={handleUpdateEscale}
                  onCancel={() => setEditingEscale(null)}
                />
              </DialogContent>
            </Dialog>
          )}
        </Box>
      </Box>
    </>
  );
};

export default EscalesPage;
