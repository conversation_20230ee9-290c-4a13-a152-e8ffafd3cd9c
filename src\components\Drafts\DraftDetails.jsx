import React, { useState } from "react";
import { Dialog, DialogTitle, DialogContent, Tabs, Tab, Box } from "@mui/material";
import TaskAssignment from "./TaskAssignment"; 
import GeneralInfo from "./GeneralInfo";
import LectureTE from "./LectureTE"; 
import BallastSounding from "./BallastSounding"; 
import DensitySW from "./DensitySW"; 
import Existants from "./Existants"; 

const DraftDetails = ({ draft, onClose, userRole }) => {
  const [tabIndex, setTabIndex] = useState(0);

  const handleChangeTab = (event, newIndex) => {
    setTabIndex(newIndex);
  };

  return (
    <Dialog open onClose={onClose} maxWidth="lg" fullWidth>
      <DialogTitle sx={{ textAlign: "center", fontWeight: "bold", fontSize: "22px", color: "#2D3E50" }}>
        Gestion du Draft - {draft.reference}
      </DialogTitle>
      <DialogContent>
        <Tabs value={tabIndex} onChange={handleChangeTab} variant="scrollable" scrollButtons="auto">
          {userRole === "Team Lead" && <Tab label="Assignation des Tâches" />}
          <Tab label="Infos Générales" />
          <Tab label="Lecture TE" />
          <Tab label="Ballast Sounding" />
          <Tab label="Densité SW" />
          <Tab label="Existants" />
        </Tabs>

        <Box sx={{ marginTop: 3 }}>
          {tabIndex === 0 && userRole === "Team Lead" && <TaskAssignment />}
          {tabIndex === (userRole === "Team Lead" ? 1 : 0) && <GeneralInfo />}
          {tabIndex === (userRole === "Team Lead" ? 2 : 1) && <LectureTE />}
          {tabIndex === (userRole === "Team Lead" ? 3 : 2) && <BallastSounding />}
          {tabIndex === (userRole === "Team Lead" ? 4 : 3) && <DensitySW />}
          {tabIndex === (userRole === "Team Lead" ? 5 : 4) && <Existants />}
        </Box>
      </DialogContent>
    </Dialog>
  );
};

export default DraftDetails;
