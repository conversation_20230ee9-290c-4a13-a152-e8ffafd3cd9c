﻿using DraftSurvey.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace DraftSurvey.Infrastructure.Data.Configurations
{
    public class RoleConfiguration : IEntityTypeConfiguration<Role>
    {
        public void Configure(EntityTypeBuilder<Role> builder)
        {
            builder.ToTable("Roles");

            // Configuration de la clé primaire
            builder.HasKey(r => r.Id);

            // Configuration des propriétés
            builder.Property(r => r.Name)
                .IsRequired()
                .HasMaxLength(50);

            builder.Property(r => r.Description)
                .HasMaxLength(255);

            builder.Property(r => r.CanManageUsers)
                .IsRequired()
                .HasDefaultValue(false);

            builder.Property(r => r.CanManageAllPorts)
                .IsRequired()
                .HasDefaultValue(false);

            // Configuration de l'index unique sur le nom
            builder.HasIndex(r => r.Name)
                .IsUnique();

            // Configuration des relations
            builder.HasMany(r => r.Users)
                .WithOne(u => u.Role)
                .HasForeignKey(u => u.RoleId)
                .OnDelete(DeleteBehavior.Restrict); // Empêche la suppression si des utilisateurs ont ce rôle

            // Données initiales
            builder.HasData(
                new Role
                {
                    Id = Guid.Parse("00000000-0000-0000-0000-000000000001"),
                    Name = "Admin",
                    Description = "Administrateur système avec tous les droits",
                    CanManageUsers = true,
                    CanManageAllPorts = true
                },
                new Role
                {
                    Id = Guid.Parse("00000000-0000-0000-0000-000000000002"),
                    Name = "TeamLead",
                    Description = "Chef d'équipe avec des droits étendus",
                    CanManageUsers = false,
                    CanManageAllPorts = true
                },
                new Role
                {
                    Id = Guid.Parse("00000000-0000-0000-0000-000000000003"),
                    Name = "Agent",
                    Description = "Utilisateur standard avec droits limités",
                    CanManageUsers = false,
                    CanManageAllPorts = false
                }
            );
        }
    }
}