// src/components/Tasks/TaskAssignment.jsx
import React, { useState } from 'react';
import {
  Box, Typography, Table, TableBody,
  TableCell, TableContainer, TableHead,
  TableRow, Paper, Select, MenuItem,
  Button, FormControl, InputLabel
} from '@mui/material';

const TaskAssignment = ({ tasks = [], users = [], onAssign }) => { // Default to empty arrays to prevent .map() error
  const [assignments, setAssignments] = useState({});

  const handleAssign = (taskId, userId) => {
    setAssignments(prev => ({ ...prev, [taskId]: userId }));
  };

  const handleSubmit = () => {
    onAssign(assignments);
  };

  return (
    <Paper sx={{ p: 3 }}>
      <Typography variant="h6" gutterBottom>
        Assignation des Tâches
      </Typography>

      <TableContainer>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Tâche</TableCell>
              <TableCell>Assigné à</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {/* Conditional rendering to handle no tasks */}
            {tasks.length === 0 ? (
              <TableRow>
                <TableCell colSpan={2} sx={{ textAlign: 'center', py: 2 }}>
                  Aucune tâche disponible pour l'assignation.
                </TableCell>
              </TableRow>
            ) : (
              tasks.map((task) => (
                <TableRow key={task.id}>
                  <TableCell>{task.name}</TableCell>
                  <TableCell>
                    <FormControl fullWidth size="small">
                      <InputLabel>Assigner</InputLabel>
                      <Select
                        value={assignments[task.id] || ''}
                        onChange={(e) => handleAssign(task.id, e.target.value)}
                        label="Assigner"
                      >
                        <MenuItem value="">Non assigné</MenuItem>
                        {/* Conditional rendering for users as well */}
                        {users.map((user) => (
                          <MenuItem key={user.id} value={user.id}>
                            {user.name} {/* Assuming 'name' property for user display */}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </TableContainer>

      <Box sx={{ mt: 2, display: 'flex', justifyContent: 'flex-end' }}>
        <Button
          variant="contained"
          onClick={handleSubmit}
        >
          Enregistrer les Assignations
        </Button>
      </Box>
    </Paper>
  );
};

export default TaskAssignment;