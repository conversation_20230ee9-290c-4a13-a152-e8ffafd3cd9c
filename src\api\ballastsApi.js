import api from './axiosConfig';

export const getBallastsBySurvey = async (surveyId) => {
  try {
    const response = await api.get(`/ballasts/by-survey/${surveyId}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching ballasts:', error);
    throw error;
  }
};

export const createBallast = async (ballastData) => {
  try {
    const response = await api.post('/ballasts', ballastData);
    return response.data;
  } catch (error) {
    console.error('Error creating ballast:', error);
    throw error;
  }
};

export const updateBallast = async (id, ballastData) => {
  try {
    await api.put(`/ballasts/${id}`, ballastData);
  } catch (error) {
    console.error('Error updating ballast:', error);
    throw error;
  }
};

export const deleteBallast = async (id) => {
  try {
    await api.delete(`/ballasts/${id}`);
  } catch (error) {
    console.error('Error deleting ballast:', error);
    throw error;
  }
};