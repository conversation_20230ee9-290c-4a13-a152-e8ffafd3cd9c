import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Typography, 
  Paper, 
  Table, 
  TableBody, 
  TableCell, 
  TableContainer, 
  TableHead, 
  TableRow,
  TextField,
  Button,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Grid,
  Chip
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import { 
  getLiquidStatementsBySurvey, 
  createLiquidStatement,
  updateLiquidStatement,
  deleteLiquidStatement,
  getAvailableLiquids
} from '../api/liquidStatementApi';

const LiquidStatementPage = ({ surveyId }) => {
  const [statements, setStatements] = useState([]);
  const [liquids, setLiquids] = useState([]);
  const [openDialog, setOpenDialog] = useState(false);
  const [currentStatement, setCurrentStatement] = useState(null);
  const [formData, setFormData] = useState({
    liquidId: '',
    volume: '',
    measuredDensity: '',
    tankName: '',
    measurementTime: new Date().toISOString().slice(0, 16),
    notes: ''
  });

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [statementsData, liquidsData] = await Promise.all([
          getLiquidStatementsBySurvey(surveyId),
          getAvailableLiquids()
        ]);
        setStatements(statementsData);
        setLiquids(liquidsData);
      } catch (error) {
        console.error('Error fetching data:', error);
      }
    };
    
    fetchData();
  }, [surveyId]);

  const handleOpenDialog = (statement = null) => {
    if (statement) {
      setCurrentStatement(statement);
      setFormData({
        liquidId: statement.liquidId,
        volume: statement.volume,
        measuredDensity: statement.measuredDensity || '',
        tankName: statement.tankName,
        measurementTime: statement.measurementTime.slice(0, 16),
        notes: statement.notes || ''
      });
    } else {
      setCurrentStatement(null);
      setFormData({
        liquidId: '',
        volume: '',
        measuredDensity: '',
        tankName: '',
        measurementTime: new Date().toISOString().slice(0, 16),
        notes: ''
      });
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  const handleChange = (e) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      const statementData = {
        ...formData,
        draftSurveyId: surveyId,
        volume: parseFloat(formData.volume),
        measuredDensity: formData.measuredDensity ? parseFloat(formData.measuredDensity) : null,
        measurementTime: new Date(formData.measurementTime).toISOString()
      };

      if (currentStatement) {
        const updated = await updateLiquidStatement(currentStatement.id, statementData);
        setStatements(statements.map(s => s.id === updated.id ? updated : s));
      } else {
        const newStatement = await createLiquidStatement(statementData);
        setStatements([...statements, newStatement]);
      }
      handleCloseDialog();
    } catch (error) {
      console.error('Error saving liquid statement:', error);
    }
  };

  const handleDelete = async (id) => {
    try {
      await deleteLiquidStatement(id);
      setStatements(statements.filter(s => s.id !== id));
    } catch (error) {
      console.error('Error deleting liquid statement:', error);
    }
  };

  const getLiquidName = (liquidId) => {
    const liquid = liquids.find(l => l.id === liquidId);
    return liquid ? liquid.name : 'Inconnu';
  };

  const getLiquidDensity = (liquidId) => {
    const liquid = liquids.find(l => l.id === liquidId);
    return liquid ? liquid.standardDensity : 0;
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>Déclarations de Liquides</Typography>
      
      <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>
        <Button 
          variant="contained" 
          startIcon={<AddIcon />}
          onClick={() => handleOpenDialog()}
        >
          Ajouter une Déclaration
        </Button>
      </Box>

      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Liquide</TableCell>
              <TableCell>Réservoir</TableCell>
              <TableCell>Volume (m³)</TableCell>
              <TableCell>Densité (t/m³)</TableCell>
              <TableCell>Poids (t)</TableCell>
              <TableCell>Date Mesure</TableCell>
              <TableCell>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {statements.map((statement) => {
              const density = statement.measuredDensity || getLiquidDensity(statement.liquidId);
              const weight = statement.volume * density;
              
              return (
                <TableRow key={statement.id}>
                  <TableCell>{getLiquidName(statement.liquidId)}</TableCell>
                  <TableCell>{statement.tankName}</TableCell>
                  <TableCell>{statement.volume.toFixed(2)}</TableCell>
                  <TableCell>{density.toFixed(3)}</TableCell>
                  <TableCell>{weight.toFixed(2)}</TableCell>
                  <TableCell>
                    {new Date(statement.measurementTime).toLocaleString()}
                  </TableCell>
                  <TableCell>
                    <IconButton onClick={() => handleOpenDialog(statement)}>
                      <EditIcon color="primary" />
                    </IconButton>
                    <IconButton onClick={() => handleDelete(statement.id)}>
                      <DeleteIcon color="error" />
                    </IconButton>
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </TableContainer>

      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {currentStatement ? 'Modifier Déclaration' : 'Nouvelle Déclaration'}
        </DialogTitle>
        <DialogContent>
          <Box component="form" onSubmit={handleSubmit} sx={{ mt: 2 }}>
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth margin="normal" required>
                  <InputLabel>Liquide</InputLabel>
                  <Select
                    name="liquidId"
                    value={formData.liquidId}
                    onChange={handleChange}
                    label="Liquide"
                  >
                    {liquids.map((liquid) => (
                      <MenuItem key={liquid.id} value={liquid.id}>
                        {liquid.name} (Densité: {liquid.standardDensity} t/m³)
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  name="tankName"
                  label="Nom du Réservoir"
                  value={formData.tankName}
                  onChange={handleChange}
                  fullWidth
                  margin="normal"
                  required
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  name="volume"
                  label="Volume (m³)"
                  type="number"
                  value={formData.volume}
                  onChange={handleChange}
                  fullWidth
                  margin="normal"
                  required
                  inputProps={{ step: "0.01" }}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  name="measuredDensity"
                  label="Densité Mesurée (t/m³)"
                  type="number"
                  value={formData.measuredDensity}
                  onChange={handleChange}
                  fullWidth
                  margin="normal"
                  inputProps={{ step: "0.001" }}
                  helperText="Laisser vide pour utiliser la densité standard"
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  name="measurementTime"
                  label="Date et Heure de Mesure"
                  type="datetime-local"
                  value={formData.measurementTime}
                  onChange={handleChange}
                  fullWidth
                  margin="normal"
                  required
                  InputLabelProps={{
                    shrink: true,
                  }}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  name="notes"
                  label="Notes"
                  multiline
                  rows={3}
                  value={formData.notes}
                  onChange={handleChange}
                  fullWidth
                  margin="normal"
                />
              </Grid>
            </Grid>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Annuler</Button>
          <Button onClick={handleSubmit} variant="contained">
            {currentStatement ? 'Mettre à jour' : 'Créer'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default LiquidStatementPage;