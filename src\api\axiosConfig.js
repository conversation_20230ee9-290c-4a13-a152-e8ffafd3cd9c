// src/api/axiosInstance.js
import axios from 'axios';
import { logout } from './auth';

const instance = axios.create({
  baseURL: 'http://localhost:5138/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }
});

// 📦 Request Interceptor
instance.interceptors.request.use(config => {
  const authData = getAuthData();
  
  if (authData?.token) {
    // Vérification silencieuse de l'expiration
    if (isTokenExpired(authData)) {
      handleExpiredToken();
      return Promise.reject(new Error('Session expired'));
    }

    config.headers.Authorization = `Bearer ${authData.token}`;
  }

  return config;
}, error => {
  return Promise.reject(error);
});

// ❌ Response Interceptor
instance.interceptors.response.use(
  response => response,
  async error => {
    const originalRequest = error.config;
    
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;
      
      try {
        // Tentative de rafraîchissement du token (si implémenté côté backend)
        const newToken = await tryRefreshToken();
        if (newToken) {
          originalRequest.headers.Authorization = `Bearer ${newToken}`;
          return instance(originalRequest);
        }
      } catch (refreshError) {
        console.error('Refresh token failed:', refreshError);
      }
      
      // Fallback: déconnexion propre
      handleUnauthorized();
    }

    return Promise.reject(error);
  }
);

// 🔒 Helper functions
const getAuthData = () => {
  try {
    const data = localStorage.getItem('auth');
    return data ? JSON.parse(data) : null;
  } catch (e) {
    console.error('Error parsing auth data:', e);
    return null;
  }
};

const isTokenExpired = (authData) => {
  return authData.expiresAt < Date.now();
};

const handleExpiredToken = () => {
  logout();
  if (window.location.pathname !== '/login') {
    window.location.href = '/login?error=session_expired';
  }
};

const handleUnauthorized = () => {
  logout();
  if (window.location.pathname !== '/login') {
    window.location.href = '/login?error=unauthorized';
  }
};

const tryRefreshToken = async () => {
  // Implémentez cette fonction si votre backend supporte le refresh token
  return null;
};

export default instance;