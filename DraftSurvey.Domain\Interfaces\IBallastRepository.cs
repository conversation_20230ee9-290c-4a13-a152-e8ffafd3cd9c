﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using DraftSurvey.Domain.Entities;

namespace DraftSurvey.Domain.Interfaces
{
    public interface IBallastRepository
    {
        Task<Ballast> GetByIdAsync(Guid id);
        Task<IEnumerable<Ballast>> GetBySurveyIdAsync(Guid surveyId);
        Task AddAsync(Ballast ballast);
        Task UpdateAsync(Ballast ballast);
        Task UpdateVolumeAsync(Guid id, double volume);
        Task DeleteAsync(Guid id);
        Task<double> GetTotalVolumeBySurveyAsync(Guid surveyId);
        Task SaveChangesAsync();
    }
}