import api from './axiosConfig';

export const getReadingsBySurvey = async (surveyId) => {
  try {
    const response = await api.get(`/draughtmarkreadings/by-survey/${surveyId}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching readings:', error);
    throw error;
  }
};

export const createReading = async (readingData) => {
  try {
    const response = await api.post('/draughtmarkreadings', readingData);
    return response.data;
  } catch (error) {
    console.error('Error creating reading:', error);
    throw error;
  }
};

export const updateReading = async (surveyId, readingData) => {
  try {
    await api.put(`/draughtmarkreadings/${surveyId}`, readingData);
  } catch (error) {
    console.error('Error updating reading:', error);
    throw error;
  }
};