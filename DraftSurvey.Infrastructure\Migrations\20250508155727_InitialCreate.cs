﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace DraftSurvey.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class InitialCreate : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "DraughtMarkReading",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    ReadingTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    PortForward = table.Column<double>(type: "float", nullable: false),
                    StarboardForward = table.Column<double>(type: "float", nullable: false),
                    PortMidship = table.Column<double>(type: "float", nullable: false),
                    StarboardMidship = table.Column<double>(type: "float", nullable: false),
                    PortAft = table.Column<double>(type: "float", nullable: false),
                    StarboardAft = table.Column<double>(type: "float", nullable: false),
                    ForwardMean = table.Column<double>(type: "float", nullable: false),
                    MidshipMean = table.Column<double>(type: "float", nullable: false),
                    AftMean = table.Column<double>(type: "float", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DraughtMarkReading", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "GlobalCalculation",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Displacement = table.Column<double>(type: "float", nullable: false),
                    FirstTrimCorrection = table.Column<double>(type: "float", nullable: false),
                    SecondTrimCorrection = table.Column<double>(type: "float", nullable: false),
                    NetCargoWeight = table.Column<double>(type: "float", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_GlobalCalculation", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Ports",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Name = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Code = table.Column<string>(type: "nvarchar(10)", maxLength: 10, nullable: false),
                    Country = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    StandardWaterDensity = table.Column<decimal>(type: "decimal(18,6)", nullable: false, defaultValue: 1.025m)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Ports", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Roles",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Name = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Description = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    CanManageUsers = table.Column<bool>(type: "bit", nullable: false),
                    CanManageAllPorts = table.Column<bool>(type: "bit", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Roles", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Vessels",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Name = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    IMONumber = table.Column<string>(type: "nvarchar(15)", maxLength: 15, nullable: false),
                    DisplacementFactor = table.Column<double>(type: "float", nullable: false),
                    TPC = table.Column<double>(type: "float", nullable: false),
                    MomentToChangeTrim = table.Column<double>(type: "float", nullable: false),
                    LengthBetweenPerpendiculars = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    LongitudinalCenterOfFloatation = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    DeadweightTonnage = table.Column<decimal>(type: "decimal(18,2)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Vessels", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Users",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Username = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    PasswordHash = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    Email = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    FullName = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    IsActive = table.Column<bool>(type: "bit", nullable: false, defaultValue: true),
                    RoleId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    PortId = table.Column<Guid>(type: "uniqueidentifier", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Users", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Users_Ports_PortId",
                        column: x => x.PortId,
                        principalTable: "Ports",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Users_Roles_RoleId",
                        column: x => x.RoleId,
                        principalTable: "Roles",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "Escales",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Reference = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    ArrivalDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    DepartureDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    VesselId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    PortId = table.Column<Guid>(type: "uniqueidentifier", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Escales", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Escales_Ports_PortId",
                        column: x => x.PortId,
                        principalTable: "Ports",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Escales_Vessels_VesselId",
                        column: x => x.VesselId,
                        principalTable: "Vessels",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "DraftSurveys",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    SurveyNumber = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    Status = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    VesselId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    EscaleId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    DraughtMarkReadingId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    GlobalCalculationId = table.Column<Guid>(type: "uniqueidentifier", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DraftSurveys", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DraftSurveys_DraughtMarkReading_DraughtMarkReadingId",
                        column: x => x.DraughtMarkReadingId,
                        principalTable: "DraughtMarkReading",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_DraftSurveys_Escales_EscaleId",
                        column: x => x.EscaleId,
                        principalTable: "Escales",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_DraftSurveys_GlobalCalculation_GlobalCalculationId",
                        column: x => x.GlobalCalculationId,
                        principalTable: "GlobalCalculation",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_DraftSurveys_Vessels_VesselId",
                        column: x => x.VesselId,
                        principalTable: "Vessels",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "Inspection",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    InspectionDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    InspectorName = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Notes = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    EscaleId = table.Column<Guid>(type: "uniqueidentifier", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Inspection", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Inspection_Escales_EscaleId",
                        column: x => x.EscaleId,
                        principalTable: "Escales",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "Ballast",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    TankName = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Volume = table.Column<double>(type: "float", nullable: false),
                    Density = table.Column<double>(type: "float", nullable: false),
                    DraftSurveyId = table.Column<Guid>(type: "uniqueidentifier", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Ballast", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Ballast_DraftSurveys_DraftSurveyId",
                        column: x => x.DraftSurveyId,
                        principalTable: "DraftSurveys",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "Document",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    DraftSurveyId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Name = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    FilePath = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    UploadDate = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Document", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Document_DraftSurveys_DraftSurveyId",
                        column: x => x.DraftSurveyId,
                        principalTable: "DraftSurveys",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "FreshWaterTank",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    TankName = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    TotalCapacity = table.Column<double>(type: "float", nullable: false),
                    CurrentVolume = table.Column<double>(type: "float", nullable: false),
                    Temperature = table.Column<double>(type: "float", nullable: false),
                    DraftSurveyId = table.Column<Guid>(type: "uniqueidentifier", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_FreshWaterTank", x => x.Id);
                    table.ForeignKey(
                        name: "FK_FreshWaterTank_DraftSurveys_DraftSurveyId",
                        column: x => x.DraftSurveyId,
                        principalTable: "DraftSurveys",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "SeaWaterDensity",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    MeasurementTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    Density = table.Column<double>(type: "float", nullable: false),
                    Temperature = table.Column<double>(type: "float", nullable: false),
                    MeasurementMethod = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    DraftSurveyId = table.Column<Guid>(type: "uniqueidentifier", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SeaWaterDensity", x => x.Id);
                    table.ForeignKey(
                        name: "FK_SeaWaterDensity_DraftSurveys_DraftSurveyId",
                        column: x => x.DraftSurveyId,
                        principalTable: "DraftSurveys",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "TaskAssignment",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    AssignmentDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CompletionDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    TaskType = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Notes = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    DraftSurveyId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    UserId = table.Column<Guid>(type: "uniqueidentifier", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TaskAssignment", x => x.Id);
                    table.ForeignKey(
                        name: "FK_TaskAssignment_DraftSurveys_DraftSurveyId",
                        column: x => x.DraftSurveyId,
                        principalTable: "DraftSurveys",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_TaskAssignment_Users_UserId",
                        column: x => x.UserId,
                        principalTable: "Users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "HoldInspection",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    HoldName = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    IsClean = table.Column<bool>(type: "bit", nullable: false),
                    IsDry = table.Column<bool>(type: "bit", nullable: false),
                    Comments = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    InspectionId = table.Column<Guid>(type: "uniqueidentifier", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_HoldInspection", x => x.Id);
                    table.ForeignKey(
                        name: "FK_HoldInspection_Inspection_InspectionId",
                        column: x => x.InspectionId,
                        principalTable: "Inspection",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_Ballast_DraftSurveyId",
                table: "Ballast",
                column: "DraftSurveyId");

            migrationBuilder.CreateIndex(
                name: "IX_Document_DraftSurveyId",
                table: "Document",
                column: "DraftSurveyId");

            migrationBuilder.CreateIndex(
                name: "IX_DraftSurveys_DraughtMarkReadingId",
                table: "DraftSurveys",
                column: "DraughtMarkReadingId");

            migrationBuilder.CreateIndex(
                name: "IX_DraftSurveys_EscaleId",
                table: "DraftSurveys",
                column: "EscaleId");

            migrationBuilder.CreateIndex(
                name: "IX_DraftSurveys_GlobalCalculationId",
                table: "DraftSurveys",
                column: "GlobalCalculationId");

            migrationBuilder.CreateIndex(
                name: "IX_DraftSurveys_VesselId",
                table: "DraftSurveys",
                column: "VesselId");

            migrationBuilder.CreateIndex(
                name: "IX_Escales_PortId",
                table: "Escales",
                column: "PortId");

            migrationBuilder.CreateIndex(
                name: "IX_Escales_VesselId",
                table: "Escales",
                column: "VesselId");

            migrationBuilder.CreateIndex(
                name: "IX_FreshWaterTank_DraftSurveyId",
                table: "FreshWaterTank",
                column: "DraftSurveyId");

            migrationBuilder.CreateIndex(
                name: "IX_HoldInspection_InspectionId",
                table: "HoldInspection",
                column: "InspectionId");

            migrationBuilder.CreateIndex(
                name: "IX_Inspection_EscaleId",
                table: "Inspection",
                column: "EscaleId");

            migrationBuilder.CreateIndex(
                name: "IX_SeaWaterDensity_DraftSurveyId",
                table: "SeaWaterDensity",
                column: "DraftSurveyId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_TaskAssignment_DraftSurveyId",
                table: "TaskAssignment",
                column: "DraftSurveyId");

            migrationBuilder.CreateIndex(
                name: "IX_TaskAssignment_UserId",
                table: "TaskAssignment",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_Users_PortId",
                table: "Users",
                column: "PortId");

            migrationBuilder.CreateIndex(
                name: "IX_Users_RoleId",
                table: "Users",
                column: "RoleId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "Ballast");

            migrationBuilder.DropTable(
                name: "Document");

            migrationBuilder.DropTable(
                name: "FreshWaterTank");

            migrationBuilder.DropTable(
                name: "HoldInspection");

            migrationBuilder.DropTable(
                name: "SeaWaterDensity");

            migrationBuilder.DropTable(
                name: "TaskAssignment");

            migrationBuilder.DropTable(
                name: "Inspection");

            migrationBuilder.DropTable(
                name: "DraftSurveys");

            migrationBuilder.DropTable(
                name: "Users");

            migrationBuilder.DropTable(
                name: "DraughtMarkReading");

            migrationBuilder.DropTable(
                name: "Escales");

            migrationBuilder.DropTable(
                name: "GlobalCalculation");

            migrationBuilder.DropTable(
                name: "Roles");

            migrationBuilder.DropTable(
                name: "Ports");

            migrationBuilder.DropTable(
                name: "Vessels");
        }
    }
}
