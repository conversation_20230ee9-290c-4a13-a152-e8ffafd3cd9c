import React, { useState, useEffect } from "react";
import { 
  <PERSON>, 
  Typo<PERSON>, 
  Button,
  CircularProgress,
  Snackbar,
  Alert,
  useTheme, // Added useTheme for consistent styling
  useMediaQuery // Added useMediaQuery for responsive design if needed
} from "@mui/material";
import DraftList from "../components/Drafts/DraftList";
import DraftEditPopup from "../components/Drafts/DraftEditPopup"; // Assuming this is for editing drafts
import DeleteConfirmationPopup from "../components/Drafts/DeleteConfirmationPopup"; // Assuming this is for delete confirmation
import DraftCreatePopup from "../components/Drafts/DraftCreatePopup"; // Explicitly include DraftCreatePopup
import { 
  getSurveys, 
  createSurveyWithDetails, 
  updateSurveyDetails, 
  deleteSurvey 
} from "../api/draftApi"; 
import Navbar from "../components/Navbar/Navbar"; 

const DraftsPage = () => {
  const [drafts, setDrafts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [openCreate, setOpenCreate] = useState(false); // State for opening create popup
  const [openEdit, setOpenEdit] = useState(false);
  const [openDelete, setOpenDelete] = useState(false);
  const [currentDraft, setCurrentDraft] = useState(null);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success'
  });

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

  useEffect(() => {
    fetchDrafts();
  }, []);

  const fetchDrafts = async () => {
    try {
      setLoading(true);
      const surveys = await getSurveys();
      setDrafts(surveys);
    } catch (error) {
      showSnackbar('Erreur lors du chargement des drafts', 'error');
    } finally {
      setLoading(false);
    }
  };

  const showSnackbar = (message, severity = 'success') => {
    setSnackbar({ open: true, message, severity });
  };

  const handleCreate = () => {
    setCurrentDraft(null); // No current draft means creating new
    setOpenCreate(true); // Open the dedicated create popup
  };

  const handleEdit = (draft) => {
    setCurrentDraft(draft);
    setOpenEdit(true);
  };

  const handleDeleteClick = (draft) => {
    setCurrentDraft(draft);
    setOpenDelete(true);
  };

  const handleDeleteConfirm = async () => {
    try {
      await deleteSurvey(currentDraft.id);
      setDrafts(drafts.filter(d => d.id !== currentDraft.id));
      showSnackbar('Draft supprimé avec succès');
    } catch (error) {
      showSnackbar('Erreur lors de la suppression', 'error');
    } finally {
      setOpenDelete(false);
    }
  };

  const handleSaveDraft = async (formData) => {
    try {
      if (currentDraft) {
        // Update existing draft (for DraftEditPopup)
        await updateSurveyDetails(currentDraft.id, formData);
        showSnackbar('Draft mis à jour avec succès');
        setOpenEdit(false);
      } else {
        // Create new draft (for DraftCreatePopup)
        const newDraft = await createSurveyWithDetails(formData); // Passing full formData
        setDrafts([...drafts, newDraft]);
        showSnackbar('Draft créé avec succès');
        setOpenCreate(false); // Close the create popup after successful creation
      }
      fetchDrafts(); // Re-fetch all drafts to get updated list and details
    } catch (error) {
      showSnackbar(
        currentDraft 
          ? 'Erreur lors de la mise à jour' 
          : 'Erreur lors de la création',
        'error'
      );
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="100vh"
        sx={{ bgcolor: theme.palette.background.default }}
      >
        <CircularProgress sx={{ color: theme.palette.primary.main }} />
      </Box>
    );
  }

  return (
    <Box sx={{ display: "flex", flexDirection: "column", minHeight: "100vh", bgcolor: theme.palette.background.default }}>
      <Navbar />

      <Box
        sx={{
          flexGrow: 1,
          pt: { xs: '72px', sm: '80px', md: '88px' }, // Adjust padding for Navbar
          pb: 4,
          px: { xs: 2, sm: 4, md: 6 },
          display: "flex",
          justifyContent: "center",
          alignItems: "flex-start",
          bgcolor: theme.palette.grey[100], // Light grey background for content area
        }}
      >
        <Box
          sx={{
            width: isMobile ? "100%" : 1200,
            maxWidth: "100%",
            padding: isMobile ? 2 : 4,
            borderRadius: theme.shape.borderRadius * 2,
            boxShadow: theme.shadows[8], // Consistent shadow with UserManagementPage
            bgcolor: "white",
            color: theme.palette.text.primary,
            border: `1px solid ${theme.palette.grey[300]}`,
          }}
        >
          <Typography
            variant={isMobile ? "h5" : "h4"}
            sx={{
              textAlign: "left",
              color: theme.palette.text.primary,
              fontWeight: "bold",
              mb: 3,
            }}
          >
            Tous les drafts
          </Typography>
          
          <Button 
            variant="contained" 
            onClick={handleCreate}
            sx={{
              bgcolor: theme.palette.success.main, // Consistent green button
              color: 'white',
              fontWeight: "bold",
              "&:hover": {
                bgcolor: theme.palette.success.dark,
                transform: "translateY(-2px)", // Subtle hover effect
                boxShadow: theme.shadows[4],
              },
              mb: 3,
              px: isMobile ? 2 : 3,
              py: isMobile ? 1 : 1.5,
              borderRadius: theme.shape.borderRadius,
            }}
          >
            Créer un nouveau draft
          </Button>

          <DraftList 
            drafts={drafts}
            onEdit={handleEdit}
            onDelete={handleDeleteClick}
            // Ensure onView and onAssignTasks are also passed if needed
            // onView={handleViewDraft}
            // onAssignTasks={handleAssignTasks}
          />

          {/* New DraftCreatePopup for adding new drafts */}
          <DraftCreatePopup
            open={openCreate}
            onClose={() => setOpenCreate(false)}
            onCreate={handleSaveDraft} // Use handleSaveDraft for creation
          />

          {/* DraftEditPopup (for existing drafts) */}
          <DraftEditPopup
            open={openEdit}
            onClose={() => setOpenEdit(false)}
            onSave={handleSaveDraft}
            draft={currentDraft}
          />

          <DeleteConfirmationPopup
            open={openDelete}
            onClose={() => setOpenDelete(false)}
            onConfirm={handleDeleteConfirm}
            itemName={currentDraft?.surveyNumber || ''}
          />

          <Snackbar
            open={snackbar.open}
            autoHideDuration={6000}
            onClose={() => setSnackbar({ ...snackbar, open: false })}
            anchorOrigin={{ vertical: 'bottom', horizontal: 'left' }}
          >
            <Alert 
              onClose={() => setSnackbar({ ...snackbar, open: false })} 
              severity={snackbar.severity}
              sx={{ width: '100%' }}
            >
              {snackbar.message}
            </Alert>
          </Snackbar>
        </Box>
      </Box>
    </Box>
  );
};

export default DraftsPage;