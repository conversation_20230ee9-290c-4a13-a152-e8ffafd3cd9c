[{"ContainingType": "DraftSurvey.WebApi.Controllers.AuthController", "Method": "<PERSON><PERSON>", "RelativePath": "api/Auth/login", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "DraftSurvey.WebApi.Controllers.LoginRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "DraftSurvey.WebApi.Controllers.AuthController", "Method": "ValidateToken", "RelativePath": "api/Auth/validate", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "DraftSurvey.WebApi.Controllers.BallastsController", "Method": "Create", "RelativePath": "api/Ballasts", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "DraftSurvey.Application.DTOs.BallastDto", "IsRequired": true}], "ReturnTypes": [{"Type": "DraftSurvey.Application.DTOs.BallastDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "DraftSurvey.WebApi.Controllers.BallastsController", "Method": "GetById", "RelativePath": "api/Ballasts/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "DraftSurvey.Application.DTOs.BallastDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "DraftSurvey.WebApi.Controllers.BallastsController", "Method": "Update", "RelativePath": "api/Ballasts/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "dto", "Type": "DraftSurvey.Application.DTOs.BallastDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "DraftSurvey.WebApi.Controllers.BallastsController", "Method": "Delete", "RelativePath": "api/Ballasts/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "DraftSurvey.WebApi.Controllers.BallastsController", "Method": "GetBySurvey", "RelativePath": "api/Ballasts/by-survey/{surveyId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "surveyId", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[DraftSurvey.Application.DTOs.BallastDto, DraftSurvey.Application, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "DraftSurvey.WebApi.Controllers.DraughtMarkReadingsController", "Method": "Create", "RelativePath": "api/DraughtMarkReadings", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "DraftSurvey.Application.DTOs.DraughtMarkReadingDto", "IsRequired": true}], "ReturnTypes": [{"Type": "DraftSurvey.Application.DTOs.DraughtMarkReadingDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "DraftSurvey.WebApi.Controllers.DraughtMarkReadingsController", "Method": "Update", "RelativePath": "api/DraughtMarkReadings/{surveyId}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "surveyId", "Type": "System.Guid", "IsRequired": true}, {"Name": "dto", "Type": "DraftSurvey.Application.DTOs.DraughtMarkReadingDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "DraftSurvey.WebApi.Controllers.DraughtMarkReadingsController", "Method": "GetBySurvey", "RelativePath": "api/DraughtMarkReadings/by-survey/{surveyId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "surveyId", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "DraftSurvey.Application.DTOs.DraughtMarkReadingDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "DraftSurvey.WebApi.Controllers.EscaleController", "Method": "GetAll", "RelativePath": "api/escales", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[DraftSurvey.Application.DTOs.EscaleDto, DraftSurvey.Application, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "DraftSurvey.WebApi.Controllers.EscaleController", "Method": "Create", "RelativePath": "api/escales", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "DraftSurvey.Application.DTOs.EscaleCreateDto", "IsRequired": true}], "ReturnTypes": [{"Type": "DraftSurvey.Application.DTOs.EscaleDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "DraftSurvey.WebApi.Controllers.EscaleController", "Method": "GetById", "RelativePath": "api/escales/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "DraftSurvey.Application.DTOs.EscaleDetailsDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "DraftSurvey.WebApi.Controllers.EscaleController", "Method": "Complete", "RelativePath": "api/escales/{id}/complete", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "DraftSurvey.WebApi.Controllers.EscaleController", "Method": "GetActive", "RelativePath": "api/escales/active", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[DraftSurvey.Application.DTOs.EscaleDto, DraftSurvey.Application, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "DraftSurvey.WebApi.Controllers.EscaleController", "Method": "GetByPort", "RelativePath": "api/escales/by-port/{portId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "portId", "Type": "System.Guid", "IsRequired": true}, {"Name": "startDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "endDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[DraftSurvey.Application.DTOs.EscaleDto, DraftSurvey.Application, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "DraftSurvey.WebApi.Controllers.PortsController", "Method": "GetAll", "RelativePath": "api/ports", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[DraftSurvey.Application.DTOs.PortDto, DraftSurvey.Application, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "DraftSurvey.WebApi.Controllers.PortsController", "Method": "Create", "RelativePath": "api/ports", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "DraftSurvey.Application.DTOs.PortDto", "IsRequired": true}], "ReturnTypes": [{"Type": "DraftSurvey.Application.DTOs.PortDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "DraftSurvey.WebApi.Controllers.PortsController", "Method": "GetById", "RelativePath": "api/ports/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "DraftSurvey.Application.DTOs.PortDetailsDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "DraftSurvey.WebApi.Controllers.PortsController", "Method": "Update", "RelativePath": "api/ports/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "dto", "Type": "DraftSurvey.Application.DTOs.PortDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "DraftSurvey.WebApi.Controllers.PortsController", "Method": "Delete", "RelativePath": "api/ports/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "DraftSurvey.WebApi.Controllers.PortsController", "Method": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "RelativePath": "api/ports/{id}/assign-manager/{userId}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "userId", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "DraftSurvey.WebApi.Controllers.PortsController", "Method": "GetStaff", "RelativePath": "api/ports/{id}/staff", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[DraftSurvey.Application.DTOs.UserDto, DraftSurvey.Application, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "DraftSurvey.WebApi.Controllers.PortsController", "Method": "UpdateWaterDensity", "RelativePath": "api/ports/{id}/water-density", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "waterDensity", "Type": "System.Double", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "DraftSurvey.WebApi.Controllers.PortsController", "Method": "Search", "RelativePath": "api/ports/search", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "term", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[DraftSurvey.Application.DTOs.PortDto, DraftSurvey.Application, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "DraftSurvey.WebApi.Controllers.RoleController", "Method": "GetAllRoles", "RelativePath": "api/Role", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[DraftSurvey.Application.DTOs.RoleDto, DraftSurvey.Application, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "DraftSurvey.WebApi.Controllers.RoleController", "Method": "CreateRole", "RelativePath": "api/Role", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "roleCreateDto", "Type": "DraftSurvey.Application.DTOs.RoleCreateDto", "IsRequired": true}], "ReturnTypes": [{"Type": "DraftSurvey.Application.DTOs.RoleDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "DraftSurvey.WebApi.Controllers.RoleController", "Method": "GetRoleById", "RelativePath": "api/Role/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "DraftSurvey.Application.DTOs.RoleDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "DraftSurvey.WebApi.Controllers.RoleController", "Method": "UpdateRole", "RelativePath": "api/Role/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "roleUpdateDto", "Type": "DraftSurvey.Application.DTOs.RoleUpdateDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "DraftSurvey.WebApi.Controllers.RoleController", "Method": "DeleteRole", "RelativePath": "api/Role/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "DraftSurvey.WebApi.Controllers.SurveysController", "Method": "Create", "RelativePath": "api/surveys", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "DraftSurvey.Application.DTOs.SurveyCreateDto", "IsRequired": true}], "ReturnTypes": [{"Type": "DraftSurvey.Application.DTOs.SurveyDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "DraftSurvey.WebApi.Controllers.SurveysController", "Method": "GetById", "RelativePath": "api/surveys/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "DraftSurvey.Application.DTOs.SurveyDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "DraftSurvey.WebApi.Controllers.SurveysController", "Method": "GetCargoWeight", "RelativePath": "api/surveys/{id}/cargo-weight", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "DraftSurvey.WebApi.Controllers.SurveysController+CargoWeightResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "DraftSurvey.WebApi.Controllers.SurveysController", "Method": "Finalize", "RelativePath": "api/surveys/{id}/finalize", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "DraftSurvey.WebApi.Controllers.SurveysController", "Method": "Recalculate", "RelativePath": "api/surveys/{id}/recalculate", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "DraftSurvey.WebApi.Controllers.UsersController", "Method": "GetAll", "RelativePath": "api/users", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "DraftSurvey.WebApi.Controllers.UsersController", "Method": "Create", "RelativePath": "api/users", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "DraftSurvey.Application.DTOs.UserCreateDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "DraftSurvey.WebApi.Controllers.UsersController", "Method": "GetById", "RelativePath": "api/users/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "DraftSurvey.WebApi.Controllers.UsersController", "Method": "Update", "RelativePath": "api/users/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "dto", "Type": "DraftSurvey.Application.DTOs.UserUpdateDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "DraftSurvey.WebApi.Controllers.UsersController", "Method": "Delete", "RelativePath": "api/users/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "DraftSurvey.WebApi.Controllers.UsersController", "Method": "ChangePassword", "RelativePath": "api/users/{id}/change-password", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "dto", "Type": "DraftSurvey.Application.DTOs.ChangePasswordDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "DraftSurvey.WebApi.Controllers.UsersController", "Method": "GetByPort", "RelativePath": "api/users/by-port/{portId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "portId", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "DraftSurvey.WebApi.Controllers.UsersController", "Method": "GetByRole", "RelativePath": "api/users/by-role/{roleName}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "<PERSON><PERSON><PERSON>", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "DraftSurvey.WebApi.Controllers.VesselsController", "Method": "GetAll", "RelativePath": "api/vessels", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[DraftSurvey.Application.DTOs.VesselDto, DraftSurvey.Application, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "DraftSurvey.WebApi.Controllers.VesselsController", "Method": "Create", "RelativePath": "api/vessels", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "DraftSurvey.Application.DTOs.VesselCreateDto", "IsRequired": true}], "ReturnTypes": [{"Type": "DraftSurvey.Application.DTOs.VesselDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "DraftSurvey.WebApi.Controllers.VesselsController", "Method": "GetById", "RelativePath": "api/vessels/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "DraftSurvey.Application.DTOs.VesselDetailsDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "DraftSurvey.WebApi.Controllers.VesselsController", "Method": "Update", "RelativePath": "api/vessels/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "dto", "Type": "DraftSurvey.Application.DTOs.VesselUpdateDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "DraftSurvey.WebApi.Controllers.VesselsController", "Method": "Delete", "RelativePath": "api/vessels/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "DraftSurvey.WebApi.Controllers.VesselsController", "Method": "UpdateHydrostaticData", "RelativePath": "api/vessels/{id}/hydrostatic", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "data", "Type": "DraftSurvey.WebApi.Controllers.HydrostaticDataDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "DraftSurvey.WebApi.Controllers.VesselsController", "Method": "GetByImo", "RelativePath": "api/vessels/imo/{imoNumber}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "imoNumber", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "DraftSurvey.Application.DTOs.VesselDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "DraftSurvey.WebApi.Controllers.VesselsController", "Method": "Search", "RelativePath": "api/vessels/search", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "name", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[DraftSurvey.Application.DTOs.VesselDto, DraftSurvey.Application, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}]