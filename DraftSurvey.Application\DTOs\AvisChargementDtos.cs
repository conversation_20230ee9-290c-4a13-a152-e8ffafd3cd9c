﻿// DraftSurvey.Application/DTOs/AvisChargementDtos.cs
using System;
using System.ComponentModel.DataAnnotations;

namespace DraftSurvey.Application.DTOs
{
    public class AvisChargementDto
    {
        public Guid Id { get; set; }
        public DateTime NoticeDate { get; set; }
        public string CargoType { get; set; }
        public double Quantity { get; set; }
        public string ReferenceNumber { get; set; }
        public Guid EscaleId { get; set; }
    }

    public class AvisChargementCreateDto
    {
        [Required]
        public DateTime NoticeDate { get; set; }
        [Required]
        public string CargoType { get; set; }
        [Required]
        public double Quantity { get; set; }
        public string ReferenceNumber { get; set; }
        [Required]
        public Guid EscaleId { get; set; }
    }

    public class AvisChargementUpdateDto
    {
        [Required]
        public DateTime NoticeDate { get; set; }
        [Required]
        public string CargoType { get; set; }
        [Required]
        public double Quantity { get; set; }
        public string ReferenceNumber { get; set; }
        // EscaleId usually not updated after creation
    }
}