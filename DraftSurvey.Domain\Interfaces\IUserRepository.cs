using DraftSurvey.Domain.Entities;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace DraftSurvey.Domain.Interfaces
{
    public interface IUserRepository
    {
        // CRUD Operations
        Task<User> GetByIdAsync(Guid id);
        Task<IEnumerable<User>> GetAllAsync();
        Task<User> CreateUserAsync(User user, string password);
        Task UpdateUserAsync(User user);
        Task DeleteUserAsync(Guid id);

        // Authentication
        Task<User> AuthenticateAsync(string username, string password);
        Task ChangePasswordAsync(Guid userId, string newPassword);

        // Role Management
        Task AssignRoleAsync(Guid userId, Guid roleId);
        Task<bool> UserHasPermission(Guid userId, string permission);

        // Search Operations
        Task<User> GetByUsernameAsync(string username);
        Task<IEnumerable<User>> GetUsersByRoleAsync(string roleName);
        Task<IEnumerable<User>> GetUsersByPortAsync(Guid portId);
    }
}