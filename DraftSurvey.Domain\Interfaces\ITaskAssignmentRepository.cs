﻿using DraftSurvey.Domain.Entities;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace DraftSurvey.Domain.Interfaces
{
    public interface ITaskAssignmentRepository
    {
        Task<TaskAssignment> GetByIdAsync(Guid id);
        Task<IEnumerable<TaskAssignment>> GetAllAsync(string? include = null); // <--- modifié ici
        Task<IEnumerable<TaskAssignment>> GetByUserIdAsync(Guid userId);
        Task<IEnumerable<TaskAssignment>> GetByDraftSurveyIdAsync(Guid draftSurveyId);
        Task<TaskAssignment> AddAsync(TaskAssignment taskAssignment);
        Task UpdateAsync(TaskAssignment taskAssignment);
        Task DeleteAsync(Guid id);
        Task SaveChangesAsync();
    }
}
