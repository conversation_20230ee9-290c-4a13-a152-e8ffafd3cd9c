import instance from "./axiosConfig";

const API_URL = '/Role';

export const getRoles = async () => {
  try {
    const response = await instance.get(API_URL);
    return response.data;
  } catch (error) {
    console.error("Error fetching roles:", error);
    throw error;
  }
};

export const getRoleById = async (id) => {
  const response = await instance.get(`${API_URL}/${id}`);
  return response.data;
};

export const createRole = async (roleData) => {
  const response = await instance.post(API_URL, roleData);
  return response.data;
};

export const updateRole = async (id, roleData) => {
  await instance.put(`${API_URL}/${id}`, roleData);
};

export const deleteRole = async (id) => {
  await instance.delete(`${API_URL}/${id}`);
};