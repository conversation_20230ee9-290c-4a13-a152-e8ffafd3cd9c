import api from './axiosConfig';

export const getDensityBySurvey = async (surveyId) => {
  try {
    const response = await api.get(`/seawaterdensitystatements/by-survey/${surveyId}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching sea water density:', error);
    throw error;
  }
};

export const createDensityStatement = async (densityData) => {
  try {
    const response = await api.post('/seawaterdensitystatements', densityData);
    return response.data;
  } catch (error) {
    console.error('Error creating density statement:', error);
    throw error;
  }
};

export const updateDensityStatement = async (id, densityData) => {
  try {
    await api.put(`/seawaterdensitystatements/${id}`, densityData);
  } catch (error) {
    console.error('Error updating density statement:', error);
    throw error;
  }
};