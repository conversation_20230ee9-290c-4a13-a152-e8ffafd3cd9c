﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace DraftSurvey.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class UpdateAllEntityRelationships : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Document_DraftSurveys_DraftSurveyId",
                table: "Document");

            migrationBuilder.DropForeignKey(
                name: "FK_DraftSurveys_GlobalCalculation_GlobalCalculationId",
                table: "DraftSurveys");

            migrationBuilder.DropForeignKey(
                name: "FK_FreshWaterTank_DraftSurveys_DraftSurveyId",
                table: "FreshWaterTank");

            migrationBuilder.DropForeignKey(
                name: "FK_HoldInspection_Inspection_InspectionId",
                table: "HoldInspection");

            migrationBuilder.DropForeignKey(
                name: "FK_LiquidStatements_Liquid_LiquidId",
                table: "LiquidStatements");

            migrationBuilder.DropTable(
                name: "GlobalCalculation");

            migrationBuilder.DropTable(
                name: "SeaWaterDensity");

            migrationBuilder.DropIndex(
                name: "IX_DraftSurveys_GlobalCalculationId",
                table: "DraftSurveys");

            migrationBuilder.DropPrimaryKey(
                name: "PK_Liquid",
                table: "Liquid");

            migrationBuilder.DropPrimaryKey(
                name: "PK_HoldInspection",
                table: "HoldInspection");

            migrationBuilder.DropPrimaryKey(
                name: "PK_FreshWaterTank",
                table: "FreshWaterTank");

            migrationBuilder.DropPrimaryKey(
                name: "PK_Document",
                table: "Document");

            migrationBuilder.DropColumn(
                name: "GlobalCalculationId",
                table: "DraftSurveys");

            migrationBuilder.DropColumn(
                name: "Comments",
                table: "HoldInspection");

            migrationBuilder.DropColumn(
                name: "HoldName",
                table: "HoldInspection");

            migrationBuilder.DropColumn(
                name: "IsClean",
                table: "HoldInspection");

            migrationBuilder.DropColumn(
                name: "CurrentVolume",
                table: "FreshWaterTank");

            migrationBuilder.RenameTable(
                name: "Liquid",
                newName: "Liquids");

            migrationBuilder.RenameTable(
                name: "HoldInspection",
                newName: "HoldInspections");

            migrationBuilder.RenameTable(
                name: "FreshWaterTank",
                newName: "FreshWaterTanks");

            migrationBuilder.RenameTable(
                name: "Document",
                newName: "Documents");

            migrationBuilder.RenameColumn(
                name: "MomentToChangeTrim",
                table: "Vessels",
                newName: "MTC");

            migrationBuilder.RenameColumn(
                name: "LongitudinalCenterOfFloatation",
                table: "Vessels",
                newName: "SummerDeadweight");

            migrationBuilder.RenameColumn(
                name: "DeadweightTonnage",
                table: "Vessels",
                newName: "LCF");

            migrationBuilder.RenameColumn(
                name: "IsDry",
                table: "HoldInspections",
                newName: "IsApproved");

            migrationBuilder.RenameIndex(
                name: "IX_HoldInspection_InspectionId",
                table: "HoldInspections",
                newName: "IX_HoldInspections_InspectionId");

            migrationBuilder.RenameColumn(
                name: "TotalCapacity",
                table: "FreshWaterTanks",
                newName: "Volume");

            migrationBuilder.RenameColumn(
                name: "Temperature",
                table: "FreshWaterTanks",
                newName: "Density");

            migrationBuilder.RenameColumn(
                name: "DraftSurveyId",
                table: "FreshWaterTanks",
                newName: "SurveyId");

            migrationBuilder.RenameIndex(
                name: "IX_FreshWaterTank_DraftSurveyId",
                table: "FreshWaterTanks",
                newName: "IX_FreshWaterTanks_SurveyId");

            migrationBuilder.RenameColumn(
                name: "Name",
                table: "Documents",
                newName: "Description");

            migrationBuilder.RenameColumn(
                name: "DraftSurveyId",
                table: "Documents",
                newName: "UploadedByUserId");

            migrationBuilder.RenameIndex(
                name: "IX_Document_DraftSurveyId",
                table: "Documents",
                newName: "IX_Documents_UploadedByUserId");

            migrationBuilder.AddColumn<double>(
                name: "BreadthMoulded",
                table: "Vessels",
                type: "float",
                nullable: false,
                defaultValue: 0.0);

            migrationBuilder.AddColumn<string>(
                name: "CallSign",
                table: "Vessels",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<double>(
                name: "DepthMoulded",
                table: "Vessels",
                type: "float",
                nullable: false,
                defaultValue: 0.0);

            migrationBuilder.AddColumn<double>(
                name: "DisplacementAtDesignDraft",
                table: "Vessels",
                type: "float",
                nullable: false,
                defaultValue: 0.0);

            migrationBuilder.AddColumn<string>(
                name: "Flag",
                table: "Vessels",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<double>(
                name: "LCG",
                table: "Vessels",
                type: "float",
                nullable: false,
                defaultValue: 0.0);

            migrationBuilder.AddColumn<double>(
                name: "LengthOverall",
                table: "Vessels",
                type: "float",
                nullable: false,
                defaultValue: 0.0);

            migrationBuilder.AddColumn<double>(
                name: "LightDisplacement",
                table: "Vessels",
                type: "float",
                nullable: false,
                defaultValue: 0.0);

            migrationBuilder.AddColumn<string>(
                name: "Notes",
                table: "Vessels",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "Type",
                table: "Vessels",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "Notes",
                table: "LiquidStatements",
                type: "nvarchar(500)",
                maxLength: 500,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "TankName",
                table: "LiquidStatements",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AlterColumn<string>(
                name: "Type",
                table: "Liquids",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: false,
                oldClrType: typeof(int),
                oldType: "int");

            migrationBuilder.AlterColumn<string>(
                name: "Name",
                table: "Liquids",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)");

            migrationBuilder.AlterColumn<string>(
                name: "Description",
                table: "Liquids",
                type: "nvarchar(500)",
                maxLength: 500,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)");

            migrationBuilder.AddColumn<bool>(
                name: "IsHazardous",
                table: "Liquids",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<string>(
                name: "Unit",
                table: "Liquids",
                type: "nvarchar(20)",
                maxLength: 20,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "Condition",
                table: "HoldInspections",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<int>(
                name: "HoldNumber",
                table: "HoldInspections",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<string>(
                name: "Notes",
                table: "HoldInspections",
                type: "nvarchar(500)",
                maxLength: 500,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "ReasonForRejection",
                table: "HoldInspections",
                type: "nvarchar(500)",
                maxLength: 500,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AlterColumn<string>(
                name: "TankName",
                table: "FreshWaterTanks",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)");

            migrationBuilder.AddColumn<DateTime>(
                name: "MeasurementTime",
                table: "FreshWaterTanks",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<string>(
                name: "Notes",
                table: "FreshWaterTanks",
                type: "nvarchar(500)",
                maxLength: 500,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AlterColumn<string>(
                name: "FilePath",
                table: "Documents",
                type: "nvarchar(500)",
                maxLength: 500,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)");

            migrationBuilder.AddColumn<string>(
                name: "ContentType",
                table: "Documents",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "DocumentType",
                table: "Documents",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "FileName",
                table: "Documents",
                type: "nvarchar(255)",
                maxLength: 255,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<long>(
                name: "FileSize",
                table: "Documents",
                type: "bigint",
                nullable: false,
                defaultValue: 0L);

            migrationBuilder.AddPrimaryKey(
                name: "PK_Liquids",
                table: "Liquids",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_HoldInspections",
                table: "HoldInspections",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_FreshWaterTanks",
                table: "FreshWaterTanks",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_Documents",
                table: "Documents",
                column: "Id");

            migrationBuilder.CreateTable(
                name: "AvisChargements",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    EscaleId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    CargoName = table.Column<string>(type: "nvarchar(250)", maxLength: 250, nullable: false),
                    QuantityExpected = table.Column<double>(type: "float", nullable: false),
                    Unit = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    ExpectedLoadingDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    ActualLoadingDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LoadingPort = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    DischargingPort = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Status = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    Notes = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: false),
                    CreatedByUserId = table.Column<Guid>(type: "uniqueidentifier", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AvisChargements", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AvisChargements_Escales_EscaleId",
                        column: x => x.EscaleId,
                        principalTable: "Escales",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_AvisChargements_Users_CreatedByUserId",
                        column: x => x.CreatedByUserId,
                        principalTable: "Users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "DraughtMarksCorrections",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    DraftSurveyId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    CorrectionType = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Value = table.Column<double>(type: "float", nullable: false),
                    Unit = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false),
                    AppliedToLocation = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    CorrectionDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    Notes = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DraughtMarksCorrections", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DraughtMarksCorrections_DraftSurveys_DraftSurveyId",
                        column: x => x.DraftSurveyId,
                        principalTable: "DraftSurveys",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "DraughtSurveyDocuments",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    DraftSurveyId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    DocumentId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    AttachedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UsageType = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    Notes = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DraughtSurveyDocuments", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DraughtSurveyDocuments_Documents_DocumentId",
                        column: x => x.DocumentId,
                        principalTable: "Documents",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_DraughtSurveyDocuments_DraftSurveys_DraftSurveyId",
                        column: x => x.DraftSurveyId,
                        principalTable: "DraftSurveys",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "DraughtSurveyGlobalCalculations",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    DraftSurveyId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    CalculationDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    InitialDisplacement = table.Column<double>(type: "float", nullable: false),
                    FinalDisplacement = table.Column<double>(type: "float", nullable: false),
                    TotalBallastWater = table.Column<double>(type: "float", nullable: false),
                    TotalFreshWater = table.Column<double>(type: "float", nullable: false),
                    TotalFuelOil = table.Column<double>(type: "float", nullable: false),
                    TotalDieselOil = table.Column<double>(type: "float", nullable: false),
                    TotalLubricatingOil = table.Column<double>(type: "float", nullable: false),
                    TotalConstant = table.Column<double>(type: "float", nullable: false),
                    NetCargoWeight = table.Column<double>(type: "float", nullable: false),
                    Difference = table.Column<double>(type: "float", nullable: false),
                    CalculationStatus = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    VerifiedByUserId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    VerifiedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    FirstTrimCorrection = table.Column<double>(type: "float", nullable: false),
                    SecondTrimCorrection = table.Column<double>(type: "float", nullable: false),
                    IsComplete = table.Column<bool>(type: "bit", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DraughtSurveyGlobalCalculations", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DraughtSurveyGlobalCalculations_DraftSurveys_DraftSurveyId",
                        column: x => x.DraftSurveyId,
                        principalTable: "DraftSurveys",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_DraughtSurveyGlobalCalculations_Users_VerifiedByUserId",
                        column: x => x.VerifiedByUserId,
                        principalTable: "Users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "InspectionReports",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    InspectionId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    ReportContent = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    GeneratedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    ReportType = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Status = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    GeneratedByUserId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    ApprovedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    ApprovedByUserId = table.Column<Guid>(type: "uniqueidentifier", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_InspectionReports", x => x.Id);
                    table.ForeignKey(
                        name: "FK_InspectionReports_Inspection_InspectionId",
                        column: x => x.InspectionId,
                        principalTable: "Inspection",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_InspectionReports_Users_ApprovedByUserId",
                        column: x => x.ApprovedByUserId,
                        principalTable: "Users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_InspectionReports_Users_GeneratedByUserId",
                        column: x => x.GeneratedByUserId,
                        principalTable: "Users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "SeaWaterDensityStatements",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    DraftSurveyId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    DensityValue = table.Column<double>(type: "float", nullable: false),
                    MeasurementDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    MeasurementTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    Density = table.Column<double>(type: "float", nullable: false),
                    Temperature = table.Column<double>(type: "float", nullable: false),
                    MeasurementMethod = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Location = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Notes = table.Column<string>(type: "nvarchar(max)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SeaWaterDensityStatements", x => x.Id);
                    table.ForeignKey(
                        name: "FK_SeaWaterDensityStatements_DraftSurveys_DraftSurveyId",
                        column: x => x.DraftSurveyId,
                        principalTable: "DraftSurveys",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_Liquids_Name",
                table: "Liquids",
                column: "Name",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_AvisChargements_CreatedByUserId",
                table: "AvisChargements",
                column: "CreatedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_AvisChargements_EscaleId",
                table: "AvisChargements",
                column: "EscaleId");

            migrationBuilder.CreateIndex(
                name: "IX_DraughtMarksCorrections_DraftSurveyId",
                table: "DraughtMarksCorrections",
                column: "DraftSurveyId");

            migrationBuilder.CreateIndex(
                name: "IX_DraughtSurveyDocuments_DocumentId",
                table: "DraughtSurveyDocuments",
                column: "DocumentId");

            migrationBuilder.CreateIndex(
                name: "IX_DraughtSurveyDocuments_DraftSurveyId",
                table: "DraughtSurveyDocuments",
                column: "DraftSurveyId");

            migrationBuilder.CreateIndex(
                name: "IX_DraughtSurveyGlobalCalculations_DraftSurveyId",
                table: "DraughtSurveyGlobalCalculations",
                column: "DraftSurveyId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_DraughtSurveyGlobalCalculations_VerifiedByUserId",
                table: "DraughtSurveyGlobalCalculations",
                column: "VerifiedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_InspectionReports_ApprovedByUserId",
                table: "InspectionReports",
                column: "ApprovedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_InspectionReports_GeneratedByUserId",
                table: "InspectionReports",
                column: "GeneratedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_InspectionReports_InspectionId",
                table: "InspectionReports",
                column: "InspectionId");

            migrationBuilder.CreateIndex(
                name: "IX_SeaWaterDensityStatements_DraftSurveyId",
                table: "SeaWaterDensityStatements",
                column: "DraftSurveyId",
                unique: true);

            migrationBuilder.AddForeignKey(
                name: "FK_Documents_Users_UploadedByUserId",
                table: "Documents",
                column: "UploadedByUserId",
                principalTable: "Users",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_FreshWaterTanks_DraftSurveys_SurveyId",
                table: "FreshWaterTanks",
                column: "SurveyId",
                principalTable: "DraftSurveys",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_HoldInspections_Inspection_InspectionId",
                table: "HoldInspections",
                column: "InspectionId",
                principalTable: "Inspection",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_LiquidStatements_Liquids_LiquidId",
                table: "LiquidStatements",
                column: "LiquidId",
                principalTable: "Liquids",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Documents_Users_UploadedByUserId",
                table: "Documents");

            migrationBuilder.DropForeignKey(
                name: "FK_FreshWaterTanks_DraftSurveys_SurveyId",
                table: "FreshWaterTanks");

            migrationBuilder.DropForeignKey(
                name: "FK_HoldInspections_Inspection_InspectionId",
                table: "HoldInspections");

            migrationBuilder.DropForeignKey(
                name: "FK_LiquidStatements_Liquids_LiquidId",
                table: "LiquidStatements");

            migrationBuilder.DropTable(
                name: "AvisChargements");

            migrationBuilder.DropTable(
                name: "DraughtMarksCorrections");

            migrationBuilder.DropTable(
                name: "DraughtSurveyDocuments");

            migrationBuilder.DropTable(
                name: "DraughtSurveyGlobalCalculations");

            migrationBuilder.DropTable(
                name: "InspectionReports");

            migrationBuilder.DropTable(
                name: "SeaWaterDensityStatements");

            migrationBuilder.DropPrimaryKey(
                name: "PK_Liquids",
                table: "Liquids");

            migrationBuilder.DropIndex(
                name: "IX_Liquids_Name",
                table: "Liquids");

            migrationBuilder.DropPrimaryKey(
                name: "PK_HoldInspections",
                table: "HoldInspections");

            migrationBuilder.DropPrimaryKey(
                name: "PK_FreshWaterTanks",
                table: "FreshWaterTanks");

            migrationBuilder.DropPrimaryKey(
                name: "PK_Documents",
                table: "Documents");

            migrationBuilder.DropColumn(
                name: "BreadthMoulded",
                table: "Vessels");

            migrationBuilder.DropColumn(
                name: "CallSign",
                table: "Vessels");

            migrationBuilder.DropColumn(
                name: "DepthMoulded",
                table: "Vessels");

            migrationBuilder.DropColumn(
                name: "DisplacementAtDesignDraft",
                table: "Vessels");

            migrationBuilder.DropColumn(
                name: "Flag",
                table: "Vessels");

            migrationBuilder.DropColumn(
                name: "LCG",
                table: "Vessels");

            migrationBuilder.DropColumn(
                name: "LengthOverall",
                table: "Vessels");

            migrationBuilder.DropColumn(
                name: "LightDisplacement",
                table: "Vessels");

            migrationBuilder.DropColumn(
                name: "Notes",
                table: "Vessels");

            migrationBuilder.DropColumn(
                name: "Type",
                table: "Vessels");

            migrationBuilder.DropColumn(
                name: "Notes",
                table: "LiquidStatements");

            migrationBuilder.DropColumn(
                name: "TankName",
                table: "LiquidStatements");

            migrationBuilder.DropColumn(
                name: "IsHazardous",
                table: "Liquids");

            migrationBuilder.DropColumn(
                name: "Unit",
                table: "Liquids");

            migrationBuilder.DropColumn(
                name: "Condition",
                table: "HoldInspections");

            migrationBuilder.DropColumn(
                name: "HoldNumber",
                table: "HoldInspections");

            migrationBuilder.DropColumn(
                name: "Notes",
                table: "HoldInspections");

            migrationBuilder.DropColumn(
                name: "ReasonForRejection",
                table: "HoldInspections");

            migrationBuilder.DropColumn(
                name: "MeasurementTime",
                table: "FreshWaterTanks");

            migrationBuilder.DropColumn(
                name: "Notes",
                table: "FreshWaterTanks");

            migrationBuilder.DropColumn(
                name: "ContentType",
                table: "Documents");

            migrationBuilder.DropColumn(
                name: "DocumentType",
                table: "Documents");

            migrationBuilder.DropColumn(
                name: "FileName",
                table: "Documents");

            migrationBuilder.DropColumn(
                name: "FileSize",
                table: "Documents");

            migrationBuilder.RenameTable(
                name: "Liquids",
                newName: "Liquid");

            migrationBuilder.RenameTable(
                name: "HoldInspections",
                newName: "HoldInspection");

            migrationBuilder.RenameTable(
                name: "FreshWaterTanks",
                newName: "FreshWaterTank");

            migrationBuilder.RenameTable(
                name: "Documents",
                newName: "Document");

            migrationBuilder.RenameColumn(
                name: "SummerDeadweight",
                table: "Vessels",
                newName: "LongitudinalCenterOfFloatation");

            migrationBuilder.RenameColumn(
                name: "MTC",
                table: "Vessels",
                newName: "MomentToChangeTrim");

            migrationBuilder.RenameColumn(
                name: "LCF",
                table: "Vessels",
                newName: "DeadweightTonnage");

            migrationBuilder.RenameColumn(
                name: "IsApproved",
                table: "HoldInspection",
                newName: "IsDry");

            migrationBuilder.RenameIndex(
                name: "IX_HoldInspections_InspectionId",
                table: "HoldInspection",
                newName: "IX_HoldInspection_InspectionId");

            migrationBuilder.RenameColumn(
                name: "Volume",
                table: "FreshWaterTank",
                newName: "TotalCapacity");

            migrationBuilder.RenameColumn(
                name: "SurveyId",
                table: "FreshWaterTank",
                newName: "DraftSurveyId");

            migrationBuilder.RenameColumn(
                name: "Density",
                table: "FreshWaterTank",
                newName: "Temperature");

            migrationBuilder.RenameIndex(
                name: "IX_FreshWaterTanks_SurveyId",
                table: "FreshWaterTank",
                newName: "IX_FreshWaterTank_DraftSurveyId");

            migrationBuilder.RenameColumn(
                name: "UploadedByUserId",
                table: "Document",
                newName: "DraftSurveyId");

            migrationBuilder.RenameColumn(
                name: "Description",
                table: "Document",
                newName: "Name");

            migrationBuilder.RenameIndex(
                name: "IX_Documents_UploadedByUserId",
                table: "Document",
                newName: "IX_Document_DraftSurveyId");

            migrationBuilder.AddColumn<Guid>(
                name: "GlobalCalculationId",
                table: "DraftSurveys",
                type: "uniqueidentifier",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"));

            migrationBuilder.AlterColumn<int>(
                name: "Type",
                table: "Liquid",
                type: "int",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "nvarchar(50)",
                oldMaxLength: 50);

            migrationBuilder.AlterColumn<string>(
                name: "Name",
                table: "Liquid",
                type: "nvarchar(max)",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "nvarchar(100)",
                oldMaxLength: 100);

            migrationBuilder.AlterColumn<string>(
                name: "Description",
                table: "Liquid",
                type: "nvarchar(max)",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "nvarchar(500)",
                oldMaxLength: 500);

            migrationBuilder.AddColumn<string>(
                name: "Comments",
                table: "HoldInspection",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "HoldName",
                table: "HoldInspection",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<bool>(
                name: "IsClean",
                table: "HoldInspection",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AlterColumn<string>(
                name: "TankName",
                table: "FreshWaterTank",
                type: "nvarchar(max)",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "nvarchar(100)",
                oldMaxLength: 100);

            migrationBuilder.AddColumn<double>(
                name: "CurrentVolume",
                table: "FreshWaterTank",
                type: "float",
                nullable: false,
                defaultValue: 0.0);

            migrationBuilder.AlterColumn<string>(
                name: "FilePath",
                table: "Document",
                type: "nvarchar(max)",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "nvarchar(500)",
                oldMaxLength: 500);

            migrationBuilder.AddPrimaryKey(
                name: "PK_Liquid",
                table: "Liquid",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_HoldInspection",
                table: "HoldInspection",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_FreshWaterTank",
                table: "FreshWaterTank",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_Document",
                table: "Document",
                column: "Id");

            migrationBuilder.CreateTable(
                name: "GlobalCalculation",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Displacement = table.Column<double>(type: "float", nullable: false),
                    FirstTrimCorrection = table.Column<double>(type: "float", nullable: false),
                    NetCargoWeight = table.Column<double>(type: "float", nullable: false),
                    SecondTrimCorrection = table.Column<double>(type: "float", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_GlobalCalculation", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "SeaWaterDensity",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    DraftSurveyId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Density = table.Column<double>(type: "float", nullable: false),
                    MeasurementMethod = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    MeasurementTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    Temperature = table.Column<double>(type: "float", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SeaWaterDensity", x => x.Id);
                    table.ForeignKey(
                        name: "FK_SeaWaterDensity_DraftSurveys_DraftSurveyId",
                        column: x => x.DraftSurveyId,
                        principalTable: "DraftSurveys",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_DraftSurveys_GlobalCalculationId",
                table: "DraftSurveys",
                column: "GlobalCalculationId");

            migrationBuilder.CreateIndex(
                name: "IX_SeaWaterDensity_DraftSurveyId",
                table: "SeaWaterDensity",
                column: "DraftSurveyId",
                unique: true);

            migrationBuilder.AddForeignKey(
                name: "FK_Document_DraftSurveys_DraftSurveyId",
                table: "Document",
                column: "DraftSurveyId",
                principalTable: "DraftSurveys",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_DraftSurveys_GlobalCalculation_GlobalCalculationId",
                table: "DraftSurveys",
                column: "GlobalCalculationId",
                principalTable: "GlobalCalculation",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_FreshWaterTank_DraftSurveys_DraftSurveyId",
                table: "FreshWaterTank",
                column: "DraftSurveyId",
                principalTable: "DraftSurveys",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_HoldInspection_Inspection_InspectionId",
                table: "HoldInspection",
                column: "InspectionId",
                principalTable: "Inspection",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_LiquidStatements_Liquid_LiquidId",
                table: "LiquidStatements",
                column: "LiquidId",
                principalTable: "Liquid",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
