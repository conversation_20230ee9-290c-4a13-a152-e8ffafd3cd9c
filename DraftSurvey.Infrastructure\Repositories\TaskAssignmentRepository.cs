﻿using DraftSurvey.Domain.Entities;
using DraftSurvey.Domain.Interfaces;
using DraftSurvey.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

public class TaskAssignmentRepository : ITaskAssignmentRepository
{
    private readonly DraftSurveyDbContext _context;

    public TaskAssignmentRepository(DraftSurveyDbContext context)
    {
        _context = context;
    }

    public async Task<TaskAssignment> GetByIdAsync(Guid id)
    {
        return await _context.TaskAssignments
            .AsSplitQuery()  // Moved to beginning
            .Include(t => t.User)
                .ThenInclude(u => u.Role)
            .Include(t => t.DraftSurvey)
            .FirstOrDefaultAsync(t => t.Id == id);
    }

    public async Task<IEnumerable<TaskAssignment>> GetAllAsync(string? include = null)
    {
        var query = _context.TaskAssignments
            .AsSplitQuery();  // Moved to beginning

        if (include?.Contains("user", StringComparison.OrdinalIgnoreCase) == true)
            query = query.Include(t => t.User)
                .ThenInclude(u => u.Role);

        if (include?.Contains("draftSurvey", StringComparison.OrdinalIgnoreCase) == true)
            query = query.Include(t => t.DraftSurvey);

        return await query.ToListAsync();
    }

    public async Task<IEnumerable<TaskAssignment>> GetByUserIdAsync(Guid userId)
    {
        return await _context.TaskAssignments
            .AsSplitQuery()  // Moved to beginning
            .Include(t => t.User)
                .ThenInclude(u => u.Role)
            .Include(t => t.DraftSurvey)
            .Where(t => t.UserId == userId)
            .ToListAsync();
    }

    public async Task<IEnumerable<TaskAssignment>> GetByDraftSurveyIdAsync(Guid draftSurveyId)
    {
        return await _context.TaskAssignments
            .AsSplitQuery()  // Moved to beginning
            .Include(t => t.User)
                .ThenInclude(u => u.Role)
            .Include(t => t.DraftSurvey)
            .Where(t => t.DraftSurveyId == draftSurveyId)
            .ToListAsync();
    }

    public async Task<TaskAssignment> AddAsync(TaskAssignment taskAssignment)
    {
        await _context.TaskAssignments.AddAsync(taskAssignment);
        return taskAssignment;
    }

    public async Task UpdateAsync(TaskAssignment taskAssignment)
    {
        _context.TaskAssignments.Update(taskAssignment);
        await Task.CompletedTask;
    }

    public async Task DeleteAsync(Guid id)
    {
        var entity = await GetByIdAsync(id);
        if (entity != null)
        {
            _context.TaskAssignments.Remove(entity);
        }
    }

    public async Task SaveChangesAsync()
    {
        await _context.SaveChangesAsync();
    }
}