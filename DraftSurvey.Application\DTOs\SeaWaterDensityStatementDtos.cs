﻿// DraftSurvey.Application/DTOs/SeaWaterDensityStatementDtos.cs
using System;
using System.ComponentModel.DataAnnotations;

namespace DraftSurvey.Application.DTOs
{
    public class SeaWaterDensityStatementDto
    {
        public Guid Id { get; set; }
        public Guid DraftSurveyId { get; set; }
        public double DensityValue { get; set; } // La densité brute
        public DateTime MeasurementDate { get; set; } // La date de mesure
        public DateTime MeasurementTime { get; set; } // L'heure de mesure
        public double Density { get; set; } // La densité (en t/m³ ou kg/m³)
        public double Temperature { get; set; } // En degrés Celsius
        public string MeasurementMethod { get; set; } // Ex: "Hydrometer", "Refractometer"
        public string Location { get; set; } // Ex: "Alongside", "At Anchorage", "Midship"
        public string Notes { get; set; }
    }

    public class SeaWaterDensityStatementCreateDto
    {
        [Required]
        public Guid DraftSurveyId { get; set; }
        [Required]
        public double DensityValue { get; set; }
        [Required]
        public DateTime MeasurementDate { get; set; }
        [Required]
        public DateTime MeasurementTime { get; set; }
        [Required]
        public double Density { get; set; }
        [Required]
        public double Temperature { get; set; }
        public string MeasurementMethod { get; set; }
        public string Location { get; set; }
        public string Notes { get; set; }
    }

    public class SeaWaterDensityStatementUpdateDto
    {
        [Required]
        public double DensityValue { get; set; }
        [Required]
        public DateTime MeasurementDate { get; set; }
        [Required]
        public DateTime MeasurementTime { get; set; }
        [Required]
        public double Density { get; set; }
        [Required]
        public double Temperature { get; set; }
        public string MeasurementMethod { get; set; }
        public string Location { get; set; }
        public string Notes { get; set; }
    }
}