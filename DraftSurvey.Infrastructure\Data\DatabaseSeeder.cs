using Microsoft.EntityFrameworkCore;
using DraftSurvey.Domain.Entities;

namespace DraftSurvey.Infrastructure.Data
{
    public class DatabaseSeeder
    {
        private readonly DraftSurveyDbContext _context;

        public DatabaseSeeder(DraftSurveyDbContext context)
        {
            _context = context;
        }

        public async Task SeedAsync()
        {
            // Clear existing data first
            await ClearExistingData();

            Console.WriteLine("Starting database seeding...");

            // Seed Roles first (required for Users)
            await SeedRoles();
            
            // Seed Ports (required for Users and Escales)
            await SeedPorts();
            
            // Seed Users (requires Roles and Ports)
            await SeedUsers();
            
            // Seed Vessels
            await SeedVessels();
            
            // Seed Escales (requires Ports and Vessels)
            await SeedEscales();
            
            // Seed DraftSurveys (requires Vessels and Escales)
            await SeedDraftSurveys();
            
            // Seed basic related entities
            await SeedTaskAssignments();

            await _context.SaveChangesAsync();
            Console.WriteLine("Database seeding completed successfully!");
        }

        private async Task ClearExistingData()
        {
            Console.WriteLine("Clearing existing data...");

            // Clear in reverse order of dependencies
            _context.TaskAssignments.RemoveRange(_context.TaskAssignments);
            _context.DraftSurveys.RemoveRange(_context.DraftSurveys);
            _context.Escales.RemoveRange(_context.Escales);
            _context.Vessels.RemoveRange(_context.Vessels);
            _context.Users.RemoveRange(_context.Users);
            _context.Ports.RemoveRange(_context.Ports);
            _context.Roles.RemoveRange(_context.Roles);

            await _context.SaveChangesAsync();
            Console.WriteLine("Existing data cleared.");
        }

        private async Task SeedRoles()
        {
            var roles = new List<Role>
            {
                new Role { Id = Guid.NewGuid(), Name = "Admin", Description = "System Administrator", CanManageUsers = true, CanManageAllPorts = true },
                new Role { Id = Guid.NewGuid(), Name = "TeamLead", Description = "Team Leader", CanManageUsers = true, CanManageAllPorts = true },
                new Role { Id = Guid.NewGuid(), Name = "Agent", Description = "Marine Agent", CanManageUsers = false, CanManageAllPorts = false },
                new Role { Id = Guid.NewGuid(), Name = "Surveyor", Description = "Marine Surveyor", CanManageUsers = false, CanManageAllPorts = false },
                new Role { Id = Guid.NewGuid(), Name = "Inspector", Description = "Cargo Inspector", CanManageUsers = false, CanManageAllPorts = false },
                new Role { Id = Guid.NewGuid(), Name = "Operator", Description = "Terminal Operator", CanManageUsers = false, CanManageAllPorts = false },
                new Role { Id = Guid.NewGuid(), Name = "Supervisor", Description = "Operations Supervisor", CanManageUsers = false, CanManageAllPorts = false },
                new Role { Id = Guid.NewGuid(), Name = "Analyst", Description = "Data Analyst", CanManageUsers = false, CanManageAllPorts = false },
                new Role { Id = Guid.NewGuid(), Name = "Coordinator", Description = "Operations Coordinator", CanManageUsers = false, CanManageAllPorts = false },
                new Role { Id = Guid.NewGuid(), Name = "Viewer", Description = "Read-only Access", CanManageUsers = false, CanManageAllPorts = false }
            };

            await _context.Roles.AddRangeAsync(roles);
            await _context.SaveChangesAsync();
        }

        private async Task SeedPorts()
        {
            var ports = new List<Port>
            {
                new Port { Id = Guid.NewGuid(), Name = "Port of Rotterdam", Code = "NLRTM", Country = "Netherlands", StandardWaterDensity = 1.025 },
                new Port { Id = Guid.NewGuid(), Name = "Port of Hamburg", Code = "DEHAM", Country = "Germany", StandardWaterDensity = 1.024 },
                new Port { Id = Guid.NewGuid(), Name = "Port of Antwerp", Code = "BEANR", Country = "Belgium", StandardWaterDensity = 1.025 },
                new Port { Id = Guid.NewGuid(), Name = "Port of Le Havre", Code = "FRLEH", Country = "France", StandardWaterDensity = 1.025 },
                new Port { Id = Guid.NewGuid(), Name = "Port of Barcelona", Code = "ESBCN", Country = "Spain", StandardWaterDensity = 1.026 },
                new Port { Id = Guid.NewGuid(), Name = "Port of Genoa", Code = "ITGOA", Country = "Italy", StandardWaterDensity = 1.026 },
                new Port { Id = Guid.NewGuid(), Name = "Port of Piraeus", Code = "GRPIR", Country = "Greece", StandardWaterDensity = 1.026 },
                new Port { Id = Guid.NewGuid(), Name = "Port of Valencia", Code = "ESVLC", Country = "Spain", StandardWaterDensity = 1.026 },
                new Port { Id = Guid.NewGuid(), Name = "Port of Marseille", Code = "FRMRS", Country = "France", StandardWaterDensity = 1.025 },
                new Port { Id = Guid.NewGuid(), Name = "Port of Bremen", Code = "DEBRV", Country = "Germany", StandardWaterDensity = 1.024 }
            };

            await _context.Ports.AddRangeAsync(ports);
            await _context.SaveChangesAsync();
        }

        private async Task SeedUsers()
        {
            var roles = await _context.Roles.ToListAsync();
            var ports = await _context.Ports.ToListAsync();
            var random = new Random();

            var users = new List<User>();
            var usernames = new[] { "admin", "teamlead", "agent1", "agent2", "surveyor1", "inspector1", "operator1", "supervisor1", "analyst1", "viewer1" };
            var fullNames = new[] { "System Administrator", "Team Leader", "Marine Agent 1", "Marine Agent 2", "Marine Surveyor", "Cargo Inspector", "Terminal Operator", "Operations Supervisor", "Data Analyst", "System Viewer" };

            for (int i = 0; i < 10; i++)
            {
                var user = new User
                {
                    Id = Guid.NewGuid(),
                    Username = usernames[i],
                    Email = $"{usernames[i]}@draftsurvey.com",
                    FullName = fullNames[i],
                    IsActive = true,
                    RoleId = roles[i].Id,
                    PortId = i < ports.Count ? ports[i].Id : null
                };
                user.SetPassword("Password123!");
                users.Add(user);
            }

            await _context.Users.AddRangeAsync(users);
            await _context.SaveChangesAsync();
        }

        private async Task SeedVessels()
        {
            var vessels = new List<Vessel>
            {
                new Vessel { Id = Guid.NewGuid(), Name = "MV Atlantic Star", IMONumber = "9123456", CallSign = "ABCD1", Flag = "Liberia", Type = "Bulk Carrier", LengthOverall = 225.0, LengthBetweenPerpendiculars = 215.0, BreadthMoulded = 32.26, DepthMoulded = 18.0, LightDisplacement = 12500, SummerDeadweight = 75000, TPC = 45.2, LCF = 107.5, LCG = 110.0, MTC = 850.0, DisplacementAtDesignDraft = 87500, DisplacementFactor = 0.85, Notes = "Bulk carrier for grain transport" },
                new Vessel { Id = Guid.NewGuid(), Name = "MV Pacific Glory", IMONumber = "9234567", CallSign = "EFGH2", Flag = "Panama", Type = "Container Ship", LengthOverall = 300.0, LengthBetweenPerpendiculars = 285.0, BreadthMoulded = 42.8, DepthMoulded = 24.6, LightDisplacement = 18000, SummerDeadweight = 120000, TPC = 62.5, LCF = 142.5, LCG = 145.0, MTC = 1200.0, DisplacementAtDesignDraft = 138000, DisplacementFactor = 0.70, Notes = "Container vessel for international trade" },
                new Vessel { Id = Guid.NewGuid(), Name = "MV Nordic Wind", IMONumber = "9345678", CallSign = "IJKL3", Flag = "Norway", Type = "Tanker", LengthOverall = 180.0, LengthBetweenPerpendiculars = 170.0, BreadthMoulded = 28.0, DepthMoulded = 15.5, LightDisplacement = 8500, SummerDeadweight = 45000, TPC = 32.8, LCF = 85.0, LCG = 88.0, MTC = 650.0, DisplacementAtDesignDraft = 53500, DisplacementFactor = 0.82, Notes = "Chemical tanker" },
                new Vessel { Id = Guid.NewGuid(), Name = "MV Mediterranean Pearl", IMONumber = "9456789", CallSign = "MNOP4", Flag = "Malta", Type = "General Cargo", LengthOverall = 150.0, LengthBetweenPerpendiculars = 142.0, BreadthMoulded = 22.0, DepthMoulded = 12.8, LightDisplacement = 5200, SummerDeadweight = 25000, TPC = 22.5, LCF = 71.0, LCG = 73.0, MTC = 420.0, DisplacementAtDesignDraft = 30200, DisplacementFactor = 0.75, Notes = "Multi-purpose cargo vessel" },
                new Vessel { Id = Guid.NewGuid(), Name = "MV Baltic Trader", IMONumber = "9567890", CallSign = "QRST5", Flag = "Denmark", Type = "Ro-Ro", LengthOverall = 200.0, LengthBetweenPerpendiculars = 190.0, BreadthMoulded = 26.0, DepthMoulded = 16.0, LightDisplacement = 9800, SummerDeadweight = 35000, TPC = 28.5, LCF = 95.0, LCG = 98.0, MTC = 580.0, DisplacementAtDesignDraft = 44800, DisplacementFactor = 0.68, Notes = "Roll-on/Roll-off ferry" },
                new Vessel { Id = Guid.NewGuid(), Name = "MV Asian Dragon", IMONumber = "9678901", CallSign = "UVWX6", Flag = "Singapore", Type = "Bulk Carrier", LengthOverall = 190.0, LengthBetweenPerpendiculars = 182.0, BreadthMoulded = 30.0, DepthMoulded = 16.5, LightDisplacement = 10200, SummerDeadweight = 55000, TPC = 38.2, LCF = 91.0, LCG = 94.0, MTC = 720.0, DisplacementAtDesignDraft = 65200, DisplacementFactor = 0.83, Notes = "Coal and ore carrier" },
                new Vessel { Id = Guid.NewGuid(), Name = "MV Arctic Explorer", IMONumber = "9789012", CallSign = "YZAB7", Flag = "Finland", Type = "Icebreaker", LengthOverall = 120.0, LengthBetweenPerpendiculars = 112.0, BreadthMoulded = 24.0, DepthMoulded = 14.0, LightDisplacement = 8500, SummerDeadweight = 15000, TPC = 18.5, LCF = 56.0, LCG = 58.0, MTC = 380.0, DisplacementAtDesignDraft = 23500, DisplacementFactor = 0.65, Notes = "Ice-class vessel" },
                new Vessel { Id = Guid.NewGuid(), Name = "MV Southern Cross", IMONumber = "9890123", CallSign = "CDEF8", Flag = "Australia", Type = "Bulk Carrier", LengthOverall = 210.0, LengthBetweenPerpendiculars = 200.0, BreadthMoulded = 34.0, DepthMoulded = 17.5, LightDisplacement = 11800, SummerDeadweight = 68000, TPC = 42.8, LCF = 100.0, LCG = 103.0, MTC = 780.0, DisplacementAtDesignDraft = 79800, DisplacementFactor = 0.84, Notes = "Iron ore specialist" },
                new Vessel { Id = Guid.NewGuid(), Name = "MV Atlantic Breeze", IMONumber = "9901234", CallSign = "GHIJ9", Flag = "UK", Type = "Tanker", LengthOverall = 165.0, LengthBetweenPerpendiculars = 157.0, BreadthMoulded = 26.5, DepthMoulded = 14.8, LightDisplacement = 7200, SummerDeadweight = 38000, TPC = 28.8, LCF = 78.5, LCG = 81.0, MTC = 520.0, DisplacementAtDesignDraft = 45200, DisplacementFactor = 0.80, Notes = "Product tanker" },
                new Vessel { Id = Guid.NewGuid(), Name = "MV Pacific Horizon", IMONumber = "9012345", CallSign = "KLMN0", Flag = "Japan", Type = "Container Ship", LengthOverall = 280.0, LengthBetweenPerpendiculars = 265.0, BreadthMoulded = 40.0, DepthMoulded = 22.5, LightDisplacement = 16500, SummerDeadweight = 95000, TPC = 55.2, LCF = 132.5, LCG = 135.0, MTC = 1050.0, DisplacementAtDesignDraft = 111500, DisplacementFactor = 0.72, Notes = "Feeder container ship" }
            };

            await _context.Vessels.AddRangeAsync(vessels);
            await _context.SaveChangesAsync();
        }

        private async Task SeedEscales()
        {
            var ports = await _context.Ports.ToListAsync();
            var vessels = await _context.Vessels.ToListAsync();
            var random = new Random();

            var escales = new List<Escale>();
            for (int i = 0; i < 10; i++)
            {
                var escale = new Escale
                {
                    Id = Guid.NewGuid(),
                    Reference = $"ESC-{DateTime.Now.Year}-{(i + 1):D4}",
                    VesselId = vessels[i].Id,
                    PortId = ports[i].Id,
                    ArrivalDate = DateTime.Now.AddDays(-random.Next(1, 30)),
                    DepartureDate = i % 2 == 0 ? DateTime.Now.AddDays(-random.Next(1, 15)) : null // Some escales still active
                };
                escales.Add(escale);
            }

            await _context.Escales.AddRangeAsync(escales);
            await _context.SaveChangesAsync();
        }

        private async Task SeedDraftSurveys()
        {
            var vessels = await _context.Vessels.ToListAsync();
            var escales = await _context.Escales.ToListAsync();
            var random = new Random();

            var draftSurveys = new List<Domain.Entities.DraftSurvey>();
            for (int i = 0; i < 10; i++)
            {
                var draftSurvey = new Domain.Entities.DraftSurvey
                {
                    Id = Guid.NewGuid(),
                    SurveyNumber = $"DS-{DateTime.Now.Year}-{(i + 1):D4}",
                    VesselId = vessels[i].Id,
                    EscaleId = escales[i].Id,
                    CreatedDate = DateTime.Now.AddDays(-random.Next(1, 20)),
                    Status = (DraftSurveyStatus)(i % 3), // Initial, Intermediate, Final
                    UpdatedDate = i % 2 == 0 ? DateTime.Now.AddDays(-random.Next(1, 10)) : null
                };
                draftSurveys.Add(draftSurvey);
            }

            await _context.DraftSurveys.AddRangeAsync(draftSurveys);
            await _context.SaveChangesAsync();
        }



        private async Task SeedTaskAssignments()
        {
            var draftSurveys = await _context.DraftSurveys.ToListAsync();
            var users = await _context.Users.ToListAsync();
            var random = new Random();

            var taskAssignments = new List<TaskAssignment>();
            foreach (var survey in draftSurveys)
            {
                var assignment = new TaskAssignment
                {
                    Id = Guid.NewGuid(),
                    DraftSurveyId = survey.Id,
                    UserId = users[random.Next(0, users.Count)].Id,
                    AssignmentDate = DateTime.Now.AddDays(-random.Next(1, 5)),
                    CompletionDate = random.Next(0, 2) == 0 ? DateTime.Now.AddDays(-random.Next(0, 3)) : null,
                    Status = random.Next(0, 4) == 0 ? "Assigned" : random.Next(0, 3) == 0 ? "In Progress" : random.Next(0, 2) == 0 ? "Completed" : "Cancelled",
                    Notes = "Survey task assignment",
                    TaskType = (TaskType)(random.Next(0, 9)), // TaskType enum has 9 values
                    IsCompleted = random.Next(0, 2) == 0,
                    TaskData = "{\"priority\": \"normal\", \"estimated_hours\": 4}"
                };
                taskAssignments.Add(assignment);
            }

            await _context.TaskAssignments.AddRangeAsync(taskAssignments);
            await _context.SaveChangesAsync();
        }
    }
}
