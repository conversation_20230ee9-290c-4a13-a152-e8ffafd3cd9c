import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Typography,
  Box,
  useTheme,
  Table,
  TableBody,
  TableCell,
  TableRow,
  TableHead,
  TableContainer,
  Paper,
  CircularProgress
} from '@mui/material';
import { getUsers } from '../../api/usersApi';
import { getSurveys } from '../../api/draftApi';

const TaskAssignmentPopup = ({ open, onClose, onAssign }) => {
  const [users, setUsers] = useState([]);
  const [surveys, setSurveys] = useState([]);
  const [assignments, setAssignments] = useState({});
  const [loading, setLoading] = useState(false);
  const theme = useTheme();

  useEffect(() => {
    if (open) {
      const fetchData = async () => {
        setLoading(true);
        try {
          const [usersData, surveysData] = await Promise.all([
            getUsers(),
            getSurveys()
          ]);
          setUsers(usersData);
          setSurveys(surveysData);
          
          // Initialiser les assignations pour chaque survey
          const initialAssignments = {};
          surveysData.forEach(survey => {
            initialAssignments[survey.id] = {
              ballastSounding: { userId: '', assigned: false },
              draftMarksReadings: { userId: '', assigned: false },
              seaWaterDensity: { userId: '', assigned: false },
              liquidStatement: { userId: '', assigned: false },
              surveyCalculation: { userId: '', assigned: false }
            };
          });
          setAssignments(initialAssignments);
        } catch (error) {
          console.error('Error fetching data:', error);
        } finally {
          setLoading(false);
        }
      };
      fetchData();
    }
  }, [open]);

  const handleUserAssign = (surveyId, taskType, userId) => {
    setAssignments(prev => ({
      ...prev,
      [surveyId]: {
        ...prev[surveyId],
        [taskType]: {
          userId,
          assigned: !!userId
        }
      }
    }));
  };

  const handleAssign = () => {
    const assignmentsToSend = [];
    
    Object.entries(assignments).forEach(([surveyId, tasks]) => {
      Object.entries(tasks).forEach(([taskType, taskData]) => {
        if (taskData.assigned && taskData.userId) {
          assignmentsToSend.push({
            draftSurveyId: surveyId,
            userId: taskData.userId,
            taskName: taskType,
            status: 'Assigned'
          });
        }
      });
    });

    if (assignmentsToSend.length === 0) {
      alert('Veuillez assigner au moins une tâche');
      return;
    }

    onAssign(assignmentsToSend);
  };

  const taskLabels = {
    ballastSounding: 'Ballast Sounding',
    draftMarksReadings: 'Draught Marks',
    seaWaterDensity: 'Sea Water Density',
    liquidStatement: 'Liquid Statement',
    surveyCalculation: 'Survey Calculation'
  };

  if (loading) {
    return (
      <Dialog open={open} onClose={onClose} maxWidth="lg" fullWidth>
        <DialogContent sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '200px' }}>
          <CircularProgress />
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={open} onClose={onClose} maxWidth="lg" fullWidth>
      <DialogTitle sx={{ bgcolor: theme.palette.primary.main, color: 'white' }}>
        Assigner des Tâches
      </DialogTitle>
      <DialogContent sx={{ pt: 3 }}>
        <Typography variant="subtitle1" gutterBottom sx={{ mb: 2 }}>
          Assignez des tâches aux agents pour chaque survey disponible
        </Typography>
        
        <TableContainer component={Paper} sx={{ maxHeight: '60vh', overflow: 'auto' }}>
          <Table stickyHeader>
            <TableHead>
              <TableRow>
                <TableCell sx={{ fontWeight: 'bold', minWidth: '200px' }}>Survey (Vessel)</TableCell>
                {Object.entries(taskLabels).map(([key, label]) => (
                  <TableCell key={key} sx={{ fontWeight: 'bold', minWidth: '180px' }}>{label}</TableCell>
                ))}
              </TableRow>
            </TableHead>
            <TableBody>
              {surveys.map(survey => (
                <TableRow key={survey.id}>
                  <TableCell>
                    <Box>
                      <Typography fontWeight="bold">{survey.surveyNumber}</Typography>
                      <Typography variant="body2">{survey.vesselName}</Typography>
                    </Box>
                  </TableCell>
                  {Object.keys(taskLabels).map(taskType => (
                    <TableCell key={`${survey.id}-${taskType}`}>
                      <FormControl fullWidth size="small">
                        <InputLabel>Agent</InputLabel>
                        <Select
                          value={assignments[survey.id]?.[taskType]?.userId || ''}
                          onChange={(e) => handleUserAssign(survey.id, taskType, e.target.value)}
                          label="Agent"
                        >
                          <MenuItem value="">Non assigné</MenuItem>
                          {users.map(user => (
                            <MenuItem key={user.id} value={user.id}>
                              {user.fullName || user.id}
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                    </TableCell>
                  ))}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </DialogContent>
      <DialogActions sx={{ p: 3 }}>
        <Button onClick={onClose} variant="outlined" color="error" sx={{ mr: 2 }}>
          Annuler
        </Button>
        <Button 
          onClick={handleAssign} 
          variant="contained" 
          color="primary"
          sx={{ minWidth: '150px' }}
        >
          Assigner
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default TaskAssignmentPopup;