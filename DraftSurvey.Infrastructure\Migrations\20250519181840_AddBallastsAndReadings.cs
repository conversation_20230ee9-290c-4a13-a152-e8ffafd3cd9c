﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace DraftSurvey.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddBallastsAndReadings : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Ballast_DraftSurveys_DraftSurveyId",
                table: "Ballast");

            migrationBuilder.DropForeignKey(
                name: "FK_DraftSurveys_DraughtMarkReading_DraughtMarkReadingId",
                table: "DraftSurveys");

            migrationBuilder.DropIndex(
                name: "IX_DraftSurveys_DraughtMarkReadingId",
                table: "DraftSurveys");

            migrationBuilder.DropPrimaryKey(
                name: "PK_DraughtMarkReading",
                table: "DraughtMarkReading");

            migrationBuilder.DropPrimaryKey(
                name: "PK_Ballast",
                table: "Ballast");

            migrationBuilder.DropColumn(
                name: "DraughtMarkReadingId",
                table: "DraftSurveys");

            migrationBuilder.RenameTable(
                name: "DraughtMarkReading",
                newName: "DraughtMarkReadings");

            migrationBuilder.RenameTable(
                name: "Ballast",
                newName: "Ballasts");

            migrationBuilder.RenameIndex(
                name: "IX_Ballast_DraftSurveyId",
                table: "Ballasts",
                newName: "IX_Ballasts_DraftSurveyId");

            migrationBuilder.AddColumn<Guid>(
                name: "DraftSurveyId",
                table: "DraughtMarkReadings",
                type: "uniqueidentifier",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"));

            migrationBuilder.AddPrimaryKey(
                name: "PK_DraughtMarkReadings",
                table: "DraughtMarkReadings",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_Ballasts",
                table: "Ballasts",
                column: "Id");

            migrationBuilder.CreateTable(
                name: "Liquid",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Name = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Type = table.Column<int>(type: "int", nullable: false),
                    StandardDensity = table.Column<double>(type: "float", nullable: false),
                    Description = table.Column<string>(type: "nvarchar(max)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Liquid", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "LiquidStatements",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    MeasurementTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    Volume = table.Column<double>(type: "float", nullable: false),
                    MeasuredDensity = table.Column<double>(type: "float", nullable: true),
                    LiquidId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    DraftSurveyId = table.Column<Guid>(type: "uniqueidentifier", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_LiquidStatements", x => x.Id);
                    table.ForeignKey(
                        name: "FK_LiquidStatements_DraftSurveys_DraftSurveyId",
                        column: x => x.DraftSurveyId,
                        principalTable: "DraftSurveys",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_LiquidStatements_Liquid_LiquidId",
                        column: x => x.LiquidId,
                        principalTable: "Liquid",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_DraughtMarkReadings_DraftSurveyId",
                table: "DraughtMarkReadings",
                column: "DraftSurveyId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_LiquidStatements_DraftSurveyId",
                table: "LiquidStatements",
                column: "DraftSurveyId");

            migrationBuilder.CreateIndex(
                name: "IX_LiquidStatements_LiquidId",
                table: "LiquidStatements",
                column: "LiquidId");

            migrationBuilder.AddForeignKey(
                name: "FK_Ballasts_DraftSurveys_DraftSurveyId",
                table: "Ballasts",
                column: "DraftSurveyId",
                principalTable: "DraftSurveys",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_DraughtMarkReadings_DraftSurveys_DraftSurveyId",
                table: "DraughtMarkReadings",
                column: "DraftSurveyId",
                principalTable: "DraftSurveys",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Ballasts_DraftSurveys_DraftSurveyId",
                table: "Ballasts");

            migrationBuilder.DropForeignKey(
                name: "FK_DraughtMarkReadings_DraftSurveys_DraftSurveyId",
                table: "DraughtMarkReadings");

            migrationBuilder.DropTable(
                name: "LiquidStatements");

            migrationBuilder.DropTable(
                name: "Liquid");

            migrationBuilder.DropPrimaryKey(
                name: "PK_DraughtMarkReadings",
                table: "DraughtMarkReadings");

            migrationBuilder.DropIndex(
                name: "IX_DraughtMarkReadings_DraftSurveyId",
                table: "DraughtMarkReadings");

            migrationBuilder.DropPrimaryKey(
                name: "PK_Ballasts",
                table: "Ballasts");

            migrationBuilder.DropColumn(
                name: "DraftSurveyId",
                table: "DraughtMarkReadings");

            migrationBuilder.RenameTable(
                name: "DraughtMarkReadings",
                newName: "DraughtMarkReading");

            migrationBuilder.RenameTable(
                name: "Ballasts",
                newName: "Ballast");

            migrationBuilder.RenameIndex(
                name: "IX_Ballasts_DraftSurveyId",
                table: "Ballast",
                newName: "IX_Ballast_DraftSurveyId");

            migrationBuilder.AddColumn<Guid>(
                name: "DraughtMarkReadingId",
                table: "DraftSurveys",
                type: "uniqueidentifier",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"));

            migrationBuilder.AddPrimaryKey(
                name: "PK_DraughtMarkReading",
                table: "DraughtMarkReading",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_Ballast",
                table: "Ballast",
                column: "Id");

            migrationBuilder.CreateIndex(
                name: "IX_DraftSurveys_DraughtMarkReadingId",
                table: "DraftSurveys",
                column: "DraughtMarkReadingId");

            migrationBuilder.AddForeignKey(
                name: "FK_Ballast_DraftSurveys_DraftSurveyId",
                table: "Ballast",
                column: "DraftSurveyId",
                principalTable: "DraftSurveys",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_DraftSurveys_DraughtMarkReading_DraughtMarkReadingId",
                table: "DraftSurveys",
                column: "DraughtMarkReadingId",
                principalTable: "DraughtMarkReading",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
