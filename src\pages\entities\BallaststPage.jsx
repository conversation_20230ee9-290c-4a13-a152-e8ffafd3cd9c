import React, { useState, useEffect } from 'react';
import EntityPageLayout from '../../components/Shared/EntityPageLayout';
import { Droplets, TrendingUp, TrendingDown, AlertTriangle, CheckCircle } from 'lucide-react';

const BallaststPage = () => {
  const [ballasts, setBallasts] = useState([]);
  const [loading, setLoading] = useState(true);

  // Mock data - replace with actual API call
  useEffect(() => {
    const mockBallasts = [
      {
        id: 1,
        tankName: "Ballast Tank #1 Port",
        vesselName: "MSC OSCAR",
        capacity: 2500.0,
        currentVolume: 1850.5,
        density: 1.025,
        temperature: 18.5,
        status: "Filled",
        lastUpdated: "2024-01-15T10:30:00Z",
        location: "Port Side Forward",
        condition: "Good",
        notes: "Regular ballast operation"
      },
      {
        id: 2,
        tankName: "Ballast Tank #2 Starboard",
        vesselName: "MSC OSCAR",
        capacity: 2500.0,
        currentVolume: 750.0,
        density: 1.024,
        temperature: 19.2,
        status: "Partially Filled",
        lastUpdated: "2024-01-15T09:45:00Z",
        location: "Starboard Side Forward",
        condition: "Good",
        notes: "Ballasting in progress"
      },
      {
        id: 3,
        tankName: "Ballast Tank #3 Center",
        vesselName: "EVER GIVEN",
        capacity: 3200.0,
        currentVolume: 0.0,
        density: 0.0,
        temperature: 0.0,
        status: "Empty",
        lastUpdated: "2024-01-15T08:15:00Z",
        location: "Center Line Aft",
        condition: "Good",
        notes: "Tank emptied for cargo loading"
      },
      {
        id: 4,
        tankName: "Ballast Tank #4 Port",
        vesselName: "MAERSK MADRID",
        capacity: 2800.0,
        currentVolume: 2800.0,
        density: 1.026,
        temperature: 17.8,
        status: "Full",
        lastUpdated: "2024-01-15T11:00:00Z",
        location: "Port Side Aft",
        condition: "Excellent",
        notes: "Maximum ballast for stability"
      },
      {
        id: 5,
        tankName: "Ballast Tank #5 Starboard",
        vesselName: "MAERSK MADRID",
        capacity: 2800.0,
        currentVolume: 1400.0,
        density: 1.023,
        temperature: 18.9,
        status: "Half Full",
        lastUpdated: "2024-01-15T10:45:00Z",
        location: "Starboard Side Aft",
        condition: "Good",
        notes: "Balanced ballasting"
      }
    ];

    setTimeout(() => {
      setBallasts(mockBallasts);
      setLoading(false);
    }, 1000);
  }, []);

  const columns = [
    {
      header: 'Tank Information',
      key: 'tankName',
      render: (ballast) => (
        <div className="flex items-center">
          <div className="flex-shrink-0 h-10 w-10">
            <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
              <Droplets className="h-5 w-5 text-blue-600" />
            </div>
          </div>
          <div className="ml-4">
            <div className="text-sm font-medium text-gray-900">{ballast.tankName}</div>
            <div className="text-sm text-gray-500">{ballast.vesselName}</div>
          </div>
        </div>
      )
    },
    {
      header: 'Capacity & Volume',
      key: 'volume',
      render: (ballast) => {
        const fillPercentage = (ballast.currentVolume / ballast.capacity) * 100;
        return (
          <div>
            <div className="text-sm text-gray-900">
              {ballast.currentVolume.toFixed(1)} / {ballast.capacity.toFixed(1)} m³
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2 mt-1">
              <div 
                className="bg-blue-600 h-2 rounded-full transition-all duration-300" 
                style={{ width: `${fillPercentage}%` }}
              ></div>
            </div>
            <div className="text-xs text-gray-500 mt-1">{fillPercentage.toFixed(1)}% filled</div>
          </div>
        );
      }
    },
    {
      header: 'Properties',
      key: 'properties',
      render: (ballast) => (
        <div className="text-sm">
          <div className="text-gray-900">Density: {ballast.density.toFixed(3)} kg/L</div>
          <div className="text-gray-500">Temp: {ballast.temperature.toFixed(1)}°C</div>
        </div>
      )
    },
    {
      header: 'Status',
      key: 'status',
      render: (ballast) => {
        const statusColors = {
          'Full': 'bg-red-100 text-red-800',
          'Filled': 'bg-blue-100 text-blue-800',
          'Half Full': 'bg-yellow-100 text-yellow-800',
          'Partially Filled': 'bg-orange-100 text-orange-800',
          'Empty': 'bg-gray-100 text-gray-800'
        };
        return (
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusColors[ballast.status] || 'bg-gray-100 text-gray-800'}`}>
            {ballast.status}
          </span>
        );
      }
    },
    {
      header: 'Location',
      key: 'location',
      render: (ballast) => (
        <div className="text-sm text-gray-900">{ballast.location}</div>
      )
    },
    {
      header: 'Condition',
      key: 'condition',
      render: (ballast) => {
        const conditionIcons = {
          'Excellent': <CheckCircle className="h-4 w-4 text-green-500" />,
          'Good': <CheckCircle className="h-4 w-4 text-blue-500" />,
          'Fair': <AlertTriangle className="h-4 w-4 text-yellow-500" />,
          'Poor': <AlertTriangle className="h-4 w-4 text-red-500" />
        };
        return (
          <div className="flex items-center text-sm">
            {conditionIcons[ballast.condition]}
            <span className="ml-2 text-gray-900">{ballast.condition}</span>
          </div>
        );
      }
    },
    {
      header: 'Last Updated',
      key: 'lastUpdated',
      render: (ballast) => (
        <div className="text-sm text-gray-500">
          {new Date(ballast.lastUpdated).toLocaleDateString()} <br />
          {new Date(ballast.lastUpdated).toLocaleTimeString()}
        </div>
      )
    }
  ];

  const handleAdd = () => {
    console.log('Add new ballast tank');
  };

  const handleEdit = (ballast) => {
    console.log('Edit ballast:', ballast);
  };

  const handleDelete = (ballast) => {
    console.log('Delete ballast:', ballast);
  };

  const handleView = (ballast) => {
    console.log('View ballast:', ballast);
  };

  const totalCapacity = ballasts.reduce((sum, ballast) => sum + ballast.capacity, 0);
  const totalVolume = ballasts.reduce((sum, ballast) => sum + ballast.currentVolume, 0);
  const averageFillPercentage = ballasts.length > 0 ? (totalVolume / totalCapacity) * 100 : 0;

  return (
    <EntityPageLayout
      title="Ballast Tanks Management"
      description="Monitor and manage ballast tank operations across your fleet for optimal vessel stability and performance."
      data={ballasts}
      columns={columns}
      onAdd={handleAdd}
      onEdit={handleEdit}
      onDelete={handleDelete}
      onView={handleView}
      loading={loading}
      searchPlaceholder="Search ballast tanks by name, vessel, location..."
    >
      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Droplets className="h-8 w-8 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Total Tanks</p>
              <p className="text-2xl font-semibold text-gray-900">{ballasts.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <TrendingUp className="h-8 w-8 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Total Capacity</p>
              <p className="text-2xl font-semibold text-gray-900">{totalCapacity.toLocaleString()} m³</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <TrendingDown className="h-8 w-8 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Current Volume</p>
              <p className="text-2xl font-semibold text-gray-900">{totalVolume.toLocaleString()} m³</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <CheckCircle className="h-8 w-8 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Average Fill</p>
              <p className="text-2xl font-semibold text-gray-900">{averageFillPercentage.toFixed(1)}%</p>
            </div>
          </div>
        </div>
      </div>
    </EntityPageLayout>
  );
};

export default BallaststPage;
