import React, { useState, useEffect } from "react";
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  TextField,
  Button,
  MenuItem,
  DialogActions,
  CircularProgress,
  Alert,
  Box,
  Typography
} from "@mui/material";
import { getPorts } from "../../api/portsApi";
import { getRoles } from "../../api/rolesApi";
import { updateUser } from "../../api/usersApi";

const ProfileEditForm = ({ user, open, onClose, onSave }) => {
  const [formData, setFormData] = useState({
    fullName: '',
    email: '',
    roleId: '',
    portId: null
  });
  const [ports, setPorts] = useState([]);
  const [roles, setRoles] = useState([]);
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchFormData = async () => {
      if (!open || !user) return;
      
      try {
        setLoading(true);
        setError(null);
        
        const [portsData, rolesData] = await Promise.all([
          getPorts(),
          getRoles()
        ]);
        
        setPorts(portsData);
        setRoles(rolesData);
        
        // Initialize form with current user data
        setFormData({
          fullName: user.fullName || '',
          email: user.email || '',
          roleId: user.role?.id || user.roleId || '',
          portId: user.port?.id || user.portId || null
        });
      } catch (err) {
        console.error("Failed to load form data:", err);
        setError("Failed to load required data. Please try again later.");
      } finally {
        setLoading(false);
      }
    };

    fetchFormData();
  }, [open, user]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ 
      ...prev, 
      [name]: value === '' ? null : value 
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setSubmitting(true);
    setError(null);

    try {
      // Validate required fields
      if (!formData.fullName || !formData.email || !formData.roleId) {
        throw new Error("Please fill all required fields");
      }

      const updateData = {
        email: formData.email,
        fullName: formData.fullName,
        roleId: formData.roleId,
        portId: formData.portId,
        isActive: true
      };

      await updateUser(user.id, updateData);
      
      const updatedRole = roles.find(r => r.id === formData.roleId);
      const updatedPort = ports.find(p => p.id === formData.portId);
      
      onSave({
        ...updateData,
        roleName: updatedRole?.name,
        portName: updatedPort?.name,
        role: updatedRole ? { id: updatedRole.id, name: updatedRole.name } : null,
        port: updatedPort ? { id: updatedPort.id, name: updatedPort.name } : null
      });
    } catch (err) {
      console.error("Failed to update user:", err);
      setError(err.response?.data?.message || err.message || "Failed to update profile");
    } finally {
      setSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!submitting) {
      onClose();
    }
  };

  return (
    <Dialog 
      open={open} 
      onClose={handleClose} 
      maxWidth="sm" 
      fullWidth
      disableEscapeKeyDown={submitting}
    >
      <DialogTitle sx={{ 
        borderBottom: '1px solid #eee', 
        py: 2,
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        <Typography variant="h6">Edit Profile</Typography>
        {loading && <CircularProgress size={24} />}
      </DialogTitle>
      
      <DialogContent dividers sx={{ pt: 3 }}>
        {loading ? (
          <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
            <CircularProgress />
          </Box>
        ) : (
          <form onSubmit={handleSubmit}>
            {error && (
              <Alert 
                severity="error" 
                sx={{ mb: 3 }}
                onClose={() => setError(null)}
              >
                {error}
              </Alert>
            )}

            <TextField
              fullWidth
              label="Full Name"
              name="fullName"
              value={formData.fullName}
              onChange={handleChange}
              margin="normal"
              required
              sx={{ mb: 2 }}
              inputProps={{ maxLength: 100 }}
            />

            <TextField
              fullWidth
              label="Email"
              name="email"
              type="email"
              value={formData.email}
              onChange={handleChange}
              margin="normal"
              required
              sx={{ mb: 2 }}
              inputProps={{ maxLength: 100 }}
            />

            <TextField
              select
              fullWidth
              label="Role"
              name="roleId"
              value={formData.roleId}
              onChange={handleChange}
              margin="normal"
              required
              sx={{ mb: 2 }}
              disabled={roles.length === 0}
            >
              {roles.length === 0 ? (
                <MenuItem disabled>Loading roles...</MenuItem>
              ) : (
                roles.map(role => (
                  <MenuItem key={role.id} value={role.id}>
                    {role.name}
                  </MenuItem>
                ))
              )}
            </TextField>

            <TextField
              select
              fullWidth
              label="Assigned Port"
              name="portId"
              value={formData.portId || ''}
              onChange={handleChange}
              margin="normal"
              disabled={ports.length === 0}
            >
              {ports.length === 0 ? (
                <MenuItem disabled>Loading ports...</MenuItem>
              ) : (
                <>
                  <MenuItem value="">No port assigned</MenuItem>
                  {ports.map(port => (
                    <MenuItem key={port.id} value={port.id}>
                      {port.name}
                    </MenuItem>
                  ))}
                </>
              )}
            </TextField>

            <DialogActions sx={{ mt: 3, px: 0 }}>
              <Button 
                onClick={handleClose} 
                disabled={submitting}
                sx={{ mr: 1 }}
                color="inherit"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                variant="contained"
                color="primary"
                disabled={submitting || loading}
                sx={{ minWidth: 120 }}
              >
                {submitting ? (
                  <>
                    <CircularProgress size={24} sx={{ mr: 1 }} />
                    Saving
                  </>
                ) : (
                  "Save Changes"
                )}
              </Button>
            </DialogActions>
          </form>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default ProfileEditForm;