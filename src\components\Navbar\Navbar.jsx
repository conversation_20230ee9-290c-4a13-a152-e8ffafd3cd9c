import React, { useState } from "react";
import {
  App<PERSON><PERSON>,
  Too<PERSON><PERSON>,
  Typography,
  IconButton,
  Menu,
  MenuItem,
  Box,
  useTheme,
  useMediaQuery,
  Avatar,
  Divider,
  <PERSON><PERSON>
} from "@mui/material";
import MenuIcon from "@mui/icons-material/Menu";
import AccountCircleIcon from "@mui/icons-material/AccountCircle";
import ExitToAppIcon from "@mui/icons-material/ExitToApp";
import PersonOutlineIcon from '@mui/icons-material/PersonOutline';
import ContactMailIcon from '@mui/icons-material/ContactMail';
import { useNavigate } from "react-router-dom";
import { useAuth } from "../../context/AuthContext";

const Navbar = ({ toggleDashboardDrawer }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  const [anchorEl, setAnchorEl] = useState(null);
  const navigate = useNavigate();
  const { isAuthenticated, user, logout } = useAuth();

  const handleMenuOpen = (event) => setAnchorEl(event.currentTarget);
  const handleMenuClose = () => setAnchorEl(null);
  
  const handleLogout = () => {
    logout();
    handleMenuClose();
  };

  const handleProfileRedirect = () => {
    handleMenuClose();
    navigate("/profile");
  };

  const handleContactRedirect = () => {
    handleMenuClose();
    navigate("/contact");
  };

  return (
    <AppBar
      position="fixed"
      sx={{
        zIndex: theme.zIndex.drawer + 1,
        backgroundColor: "black",
        boxShadow: theme.shadows[4],
        color: 'white',
        borderBottom: "1px solid rgba(255, 255, 255, 0.12)"
      }}
    >
      <Toolbar>
        {isMobile && toggleDashboardDrawer && (
          <IconButton
            color="inherit"
            aria-label="open dashboard drawer"
            edge="start"
            onClick={toggleDashboardDrawer}
            sx={{ mr: 2, color: "grey.400" }}
          >
            <MenuIcon />
          </IconButton>
        )}

        <Typography
          variant="h6"
          noWrap
          component="div"
          sx={{ flexGrow: 1, fontWeight: "bold" }}
        >
          DraftSurvey
        </Typography>

        {isAuthenticated ? (
          <>
            <Box sx={{ display: 'flex', alignItems: 'center', mr: 2 }}>
              <Typography variant="subtitle2" sx={{ mr: 1 }}>
                {user?.username || user?.fullName}
              </Typography>
              <Typography variant="caption" sx={{ 
                backgroundColor: theme.palette.primary.main,
                borderRadius: '12px',
                px: 1,
                py: 0.5
              }}>
                {user?.role?.name || user?.roleName}
              </Typography>
            </Box>

            <IconButton
              onClick={handleMenuOpen}
              sx={{ color: "white" }}
            >
              <Avatar sx={{ 
                width: 32, 
                height: 32,
                bgcolor: theme.palette.primary.main 
              }}>
                {(user?.username?.charAt(0) || user?.fullName?.charAt(0) || 'U').toUpperCase()}
              </Avatar>
            </IconButton>

            <Menu
              anchorEl={anchorEl}
              open={Boolean(anchorEl)}
              onClose={handleMenuClose}
              anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
              transformOrigin={{ vertical: "top", horizontal: "right" }}
              PaperProps={{
                sx: {
                  bgcolor: '#333333',
                  color: 'white',
                  boxShadow: theme.shadows[6],
                  borderRadius: theme.shape.borderRadius * 1.5,
                  minWidth: '200px'
                },
              }}
            >
              <Box sx={{ px: 2, py: 1 }}>
                <Typography variant="subtitle2">{user?.username || user?.fullName}</Typography>
                <Typography variant="caption" color="text.secondary">
                  {user?.role?.name || user?.roleName}
                </Typography>
              </Box>
              <Divider sx={{ my: 1, bgcolor: 'rgba(255,255,255,0.12)' }} />
              
              <MenuItem 
                onClick={handleProfileRedirect} 
                sx={{ '&:hover': { bgcolor: 'rgba(255,255,255,0.08)' } }}
              >
                <PersonOutlineIcon sx={{ mr: 1, color: "grey.400" }} /> Profile
              </MenuItem>
              
              <MenuItem 
                onClick={handleContactRedirect} 
                sx={{ '&:hover': { bgcolor: 'rgba(255,255,255,0.08)' } }}
              >
                <ContactMailIcon sx={{ mr: 1, color: "grey.400" }} /> Contact
              </MenuItem>
              
              <MenuItem 
                onClick={handleLogout} 
                sx={{ '&:hover': { bgcolor: 'rgba(255,255,255,0.08)' } }}
              >
                <ExitToAppIcon sx={{ mr: 1, color: theme.palette.error.main }} /> Logout
              </MenuItem>
            </Menu>
          </>
        ) : (
          <Button 
            color="inherit" 
            onClick={() => navigate('/login')}
            sx={{ textTransform: 'none' }}
          >
            Login
          </Button>
        )}
      </Toolbar>
    </AppBar>
  );
};

export default Navbar;