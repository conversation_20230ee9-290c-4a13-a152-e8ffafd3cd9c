﻿using System;
using System.ComponentModel;

namespace DraftSurvey.Domain.Entities
{
    public class TaskAssignment
    {
        public Guid Id { get; set; }
        public Guid DraftSurveyId { get; set; }
        public DraftSurvey DraftSurvey { get; set; }
        public Guid UserId { get; set; }
        public User User { get; set; }
        public DateTime AssignmentDate { get; set; }
        public DateTime? CompletionDate { get; set; }
        public string Status { get; set; } = "Assigned";
        public string Notes { get; set; }
        public TaskType TaskType { get; set; }
        public bool IsCompleted { get; set; } = false;
        public string TaskData { get; set; }
    }

    public enum TaskType
    {
        [Description("Ballast Management")]
        Ballast,

        [Description("Draught Marks Reading")]
        DraughtMarkReading,

        [Description("Draught Marks Correction")]
        DraughtMarksCorrection,

        [Description("Global Calculation")]
        DraughtSurveyGlobalCalculation,

        [Description("Fresh Water Tank")]
        FreshWaterTank,

        [Description("Global Calculation")]
        GlobalCalculation,

        [Description("Hold Inspection")]
        Inspection,

        [Description("Liquid Statement")]
        LiquidStatement,

        [Description("Sea Water Density")]
        SeaWaterDensityStatement
    }
}