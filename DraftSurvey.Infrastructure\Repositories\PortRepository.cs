using Microsoft.EntityFrameworkCore;
using DraftSurvey.Domain.Entities;
using DraftSurvey.Domain.Interfaces;
using DraftSurvey.Infrastructure.Data;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace DraftSurvey.Infrastructure.Repositories
{
    public class PortRepository : IPortRepository
    {
        private readonly DraftSurveyDbContext _context;

        public PortRepository(DraftSurveyDbContext context)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
        }

        public async Task<Port> GetByIdAsync(Guid id)
        {
            return await _context.Ports
                .Include(p => p.Escales)
                .Include(p => p.Users)
                .FirstOrDefaultAsync(p => p.Id == id);
        }

        public async Task<IEnumerable<Port>> GetAllAsync()
        {
            return await _context.Ports
                .AsNoTracking()
                .ToListAsync();
        }

        public async Task AddAsync(Port port)
        {
            await _context.Ports.AddAsync(port);
        }

        public async Task UpdateAsync(Port port)
        {
            _context.Ports.Update(port);
        }

        public async Task DeleteAsync(Guid id)
        {
            var port = await GetByIdAsync(id);
            if (port != null)
            {
                _context.Ports.Remove(port);
            }
        }

        public async Task<int> GetEscalesCountAsync(Guid portId)
        {
            return await _context.Escales
                .CountAsync(e => e.PortId == portId);
        }

        public async Task<int> GetUsersCountAsync(Guid portId)
        {
            return await _context.Users
                .CountAsync(u => u.PortId == portId);
        }

        public async Task UpdateWaterDensityAsync(Guid portId, double newDensity)
        {
            var port = await GetByIdAsync(portId);
            if (port != null)
            {
                port.StandardWaterDensity = newDensity;
                _context.Entry(port).Property(p => p.StandardWaterDensity).IsModified = true;
            }
        }

        public async Task<IEnumerable<Port>> SearchAsync(string searchTerm)
        {
            return await _context.Ports
                .Where(p => p.Name.Contains(searchTerm) ||
                           p.Code.Contains(searchTerm) ||
                           p.Country.Contains(searchTerm))
                .AsNoTracking()
                .ToListAsync();
        }
    }
}