﻿using AutoMapper;
using DraftSurvey.Application.DTOs;
using DraftSurvey.Domain.Entities;
using DraftSurvey.Domain.Interfaces;
using DraftSurvey.Infrastructure.Data;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace DraftSurvey.WebApi.Controllers
{
    [Authorize]
    [ApiController]
    [Route("api/[controller]")]
    public class LiquidStatementController : ControllerBase
    {
        private readonly ILiquidStatementRepository _repository;
        private readonly IMapper _mapper;
        private readonly DraftSurveyDbContext _context;

        public LiquidStatementController(
            ILiquidStatementRepository repository,
            IMapper mapper,
            DraftSurveyDbContext context)
        {
            _repository = repository;
            _mapper = mapper;
            _context = context;
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<LiquidStatementDto>> GetById(Guid id)
        {
            var liquidStatement = await _repository.GetByIdAsync(id);
            if (liquidStatement == null) return NotFound();

            // Load related Liquid data
            await _context.Entry(liquidStatement)
                .Reference(ls => ls.Liquid)
                .LoadAsync();

            return Ok(_mapper.Map<LiquidStatementDto>(liquidStatement));
        }

        [HttpGet("by-survey/{surveyId}")]
        public async Task<ActionResult<IEnumerable<LiquidStatementDto>>> GetBySurvey(Guid surveyId)
        {
            var liquidStatements = await _repository.GetBySurveyIdAsync(surveyId);

            // Load related Liquid data for each statement
            foreach (var statement in liquidStatements)
            {
                await _context.Entry(statement)
                    .Reference(ls => ls.Liquid)
                    .LoadAsync();
            }

            return Ok(_mapper.Map<IEnumerable<LiquidStatementDto>>(liquidStatements));
        }

        [HttpPost]
        [Authorize(Roles = "Admin,TeamLead,Inspector")]
        public async Task<ActionResult<LiquidStatementDto>> Create([FromBody] LiquidStatementCreateDto dto)
        {
            var liquidStatement = _mapper.Map<LiquidStatement>(dto);

            // Load the related Liquid to get StandardDensity
            var liquid = await _context.Liquids.FindAsync(dto.LiquidId);
            if (liquid == null)
            {
                return BadRequest("Invalid LiquidId specified");
            }

            // The CalculatedWeight is computed automatically in the entity
            // No need to set it manually as it's a computed property

            await _repository.AddAsync(liquidStatement);
            await _repository.SaveChangesAsync();

            return CreatedAtAction(
                nameof(GetById),
                new { id = liquidStatement.Id },
                _mapper.Map<LiquidStatementDto>(liquidStatement));
        }

        [HttpPut("{id}")]
        [Authorize(Roles = "Admin,TeamLead,Inspector")]
        public async Task<IActionResult> Update(Guid id, [FromBody] LiquidStatementUpdateDto dto)
        {
            var liquidStatement = await _repository.GetByIdAsync(id);
            if (liquidStatement == null) return NotFound();

            _mapper.Map(dto, liquidStatement);

            // The CalculatedWeight will be recomputed automatically when accessed
            // because it's a computed property in the entity

            await _repository.UpdateAsync(liquidStatement);
            await _repository.SaveChangesAsync();
            return NoContent();
        }

        [HttpDelete("{id}")]
        [Authorize(Roles = "Admin,TeamLead")]
        public async Task<IActionResult> Delete(Guid id)
        {
            var liquidStatement = await _repository.GetByIdAsync(id);
            if (liquidStatement == null) return NotFound();

            await _repository.DeleteAsync(id);
            await _repository.SaveChangesAsync();
            return NoContent();
        }
    }
}