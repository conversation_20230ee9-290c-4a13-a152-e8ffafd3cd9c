// Utility function to create alpha colors without importing alpha
export const createAlphaColor = (color, opacity) => {
  // Convert hex color to rgba
  if (color.startsWith('#')) {
    const hex = color.replace('#', '');
    const r = parseInt(hex.substr(0, 2), 16);
    const g = parseInt(hex.substr(2, 2), 16);
    const b = parseInt(hex.substr(4, 2), 16);
    return `rgba(${r}, ${g}, ${b}, ${opacity})`;
  }
  
  // If it's already an rgb/rgba color, just modify the alpha
  if (color.startsWith('rgb')) {
    const values = color.match(/\d+/g);
    if (values && values.length >= 3) {
      return `rgba(${values[0]}, ${values[1]}, ${values[2]}, ${opacity})`;
    }
  }
  
  // Fallback for theme colors - use predefined alpha values
  const alphaColors = {
    'primary.main': `rgba(25, 118, 210, ${opacity})`,
    'secondary.main': `rgba(156, 39, 176, ${opacity})`,
    'success.main': `rgba(46, 125, 50, ${opacity})`,
    'warning.main': `rgba(237, 108, 2, ${opacity})`,
    'error.main': `rgba(211, 47, 47, ${opacity})`,
    'info.main': `rgba(2, 136, 209, ${opacity})`,
  };
  
  return alphaColors[color] || `rgba(0, 0, 0, ${opacity})`;
};
