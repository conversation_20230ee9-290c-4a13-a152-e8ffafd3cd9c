﻿using System;
using System.ComponentModel.DataAnnotations;

namespace DraftSurvey.Application.DTOs
{
    public class TaskAssignmentCreateDto
    {
        [Required]
        public Guid DraftSurveyId { get; set; }

        [Required]
        public Guid UserId { get; set; }

        public DateTime AssignmentDate { get; set; } = DateTime.UtcNow;

        [MaxLength(1000)]
        public string Notes { get; set; }

        [Required]
        public TaskType TaskType { get; set; }  // Utilisation de l'enum TaskType

        // Optionnel : données spécifiques à la tâche
        public string TaskData { get; set; }
    }

    public enum TaskType
    {
        Ballast,
        DraughtMarkReading,
        DraughtMarksCorrection,
        DraughtSurveyGlobalCalculation,
        FreshWaterTank,
        GlobalCalculation,
        Inspection,
        LiquidStatement,
        SeaWaterDensityStatement
    }
}