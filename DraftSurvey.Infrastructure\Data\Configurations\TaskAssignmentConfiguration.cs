using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using DraftSurvey.Domain.Entities;

namespace DraftSurvey.Infrastructure.Data.Configurations
{
    public class TaskAssignmentConfiguration : IEntityTypeConfiguration<TaskAssignment>
    {
        public void Configure(EntityTypeBuilder<TaskAssignment> builder)
        {
            // Définition de la clé primaire
            builder.HasKey(ta => ta.Id);

            // Configuration des propriétés
            builder.Property(ta => ta.Status)
                .IsRequired()
                .HasMaxLength(50)
                .HasDefaultValue("Assigned");

            builder.Property(ta => ta.Notes)
                .HasMaxLength(1000);

            builder.Property(ta => ta.TaskType)
                .IsRequired()
                .HasConversion<string>() // Stocke l'enum comme string dans la base
                .HasMaxLength(50);

            builder.Property(ta => ta.TaskData)
                .HasColumnType("TEXT"); // SQLite uses TEXT for large text data

            builder.Property(ta => ta.IsCompleted)
                .HasDefaultValue(false);

            builder.Property(ta => ta.AssignmentDate)
                .IsRequired()
                .HasDefaultValueSql("datetime('now')"); // SQLite datetime function

            // Relations
            builder.HasOne(ta => ta.DraftSurvey)
                .WithMany(ds => ds.TaskAssignments)
                .HasForeignKey(ta => ta.DraftSurveyId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.HasOne(ta => ta.User)
                .WithMany(u => u.TaskAssignments)
                .HasForeignKey(ta => ta.UserId)
                .OnDelete(DeleteBehavior.Restrict);

            // Index pour améliorer les performances des requêtes courantes
            builder.HasIndex(ta => ta.TaskType);
            builder.HasIndex(ta => ta.IsCompleted);
            builder.HasIndex(ta => ta.DraftSurveyId);
            builder.HasIndex(ta => ta.UserId);
        }
    }
}