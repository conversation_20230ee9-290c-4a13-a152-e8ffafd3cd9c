﻿// Inspection DTOs
public class InspectionDto
{
    public Guid Id { get; set; }
    public DateTime InspectionDate { get; set; }
    public string InspectorName { get; set; }
    public string Notes { get; set; }
    public Guid EscaleId { get; set; }
    public bool AllHoldsApproved { get; set; }
    public ICollection<HoldInspectionDto> HoldInspections { get; set; } = new List<HoldInspectionDto>();
    public ICollection<InspectionReportDto> InspectionReports { get; set; } = new List<InspectionReportDto>();
}

public class InspectionCreateDto
{
    public DateTime InspectionDate { get; set; }
    public string InspectorName { get; set; }
    public string Notes { get; set; }
    public Guid EscaleId { get; set; }
    public ICollection<HoldInspectionCreateDto> HoldInspections { get; set; } = new List<HoldInspectionCreateDto>();
}

public class InspectionUpdateDto
{
    public DateTime? InspectionDate { get; set; }
    public string InspectorName { get; set; }
    public string Notes { get; set; }
    public Guid EscaleId { get; set; }
}

public class HoldInspectionDto
{
    public Guid Id { get; set; }
    public Guid InspectionId { get; set; }
    public string HoldName { get; set; }
    public bool IsApproved { get; set; }
    public string Comments { get; set; }
}

public class HoldInspectionCreateDto
{
    public string HoldName { get; set; }
    public bool IsApproved { get; set; }
    public string Comments { get; set; }
    public string Condition { get; set; } = "Unknown"; // 👈 Valeur par défaut ou obligatoire
    public string? RejectionReason { get; set; } // Ajoute ceci

}

public class InspectionReportDto
{
    public Guid Id { get; set; }
    public Guid InspectionId { get; set; }
    public string ReportContent { get; set; }
    public DateTime GeneratedDate { get; set; }
}