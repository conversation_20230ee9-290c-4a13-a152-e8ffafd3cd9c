﻿using DraftSurvey.Domain.Entities;
using DraftSurvey.Domain.Interfaces;
using DraftSurvey.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace DraftSurvey.Infrastructure.Repositories
{
    public class InspectionRepository : IInspectionRepository
    {
        private readonly DraftSurveyDbContext _context;

        public InspectionRepository(DraftSurveyDbContext context)
        {
            _context = context;
        }

        public async Task<Inspection> GetByIdAsync(Guid id)
        {
            return await _context.Inspections
                .Include(i => i.HoldInspections)
                .Include(i => i.InspectionReports)
                .Include(i => i.Escale)
                .FirstOrDefaultAsync(i => i.Id == id);
        }

        public async Task<IEnumerable<Inspection>> GetAllAsync()
        {
            return await _context.Inspections
                .Include(i => i.HoldInspections)
                .Include(i => i.InspectionReports)
                .Include(i => i.Escale)
                .ToListAsync();
        }

        public async Task<IEnumerable<Inspection>> GetByEscaleIdAsync(Guid escaleId)
        {
            return await _context.Inspections
                .Where(i => i.EscaleId == escaleId)
                .Include(i => i.HoldInspections)
                .Include(i => i.InspectionReports)
                .ToListAsync();
        }

        public async Task AddAsync(Inspection inspection)
        {
            await _context.Inspections.AddAsync(inspection);
        }

        public async Task UpdateAsync(Inspection inspection)
        {
            _context.Entry(inspection).State = EntityState.Modified;
        }

        public async Task DeleteAsync(Guid id)
        {
            var inspection = await GetByIdAsync(id);
            if (inspection != null)
            {
                _context.Inspections.Remove(inspection);
            }
        }

        public async Task<HoldInspection> GetHoldInspectionByIdAsync(Guid id)
        {
            return await _context.HoldInspections.FindAsync(id);
        }

        public async Task<IEnumerable<HoldInspection>> GetHoldInspectionsByInspectionIdAsync(Guid inspectionId)
        {
            return await _context.HoldInspections
                .Where(hi => hi.InspectionId == inspectionId)
                .ToListAsync();
        }

        public async Task AddHoldInspectionAsync(HoldInspection holdInspection)
        {
            await _context.HoldInspections.AddAsync(holdInspection);
        }

        public async Task UpdateHoldInspectionAsync(HoldInspection holdInspection)
        {
            _context.HoldInspections.Update(holdInspection);
        }

        public async Task DeleteHoldInspectionAsync(Guid id)
        {
            var holdInspection = await GetHoldInspectionByIdAsync(id);
            if (holdInspection != null)
            {
                _context.HoldInspections.Remove(holdInspection);
            }
        }

        public async Task<InspectionReport> GetReportByIdAsync(Guid id)
        {
            return await _context.InspectionReports.FindAsync(id);
        }

        public async Task AddReportAsync(InspectionReport report)
        {
            await _context.InspectionReports.AddAsync(report);
        }

        public async Task UpdateReportAsync(InspectionReport report)
        {
            _context.InspectionReports.Update(report);
        }

        public async Task DeleteReportAsync(Guid id)
        {
            var report = await GetReportByIdAsync(id);
            if (report != null)
            {
                _context.InspectionReports.Remove(report);
            }
        }

        public async Task<bool> AllHoldsApprovedAsync(Guid inspectionId)
        {
            var inspection = await GetByIdAsync(inspectionId);
            return inspection?.AllHoldsApproved ?? false;
        }

        public async Task CompleteInspectionAsync(Guid inspectionId)
        {
            var inspection = await GetByIdAsync(inspectionId);
            if (inspection != null)
            {
                _context.Entry(inspection).State = EntityState.Modified;
                await _context.SaveChangesAsync();
            }
        }

        public async Task SaveChangesAsync()
        {
            await _context.SaveChangesAsync();
        }
    }
}