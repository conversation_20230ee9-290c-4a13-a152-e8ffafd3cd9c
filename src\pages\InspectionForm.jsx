import React, { useState, useEffect } from 'react';
import { 
  Box, 
  TextField, 
  Button, 
  Typography, 
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  MenuItem,
  Alert,
  CircularProgress
} from '@mui/material';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import { getInspectionsBySurvey, createInspection, addHoldInspection } from '../api/inspectionApi';

const InspectionForm = ({ surveyId, onComplete }) => {
  const [inspection, setInspection] = useState(null);
  const [holds, setHolds] = useState([]);
  const [openDialog, setOpenDialog] = useState(false);
  const [newHold, setNewHold] = useState({
    holdName: '',
    condition: 'Good',
    comments: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [completed, setCompleted] = useState(false);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const data = await getInspectionsBySurvey(surveyId);
        if (data) {
          setInspection(data);
          setHolds(data.holdInspections || []);
        }
        setLoading(false);
      } catch (err) {
        setError('Erreur lors du chargement des données d\'inspection');
        setLoading(false);
      }
    };
    
    fetchData();
  }, [surveyId]);

  const handleCreateInspection = async () => {
    try {
      setLoading(true);
      const data = await createInspection({
        draftSurveyId: surveyId,
        inspectionDate: new Date().toISOString(),
        inspectorName: 'Inspecteur Principal'
      });
      setInspection(data);
      setLoading(false);
    } catch (err) {
      setError('Erreur lors de la création de l\'inspection');
      setLoading(false);
    }
  };

  const handleAddHold = async () => {
    try {
      setLoading(true);
      const hold = await addHoldInspection(inspection.id, newHold);
      setHolds([...holds, hold]);
      setOpenDialog(false);
      setNewHold({
        holdName: '',
        condition: 'Good',
        comments: ''
      });
      setLoading(false);
    } catch (err) {
      setError('Erreur lors de l\'ajout de la cale');
      setLoading(false);
    }
  };

  const handleMarkComplete = () => {
    setCompleted(true);
    onComplete();
  };

  if (loading) return <CircularProgress />;

  return (
    <Paper sx={{ p: 3, mb: 3 }}>
      <Typography variant="h6" gutterBottom>Inspection des Cales</Typography>
      
      {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>}

      {!inspection ? (
        <Button 
          variant="contained" 
          onClick={handleCreateInspection}
        >
          Commencer l'Inspection
        </Button>
      ) : (
        <>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
            <Typography>
              Inspection #{inspection.id.slice(0, 8)} - {new Date(inspection.inspectionDate).toLocaleDateString()}
            </Typography>
            <Button 
              variant="outlined" 
              onClick={() => setOpenDialog(true)}
            >
              Ajouter une Cale
            </Button>
          </Box>

          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Cale</TableCell>
                  <TableCell>État</TableCell>
                  <TableCell>Commentaires</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {holds.map((hold) => (
                  <TableRow key={hold.id}>
                    <TableCell>{hold.holdName}</TableCell>
                    <TableCell>
                      <Chip 
                        label={hold.condition} 
                        color={hold.condition === 'Good' ? 'success' : 'warning'} 
                      />
                    </TableCell>
                    <TableCell>{hold.comments}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>

          {!completed && (
            <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end' }}>
              <Button
                variant="contained"
                color="success"
                startIcon={<CheckCircleIcon />}
                onClick={handleMarkComplete}
              >
                Marquer comme Terminé
              </Button>
            </Box>
          )}

          {completed && (
            <Alert severity="success" sx={{ mt: 2 }}>
              Cette sous-tâche a été marquée comme terminée
            </Alert>
          )}
        </>
      )}

      <Dialog open={openDialog} onClose={() => setOpenDialog(false)}>
        <DialogTitle>Ajouter une Cale</DialogTitle>
        <DialogContent>
          <TextField
            label="Nom de la Cale"
            fullWidth
            margin="normal"
            value={newHold.holdName}
            onChange={(e) => setNewHold({...newHold, holdName: e.target.value})}
          />
          <TextField
            label="Condition"
            select
            fullWidth
            margin="normal"
            value={newHold.condition}
            onChange={(e) => setNewHold({...newHold, condition: e.target.value})}
          >
            <MenuItem value="Good">Bon</MenuItem>
            <MenuItem value="Fair">Moyen</MenuItem>
            <MenuItem value="Poor">Mauvais</MenuItem>
          </TextField>
          <TextField
            label="Commentaires"
            multiline
            rows={3}
            fullWidth
            margin="normal"
            value={newHold.comments}
            onChange={(e) => setNewHold({...newHold, comments: e.target.value})}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDialog(false)}>Annuler</Button>
          <Button onClick={handleAddHold} variant="contained">Ajouter</Button>
        </DialogActions>
      </Dialog>
    </Paper>
  );
};

export default InspectionForm;