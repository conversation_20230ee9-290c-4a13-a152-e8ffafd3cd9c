﻿// Dans DraftSurvey.Domain/Entities/AvisChargement.cs
using System;

namespace DraftSurvey.Domain.Entities
{
    public class AvisChargement
    {
        public Guid Id { get; set; }
        public Guid EscaleId { get; set; }
        public Escale Escale { get; set; }

        public string CargoName { get; set; } // Nom de la cargaison
        public double QuantityExpected { get; set; } // Quantité de cargaison attendue
        public string Unit { get; set; } // Unité (Tonnes, m3, etc.)
        public DateTime ExpectedLoadingDate { get; set; }
        public DateTime? ActualLoadingDate { get; set; }
        public string LoadingPort { get; set; } // Nom du port de chargement (si différent de l'escale actuelle)
        public string DischargingPort { get; set; } // Nom du port de déchargement
        public string Status { get; set; } = "Planned"; // "Planned", "InProgress", "Completed", "Cancelled"
        public string Notes { get; set; }
        public Guid CreatedByUserId { get; set; }
        public User CreatedByUser { get; set; }
    }
}