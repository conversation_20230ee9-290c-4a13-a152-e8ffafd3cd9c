import React, { useState } from "react";
import { Box, Button, Typography, useTheme } from "@mui/material"; // Import useTheme
import PortList from "../components/Ports/PortsList";
import PortForm from "../components/Ports/PortForm";
import AddIcon from "@mui/icons-material/Add";
import Navbar from "../components/Navbar/Navbar";

const PortPage = () => {
  const theme = useTheme(); // Access the theme

  const [openForm, setOpenForm] = useState(false);
  const [selectedPortId, setSelectedPortId] = useState(null);
  const [refreshKey, setRefreshKey] = useState(0);

  const handleOpenForm = () => {
    setSelectedPortId(null);
    setOpenForm(true);
  };

  const handleEditPort = (portId) => {
    setSelectedPortId(portId);
    setOpenForm(true);
  };

  const handleFormSubmit = () => {
    setRefreshKey((prev) => prev + 1);
  };

  return (
    <>
      <Navbar />
      <Box
        sx={{
          flexGrow: 1, // Allows content to take available space
          pt: { xs: '72px', sm: '80px', md: '88px' }, // Padding top to clear fixed Navbar
          pb: 4, // Padding bottom
          px: { xs: 2, sm: 4, md: 6 }, // Responsive horizontal padding
          display: "flex",
          flexDirection: "column",
          alignItems: "center", // Center content horizontally
          bgcolor: theme.palette.grey[100], // Subtle off-white background for the page section
          minHeight: "100vh", // Ensure it takes full viewport height
        }}
      >
        <Box
          sx={{
            width: '100%',
            maxWidth: 1200, // Max width for content container
            padding: { xs: 2, sm: 4 }, // Responsive padding inside the main card
            borderRadius: theme.shape.borderRadius * 2,
            boxShadow: theme.shadows[8], // Stronger shadow for the main card
            bgcolor: "white", // Hard white background for the main content card
            color: theme.palette.text.primary,
            border: `1px solid ${theme.palette.grey[300]}`, // Subtle light grey border
          }}
        >
          <Typography
            variant="h4"
            sx={{
              fontWeight: "bold",
              mb: 3, // Increased margin for spacing
              color: theme.palette.text.primary,
            }}
          >
            Port Management
          </Typography>

          <Box sx={{ width: "100%", display: "flex", justifyContent: "flex-start", mb: 3 }}>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={handleOpenForm}
              sx={{
                bgcolor: theme.palette.success.main, // Use theme success color
                color: "white",
                fontWeight: "bold",
                "&:hover": {
                  bgcolor: theme.palette.success.dark,
                  transform: "translateY(-2px)",
                  boxShadow: theme.shadows[4],
                },
                px: { xs: 2, sm: 3 }, // Responsive padding
                py: { xs: 1, sm: 1.5 }, // Responsive padding
                borderRadius: theme.shape.borderRadius,
              }}
            >
              Add Port
            </Button>
          </Box>

          <PortList onEdit={handleEditPort} refreshTrigger={refreshKey} />
        </Box>
        <PortForm
          open={openForm}
          onClose={() => setOpenForm(false)}
          onSubmit={handleFormSubmit}
          portId={selectedPortId}
        />
      </Box>
    </>
  );
};

export default PortPage;