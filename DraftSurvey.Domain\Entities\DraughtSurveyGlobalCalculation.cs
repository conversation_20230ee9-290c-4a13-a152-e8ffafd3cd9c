﻿// Dans DraftSurvey.Domain/Entities/DraughtSurveyGlobalCalculation.cs
using System;

namespace DraftSurvey.Domain.Entities
{
    public class DraughtSurveyGlobalCalculation
    {
        public Guid Id { get; set; }
        public Guid DraftSurveyId { get; set; }
        public DraftSurvey DraftSurvey { get; set; } // Relation One-to-One

        public DateTime CalculationDate { get; set; }
        public double InitialDisplacement { get; set; }
        public double FinalDisplacement { get; set; }
        public double TotalBallastWater { get; set; }
        public double TotalFreshWater { get; set; }
        public double TotalFuelOil { get; set; }
        public double TotalDieselOil { get; set; }
        public double TotalLubricatingOil { get; set; }
        public double TotalConstant { get; set; }
        public double NetCargoWeight { get; set; }
        public double Difference { get; set; }
        public string CalculationStatus { get; set; }
        public Guid? VerifiedByUserId { get; set; }
        public User VerifiedByUser { get; set; }
        public DateTime? VerifiedDate { get; set; }

        // NOUVELLES PROPRIÉTÉS AJOUTÉES
        public double FirstTrimCorrection { get; set; }
        public double SecondTrimCorrection { get; set; }
        public bool IsComplete { get; set; } = false; // Défaut à false
    }
}