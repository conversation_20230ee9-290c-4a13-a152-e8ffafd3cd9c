// In DraftSurvey.Infrastructure/Repositories/VesselRepository.cs

using DraftSurvey.Domain.Entities;
using DraftSurvey.Domain.Interfaces;
using DraftSurvey.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace DraftSurvey.Infrastructure.Repositories
{
    public class VesselRepository : IVesselRepository
    {
        private readonly DraftSurveyDbContext _context;

        public VesselRepository(DraftSurveyDbContext context)
        {
            _context = context;
        }

        public async Task<Vessel> GetByIdAsync(Guid id)
        {
            return await _context.Vessels.FindAsync(id);
        }

        public async Task<Vessel> GetByIMONumberAsync(string imo)
        {
            return await _context.Vessels
                .FirstOrDefaultAsync(v => v.IMONumber == imo);
        }

        public async Task<IEnumerable<Vessel>> SearchByNameAsync(string name)
        {
            return await _context.Vessels
                .Where(v => v.Name.Contains(name))
                .ToListAsync();
        }

        public async Task AddAsync(Vessel vessel)
        {
            await _context.Vessels.AddAsync(vessel);
            // REMOVED: await _context.SaveChangesAsync();
        }

        public async Task UpdateAsync(Vessel vessel)
        {
            _context.Vessels.Update(vessel);
            // REMOVED: await _context.SaveChangesAsync();
        }

        public async Task UpdateHydrostaticDataAsync(Guid vesselId, double displacementFactor, double tpc)
        {
            var vessel = await GetByIdAsync(vesselId);
            if (vessel != null)
            {
                vessel.DisplacementFactor = displacementFactor;
                vessel.TPC = tpc;
                await UpdateAsync(vessel); // Calls the UoW-consistent UpdateAsync
            }
        }

        public async Task<IEnumerable<Vessel>> GetAllAsync()
        {
            return await _context.Vessels.ToListAsync();
        }

        public async Task DeleteAsync(Guid id)
        {
            var vessel = await _context.Vessels.FindAsync(id);
            if (vessel != null)
            {
                _context.Vessels.Remove(vessel);
                // REMOVED: await _context.SaveChangesAsync();
            }
        }

        public async Task<bool> IMONumberExistsAsync(string imoNumber)
        {
            return await _context.Vessels
                .AnyAsync(v => v.IMONumber == imoNumber);
        }

        public async Task SaveChangesAsync()
        {
            await _context.SaveChangesAsync();
        }
    }
}