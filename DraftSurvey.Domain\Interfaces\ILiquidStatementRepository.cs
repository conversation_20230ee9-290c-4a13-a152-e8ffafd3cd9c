﻿using DraftSurvey.Domain.Entities;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;

public interface ILiquidStatementRepository
{
    Task<LiquidStatement> GetByIdAsync(Guid id);
    Task<IEnumerable<LiquidStatement>> GetBySurveyIdAsync(Guid surveyId);
    Task AddAsync(LiquidStatement liquidStatement);
    Task UpdateAsync(LiquidStatement liquidStatement);
    Task DeleteAsync(Guid id);
    Task SaveChangesAsync();
}