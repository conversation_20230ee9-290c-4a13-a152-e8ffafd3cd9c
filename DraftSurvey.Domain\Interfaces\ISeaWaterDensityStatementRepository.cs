﻿// In DraftSurvey.Domain/Interfaces/ISeaWaterDensityStatementRepository.cs
using System;
using System.Threading.Tasks;
using DraftSurvey.Domain.Entities;

namespace DraftSurvey.Domain.Interfaces
{
    public interface ISeaWaterDensityStatementRepository
    {
        Task<SeaWaterDensityStatement> GetByIdAsync(Guid id); // <-- C'EST LA LIGNE MANQUANTE

        Task<SeaWaterDensityStatement> GetBySurveyIdAsync(Guid surveyId); // Singular relation
        Task AddAsync(SeaWaterDensityStatement statement);
        Task UpdateAsync(SeaWaterDensityStatement statement);
        Task DeleteAsync(Guid id); // Can delete the statement directly
        Task SaveChangesAsync();
    }
}