import React from 'react';
import BallastForm from './BallastForm';
import FreshWaterTankForm from './FreshWaterTankForm';
import SeaWaterDensityForm from './SeaWaterDensityForm';
import InspectionForm from './InspectionForm';
import LiquidStatementPage from './LiquidStatementPage';
import DraughtMarkReadingsForm from './DraughtMarksForm';
import SurveyCalculationPage from './SurveyCalculationPage';

const TaskFormSwitcher = ({ taskType, surveyId, onComplete }) => {
  switch(taskType) {
    case 'Ballast':
      return <BallastForm surveyId={surveyId} onComplete={onComplete} />;
    case 'FreshWaterTank':
      return <FreshWaterTankForm surveyId={surveyId} onComplete={onComplete} />;
    case 'SeaWaterDensityStatement':
      return <SeaWaterDensityForm surveyId={surveyId} onComplete={onComplete} />;
    case 'Inspection':
      return <InspectionForm surveyId={surveyId} onComplete={onComplete} />;
    case 'LiquidStatement':
      return <LiquidStatementPage surveyId={surveyId} />;
    case 'DraughtMarkReading':
      return <DraughtMarkReadingsForm surveyId={surveyId} onComplete={onComplete} />;
    case 'DraughtSurveyGlobalCalculation':
    case 'GlobalCalculation':
      return <SurveyCalculationPage surveyId={surveyId} />;
    default:
      return <div>Formulaire non disponible pour ce type de tâche</div>;
  }
};

export default TaskFormSwitcher;