﻿// <auto-generated />
using System;
using DraftSurvey.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace DraftSurvey.Infrastructure.Migrations
{
    [DbContext(typeof(DraftSurveyDbContext))]
    [Migration("20250620221056_UpdateInspectionModel")]
    partial class UpdateInspectionModel
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.4")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("DraftSurvey.Domain.Entities.AvisChargement", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("ActualLoadingDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("CargoName")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("DischargingPort")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<Guid>("EscaleId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("ExpectedLoadingDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("LoadingPort")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Notes")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<double>("QuantityExpected")
                        .HasColumnType("float");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Unit")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("Id");

                    b.HasIndex("CreatedByUserId");

                    b.HasIndex("EscaleId");

                    b.ToTable("AvisChargements");
                });

            modelBuilder.Entity("DraftSurvey.Domain.Entities.Ballast", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<double>("Density")
                        .HasColumnType("float");

                    b.Property<Guid>("DraftSurveyId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("TankName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<double>("Volume")
                        .HasColumnType("float");

                    b.HasKey("Id");

                    b.HasIndex("DraftSurveyId");

                    b.ToTable("Ballasts");
                });

            modelBuilder.Entity("DraftSurvey.Domain.Entities.Document", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ContentType")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DocumentType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("FilePath")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<long>("FileSize")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("UploadDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("UploadedByUserId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("UploadedByUserId");

                    b.ToTable("Documents");
                });

            modelBuilder.Entity("DraftSurvey.Domain.Entities.DraftSurvey", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("EscaleId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("SurveyNumber")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("VesselId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("EscaleId");

                    b.HasIndex("VesselId");

                    b.ToTable("DraftSurveys");
                });

            modelBuilder.Entity("DraftSurvey.Domain.Entities.DraughtMarkReading", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<double>("AftMean")
                        .HasColumnType("float");

                    b.Property<Guid>("DraftSurveyId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<double>("ForwardMean")
                        .HasColumnType("float");

                    b.Property<double>("MidshipMean")
                        .HasColumnType("float");

                    b.Property<double>("PortAft")
                        .HasColumnType("float");

                    b.Property<double>("PortForward")
                        .HasColumnType("float");

                    b.Property<double>("PortMidship")
                        .HasColumnType("float");

                    b.Property<DateTime>("ReadingTime")
                        .HasColumnType("datetime2");

                    b.Property<double>("StarboardAft")
                        .HasColumnType("float");

                    b.Property<double>("StarboardForward")
                        .HasColumnType("float");

                    b.Property<double>("StarboardMidship")
                        .HasColumnType("float");

                    b.HasKey("Id");

                    b.HasIndex("DraftSurveyId")
                        .IsUnique();

                    b.ToTable("DraughtMarkReadings");
                });

            modelBuilder.Entity("DraftSurvey.Domain.Entities.DraughtMarksCorrection", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("AppliedToLocation")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("CorrectionDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("CorrectionType")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<Guid>("DraftSurveyId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Notes")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("Unit")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<double>("Value")
                        .HasColumnType("float");

                    b.HasKey("Id");

                    b.HasIndex("DraftSurveyId");

                    b.ToTable("DraughtMarksCorrections");
                });

            modelBuilder.Entity("DraftSurvey.Domain.Entities.DraughtSurveyDocument", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("AttachedDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("DocumentId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("DraftSurveyId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Notes")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("UsageType")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("Id");

                    b.HasIndex("DocumentId");

                    b.HasIndex("DraftSurveyId");

                    b.ToTable("DraughtSurveyDocuments");
                });

            modelBuilder.Entity("DraftSurvey.Domain.Entities.DraughtSurveyGlobalCalculation", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CalculationDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("CalculationStatus")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<double>("Difference")
                        .HasColumnType("float");

                    b.Property<Guid>("DraftSurveyId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<double>("FinalDisplacement")
                        .HasColumnType("float");

                    b.Property<double>("FirstTrimCorrection")
                        .HasColumnType("float");

                    b.Property<double>("InitialDisplacement")
                        .HasColumnType("float");

                    b.Property<bool>("IsComplete")
                        .HasColumnType("bit");

                    b.Property<double>("NetCargoWeight")
                        .HasColumnType("float");

                    b.Property<double>("SecondTrimCorrection")
                        .HasColumnType("float");

                    b.Property<double>("TotalBallastWater")
                        .HasColumnType("float");

                    b.Property<double>("TotalConstant")
                        .HasColumnType("float");

                    b.Property<double>("TotalDieselOil")
                        .HasColumnType("float");

                    b.Property<double>("TotalFreshWater")
                        .HasColumnType("float");

                    b.Property<double>("TotalFuelOil")
                        .HasColumnType("float");

                    b.Property<double>("TotalLubricatingOil")
                        .HasColumnType("float");

                    b.Property<Guid?>("VerifiedByUserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("VerifiedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("DraftSurveyId")
                        .IsUnique();

                    b.HasIndex("VerifiedByUserId");

                    b.ToTable("DraughtSurveyGlobalCalculations");
                });

            modelBuilder.Entity("DraftSurvey.Domain.Entities.Escale", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("ArrivalDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DepartureDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("PortId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Reference")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<Guid>("VesselId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("PortId");

                    b.HasIndex("VesselId");

                    b.ToTable("Escales");
                });

            modelBuilder.Entity("DraftSurvey.Domain.Entities.FreshWaterTank", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<double>("Density")
                        .HasColumnType("float");

                    b.Property<Guid>("DraftSurveyId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("MeasurementTime")
                        .HasColumnType("datetime2");

                    b.Property<string>("Notes")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("TankName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<double>("Volume")
                        .HasColumnType("float");

                    b.HasKey("Id");

                    b.HasIndex("DraftSurveyId");

                    b.ToTable("FreshWaterTanks");
                });

            modelBuilder.Entity("DraftSurvey.Domain.Entities.HoldInspection", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Condition")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("HoldNumber")
                        .HasColumnType("int");

                    b.Property<Guid>("InspectionId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("IsApproved")
                        .HasColumnType("bit");

                    b.Property<string>("Notes")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("RejectionReason")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.HasKey("Id");

                    b.HasIndex("InspectionId");

                    b.ToTable("HoldInspections");
                });

            modelBuilder.Entity("DraftSurvey.Domain.Entities.Inspection", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("EscaleId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("InspectionDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("InspectorName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Notes")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("EscaleId");

                    b.ToTable("Inspections");
                });

            modelBuilder.Entity("DraftSurvey.Domain.Entities.InspectionReport", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("ApprovedByUserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("ApprovedDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("GeneratedByUserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("GeneratedDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("InspectionId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ReportContent")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ReportType")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("Id");

                    b.HasIndex("ApprovedByUserId");

                    b.HasIndex("GeneratedByUserId");

                    b.HasIndex("InspectionId");

                    b.ToTable("InspectionReports");
                });

            modelBuilder.Entity("DraftSurvey.Domain.Entities.Liquid", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<bool>("IsHazardous")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<double>("StandardDensity")
                        .HasColumnType("float");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Unit")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.HasKey("Id");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.ToTable("Liquids");
                });

            modelBuilder.Entity("DraftSurvey.Domain.Entities.LiquidStatement", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("DraftSurveyId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("LiquidId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<double?>("MeasuredDensity")
                        .HasColumnType("float");

                    b.Property<DateTime>("MeasurementTime")
                        .HasColumnType("datetime2");

                    b.Property<string>("Notes")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("TankName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<double>("Volume")
                        .HasColumnType("float");

                    b.HasKey("Id");

                    b.HasIndex("DraftSurveyId");

                    b.HasIndex("LiquidId");

                    b.ToTable("LiquidStatements");
                });

            modelBuilder.Entity("DraftSurvey.Domain.Entities.Port", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<string>("Country")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<decimal>("StandardWaterDensity")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18,6)")
                        .HasDefaultValue(1.025m);

                    b.HasKey("Id");

                    b.ToTable("Ports");
                });

            modelBuilder.Entity("DraftSurvey.Domain.Entities.Role", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("CanManageAllPorts")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<bool>("CanManageUsers")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("Id");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.ToTable("Roles", (string)null);

                    b.HasData(
                        new
                        {
                            Id = new Guid("00000000-0000-0000-0000-000000000001"),
                            CanManageAllPorts = true,
                            CanManageUsers = true,
                            Description = "Administrateur système avec tous les droits",
                            Name = "Admin"
                        },
                        new
                        {
                            Id = new Guid("00000000-0000-0000-0000-000000000002"),
                            CanManageAllPorts = true,
                            CanManageUsers = false,
                            Description = "Chef d'équipe avec des droits étendus",
                            Name = "TeamLead"
                        },
                        new
                        {
                            Id = new Guid("00000000-0000-0000-0000-000000000003"),
                            CanManageAllPorts = false,
                            CanManageUsers = false,
                            Description = "Utilisateur standard avec droits limités",
                            Name = "Agent"
                        });
                });

            modelBuilder.Entity("DraftSurvey.Domain.Entities.SeaWaterDensityStatement", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<double>("Density")
                        .HasColumnType("float");

                    b.Property<double>("DensityValue")
                        .HasColumnType("float");

                    b.Property<Guid>("DraftSurveyId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Location")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime>("MeasurementDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("MeasurementMethod")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("MeasurementTime")
                        .HasColumnType("datetime2");

                    b.Property<string>("Notes")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<double>("Temperature")
                        .HasColumnType("float");

                    b.HasKey("Id");

                    b.HasIndex("DraftSurveyId")
                        .IsUnique();

                    b.ToTable("SeaWaterDensityStatements");
                });

            modelBuilder.Entity("DraftSurvey.Domain.Entities.TaskAssignment", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("AssignmentDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("BallastCalculationAssigned")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<bool>("BallastCalculationCompleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<bool>("BallastSoundingAssigned")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<bool>("BallastSoundingCompleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<DateTime?>("CompletionDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("DocumentsAssigned")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<bool>("DocumentsCompleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<bool>("DraftMarksReadingsAssigned")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<bool>("DraftMarksReadingsCompleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<Guid>("DraftSurveyId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("FreshWaterCalculationAssigned")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<bool>("FreshWaterCalculationCompleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<bool>("FreshWaterSoundingAssigned")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<bool>("FreshWaterSoundingCompleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<bool>("GeneralInfoAssigned")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<bool>("GeneralInfoCompleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<bool>("GlobalCalculationAssigned")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<bool>("GlobalCalculationCompleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<bool>("HoldInspectionAssigned")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<bool>("HoldInspectionCompleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<bool>("LiquidStatementAssigned")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<bool>("LiquidStatementCompleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<string>("Notes")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<bool>("SeaWaterDensityStatementAssigned")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<bool>("SeaWaterDensityStatementCompleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("DraftSurveyId");

                    b.HasIndex("UserId");

                    b.ToTable("TaskAssignments");
                });

            modelBuilder.Entity("DraftSurvey.Domain.Entities.User", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("FullName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true);

                    b.Property<string>("PasswordHash")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid?>("PortId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("RoleId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Username")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("Id");

                    b.HasIndex("PortId");

                    b.HasIndex("RoleId");

                    b.ToTable("Users");
                });

            modelBuilder.Entity("DraftSurvey.Domain.Entities.Vessel", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<double>("BreadthMoulded")
                        .HasColumnType("float");

                    b.Property<string>("CallSign")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<double>("DepthMoulded")
                        .HasColumnType("float");

                    b.Property<double>("DisplacementAtDesignDraft")
                        .HasColumnType("float");

                    b.Property<double>("DisplacementFactor")
                        .HasColumnType("float");

                    b.Property<string>("Flag")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("IMONumber")
                        .IsRequired()
                        .HasMaxLength(15)
                        .HasColumnType("nvarchar(15)");

                    b.Property<decimal>("LCF")
                        .HasColumnType("decimal(18,2)");

                    b.Property<double>("LCG")
                        .HasColumnType("float");

                    b.Property<decimal>("LengthBetweenPerpendiculars")
                        .HasColumnType("decimal(18,2)");

                    b.Property<double>("LengthOverall")
                        .HasColumnType("float");

                    b.Property<double>("LightDisplacement")
                        .HasColumnType("float");

                    b.Property<double>("MTC")
                        .HasColumnType("float");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Notes")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal>("SummerDeadweight")
                        .HasColumnType("decimal(18,2)");

                    b.Property<double>("TPC")
                        .HasColumnType("float");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("Vessels");
                });

            modelBuilder.Entity("DraftSurvey.Domain.Entities.AvisChargement", b =>
                {
                    b.HasOne("DraftSurvey.Domain.Entities.User", "CreatedByUser")
                        .WithMany()
                        .HasForeignKey("CreatedByUserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("DraftSurvey.Domain.Entities.Escale", "Escale")
                        .WithMany("AvisChargements")
                        .HasForeignKey("EscaleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CreatedByUser");

                    b.Navigation("Escale");
                });

            modelBuilder.Entity("DraftSurvey.Domain.Entities.Ballast", b =>
                {
                    b.HasOne("DraftSurvey.Domain.Entities.DraftSurvey", "DraftSurvey")
                        .WithMany("Ballasts")
                        .HasForeignKey("DraftSurveyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DraftSurvey");
                });

            modelBuilder.Entity("DraftSurvey.Domain.Entities.Document", b =>
                {
                    b.HasOne("DraftSurvey.Domain.Entities.User", "UploadedByUser")
                        .WithMany()
                        .HasForeignKey("UploadedByUserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("UploadedByUser");
                });

            modelBuilder.Entity("DraftSurvey.Domain.Entities.DraftSurvey", b =>
                {
                    b.HasOne("DraftSurvey.Domain.Entities.Escale", "Escale")
                        .WithMany("DraftSurveys")
                        .HasForeignKey("EscaleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("DraftSurvey.Domain.Entities.Vessel", "Vessel")
                        .WithMany("DraftSurveys")
                        .HasForeignKey("VesselId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Escale");

                    b.Navigation("Vessel");
                });

            modelBuilder.Entity("DraftSurvey.Domain.Entities.DraughtMarkReading", b =>
                {
                    b.HasOne("DraftSurvey.Domain.Entities.DraftSurvey", "DraftSurvey")
                        .WithOne("DraughtMarkReading")
                        .HasForeignKey("DraftSurvey.Domain.Entities.DraughtMarkReading", "DraftSurveyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DraftSurvey");
                });

            modelBuilder.Entity("DraftSurvey.Domain.Entities.DraughtMarksCorrection", b =>
                {
                    b.HasOne("DraftSurvey.Domain.Entities.DraftSurvey", "DraftSurvey")
                        .WithMany("DraughtMarksCorrections")
                        .HasForeignKey("DraftSurveyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DraftSurvey");
                });

            modelBuilder.Entity("DraftSurvey.Domain.Entities.DraughtSurveyDocument", b =>
                {
                    b.HasOne("DraftSurvey.Domain.Entities.Document", "Document")
                        .WithMany()
                        .HasForeignKey("DocumentId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("DraftSurvey.Domain.Entities.DraftSurvey", "DraftSurvey")
                        .WithMany("DraughtSurveyDocuments")
                        .HasForeignKey("DraftSurveyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Document");

                    b.Navigation("DraftSurvey");
                });

            modelBuilder.Entity("DraftSurvey.Domain.Entities.DraughtSurveyGlobalCalculation", b =>
                {
                    b.HasOne("DraftSurvey.Domain.Entities.DraftSurvey", "DraftSurvey")
                        .WithOne("DraughtSurveyGlobalCalculation")
                        .HasForeignKey("DraftSurvey.Domain.Entities.DraughtSurveyGlobalCalculation", "DraftSurveyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("DraftSurvey.Domain.Entities.User", "VerifiedByUser")
                        .WithMany()
                        .HasForeignKey("VerifiedByUserId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("DraftSurvey");

                    b.Navigation("VerifiedByUser");
                });

            modelBuilder.Entity("DraftSurvey.Domain.Entities.Escale", b =>
                {
                    b.HasOne("DraftSurvey.Domain.Entities.Port", "Port")
                        .WithMany("Escales")
                        .HasForeignKey("PortId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("DraftSurvey.Domain.Entities.Vessel", "Vessel")
                        .WithMany("Escales")
                        .HasForeignKey("VesselId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Port");

                    b.Navigation("Vessel");
                });

            modelBuilder.Entity("DraftSurvey.Domain.Entities.FreshWaterTank", b =>
                {
                    b.HasOne("DraftSurvey.Domain.Entities.DraftSurvey", "DraftSurvey")
                        .WithMany("FreshWaterTanks")
                        .HasForeignKey("DraftSurveyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DraftSurvey");
                });

            modelBuilder.Entity("DraftSurvey.Domain.Entities.HoldInspection", b =>
                {
                    b.HasOne("DraftSurvey.Domain.Entities.Inspection", "Inspection")
                        .WithMany("HoldInspections")
                        .HasForeignKey("InspectionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Inspection");
                });

            modelBuilder.Entity("DraftSurvey.Domain.Entities.Inspection", b =>
                {
                    b.HasOne("DraftSurvey.Domain.Entities.Escale", "Escale")
                        .WithMany("Inspections")
                        .HasForeignKey("EscaleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Escale");
                });

            modelBuilder.Entity("DraftSurvey.Domain.Entities.InspectionReport", b =>
                {
                    b.HasOne("DraftSurvey.Domain.Entities.User", "ApprovedByUser")
                        .WithMany()
                        .HasForeignKey("ApprovedByUserId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("DraftSurvey.Domain.Entities.User", "GeneratedByUser")
                        .WithMany()
                        .HasForeignKey("GeneratedByUserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("DraftSurvey.Domain.Entities.Inspection", "Inspection")
                        .WithMany("InspectionReports")
                        .HasForeignKey("InspectionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ApprovedByUser");

                    b.Navigation("GeneratedByUser");

                    b.Navigation("Inspection");
                });

            modelBuilder.Entity("DraftSurvey.Domain.Entities.LiquidStatement", b =>
                {
                    b.HasOne("DraftSurvey.Domain.Entities.DraftSurvey", "DraftSurvey")
                        .WithMany("LiquidStatements")
                        .HasForeignKey("DraftSurveyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("DraftSurvey.Domain.Entities.Liquid", "Liquid")
                        .WithMany("Statements")
                        .HasForeignKey("LiquidId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("DraftSurvey");

                    b.Navigation("Liquid");
                });

            modelBuilder.Entity("DraftSurvey.Domain.Entities.SeaWaterDensityStatement", b =>
                {
                    b.HasOne("DraftSurvey.Domain.Entities.DraftSurvey", "DraftSurvey")
                        .WithOne("SeaWaterDensityStatement")
                        .HasForeignKey("DraftSurvey.Domain.Entities.SeaWaterDensityStatement", "DraftSurveyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DraftSurvey");
                });

            modelBuilder.Entity("DraftSurvey.Domain.Entities.TaskAssignment", b =>
                {
                    b.HasOne("DraftSurvey.Domain.Entities.DraftSurvey", "DraftSurvey")
                        .WithMany("TaskAssignments")
                        .HasForeignKey("DraftSurveyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("DraftSurvey.Domain.Entities.User", "User")
                        .WithMany("TaskAssignments")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("DraftSurvey");

                    b.Navigation("User");
                });

            modelBuilder.Entity("DraftSurvey.Domain.Entities.User", b =>
                {
                    b.HasOne("DraftSurvey.Domain.Entities.Port", "Port")
                        .WithMany("Users")
                        .HasForeignKey("PortId");

                    b.HasOne("DraftSurvey.Domain.Entities.Role", "Role")
                        .WithMany("Users")
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Port");

                    b.Navigation("Role");
                });

            modelBuilder.Entity("DraftSurvey.Domain.Entities.DraftSurvey", b =>
                {
                    b.Navigation("Ballasts");

                    b.Navigation("DraughtMarkReading")
                        .IsRequired();

                    b.Navigation("DraughtMarksCorrections");

                    b.Navigation("DraughtSurveyDocuments");

                    b.Navigation("DraughtSurveyGlobalCalculation")
                        .IsRequired();

                    b.Navigation("FreshWaterTanks");

                    b.Navigation("LiquidStatements");

                    b.Navigation("SeaWaterDensityStatement")
                        .IsRequired();

                    b.Navigation("TaskAssignments");
                });

            modelBuilder.Entity("DraftSurvey.Domain.Entities.Escale", b =>
                {
                    b.Navigation("AvisChargements");

                    b.Navigation("DraftSurveys");

                    b.Navigation("Inspections");
                });

            modelBuilder.Entity("DraftSurvey.Domain.Entities.Inspection", b =>
                {
                    b.Navigation("HoldInspections");

                    b.Navigation("InspectionReports");
                });

            modelBuilder.Entity("DraftSurvey.Domain.Entities.Liquid", b =>
                {
                    b.Navigation("Statements");
                });

            modelBuilder.Entity("DraftSurvey.Domain.Entities.Port", b =>
                {
                    b.Navigation("Escales");

                    b.Navigation("Users");
                });

            modelBuilder.Entity("DraftSurvey.Domain.Entities.Role", b =>
                {
                    b.Navigation("Users");
                });

            modelBuilder.Entity("DraftSurvey.Domain.Entities.User", b =>
                {
                    b.Navigation("TaskAssignments");
                });

            modelBuilder.Entity("DraftSurvey.Domain.Entities.Vessel", b =>
                {
                    b.Navigation("DraftSurveys");

                    b.Navigation("Escales");
                });
#pragma warning restore 612, 618
        }
    }
}
