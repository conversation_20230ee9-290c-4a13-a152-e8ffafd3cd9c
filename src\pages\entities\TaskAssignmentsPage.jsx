import React, { useState, useEffect } from 'react';
import EntityPageLayout from '../../components/Shared/EntityPageLayout';
import { Users, Clock, CheckCircle, AlertCircle, Calendar, User } from 'lucide-react';

const TaskAssignmentsPage = () => {
  const [taskAssignments, setTaskAssignments] = useState([]);
  const [loading, setLoading] = useState(true);

  // Mock data - replace with actual API call
  useEffect(() => {
    const mockTaskAssignments = [
      {
        id: 1,
        assignmentId: "TA-2024-001",
        taskTitle: "Pre-loading Draft Survey",
        vesselName: "MSC OSCAR",
        assignedTo: "<PERSON>",
        assignedBy: "<PERSON>",
        priority: "High",
        status: "In Progress",
        assignedDate: "2024-01-15T08:00:00Z",
        dueDate: "2024-01-15T12:00:00Z",
        completedDate: null,
        estimatedHours: 4,
        actualHours: 2.5,
        department: "Survey Team",
        location: "Port of Rotterdam",
        description: "Conduct comprehensive pre-loading draft survey for cargo operations"
      },
      {
        id: 2,
        assignmentId: "TA-2024-002",
        taskTitle: "Safety Equipment Inspection",
        vesselName: "EVER GIVEN",
        assignedTo: "<PERSON>",
        assignedBy: "David Kim",
        priority: "Medium",
        status: "Completed",
        assignedDate: "2024-01-14T10:00:00Z",
        dueDate: "2024-01-14T16:00:00Z",
        completedDate: "2024-01-14T15:30:00Z",
        estimatedHours: 6,
        actualHours: 5.5,
        department: "Safety Team",
        location: "Engine Room",
        description: "Complete safety equipment inspection and certification"
      },
      {
        id: 3,
        assignmentId: "TA-2024-003",
        taskTitle: "Cargo Hold Cleaning Verification",
        vesselName: "MAERSK MADRID",
        assignedTo: "Emma Rodriguez",
        assignedBy: "John Anderson",
        priority: "Low",
        status: "Pending",
        assignedDate: "2024-01-16T06:00:00Z",
        dueDate: "2024-01-16T14:00:00Z",
        completedDate: null,
        estimatedHours: 8,
        actualHours: 0,
        department: "Inspection Team",
        location: "All Cargo Holds",
        description: "Verify cargo hold cleanliness before next loading operation"
      },
      {
        id: 4,
        assignmentId: "TA-2024-004",
        taskTitle: "Ballast Water Treatment Check",
        vesselName: "EVER GIVEN",
        assignedTo: "Lisa Johnson",
        assignedBy: "Sarah Mitchell",
        priority: "Critical",
        status: "Overdue",
        assignedDate: "2024-01-13T09:00:00Z",
        dueDate: "2024-01-13T17:00:00Z",
        completedDate: null,
        estimatedHours: 3,
        actualHours: 1.5,
        department: "Environmental Team",
        location: "Ballast System",
        description: "Urgent ballast water treatment system inspection and compliance check"
      },
      {
        id: 5,
        assignmentId: "TA-2024-005",
        taskTitle: "Documentation Review",
        vesselName: "MSC OSCAR",
        assignedTo: "Kevin Wilson",
        assignedBy: "David Kim",
        priority: "Medium",
        status: "In Review",
        assignedDate: "2024-01-15T11:00:00Z",
        dueDate: "2024-01-15T18:00:00Z",
        completedDate: null,
        estimatedHours: 2,
        actualHours: 1.8,
        department: "Documentation Team",
        location: "Office",
        description: "Review and validate all vessel documentation for compliance"
      }
    ];

    setTimeout(() => {
      setTaskAssignments(mockTaskAssignments);
      setLoading(false);
    }, 1000);
  }, []);

  const columns = [
    {
      header: 'Assignment Details',
      key: 'assignmentId',
      render: (assignment) => (
        <div className="flex items-center">
          <div className="flex-shrink-0 h-10 w-10">
            <div className="h-10 w-10 rounded-full bg-indigo-100 flex items-center justify-center">
              <Users className="h-5 w-5 text-indigo-600" />
            </div>
          </div>
          <div className="ml-4">
            <div className="text-sm font-medium text-gray-900">{assignment.assignmentId}</div>
            <div className="text-sm text-gray-500">{assignment.vesselName}</div>
          </div>
        </div>
      )
    },
    {
      header: 'Task Information',
      key: 'taskTitle',
      render: (assignment) => (
        <div>
          <div className="text-sm font-medium text-gray-900">{assignment.taskTitle}</div>
          <div className="text-sm text-gray-500">{assignment.department}</div>
        </div>
      )
    },
    {
      header: 'Assigned To',
      key: 'assignedTo',
      render: (assignment) => (
        <div className="text-sm">
          <div className="text-gray-900 flex items-center">
            <User className="h-4 w-4 text-gray-400 mr-1" />
            {assignment.assignedTo}
          </div>
          <div className="text-gray-500">by {assignment.assignedBy}</div>
        </div>
      )
    },
    {
      header: 'Priority',
      key: 'priority',
      render: (assignment) => {
        const priorityConfig = {
          'Critical': { color: 'bg-red-100 text-red-800 border-red-200', icon: AlertCircle },
          'High': { color: 'bg-orange-100 text-orange-800 border-orange-200', icon: AlertCircle },
          'Medium': { color: 'bg-yellow-100 text-yellow-800 border-yellow-200', icon: Clock },
          'Low': { color: 'bg-green-100 text-green-800 border-green-200', icon: CheckCircle }
        };
        const config = priorityConfig[assignment.priority] || priorityConfig['Medium'];
        const IconComponent = config.icon;
        
        return (
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${config.color}`}>
            <IconComponent className="w-3 h-3 mr-1" />
            {assignment.priority}
          </span>
        );
      }
    },
    {
      header: 'Status',
      key: 'status',
      render: (assignment) => {
        const statusConfig = {
          'Completed': { color: 'bg-green-100 text-green-800', icon: CheckCircle },
          'In Progress': { color: 'bg-blue-100 text-blue-800', icon: Clock },
          'In Review': { color: 'bg-purple-100 text-purple-800', icon: Clock },
          'Pending': { color: 'bg-yellow-100 text-yellow-800', icon: Clock },
          'Overdue': { color: 'bg-red-100 text-red-800', icon: AlertCircle }
        };
        const config = statusConfig[assignment.status] || statusConfig['Pending'];
        const IconComponent = config.icon;
        
        return (
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
            <IconComponent className="w-3 h-3 mr-1" />
            {assignment.status}
          </span>
        );
      }
    },
    {
      header: 'Time Tracking',
      key: 'timeTracking',
      render: (assignment) => {
        const progressPercentage = (assignment.actualHours / assignment.estimatedHours) * 100;
        const isOvertime = assignment.actualHours > assignment.estimatedHours;
        
        return (
          <div className="text-sm">
            <div className="text-gray-900">
              {assignment.actualHours}h / {assignment.estimatedHours}h
            </div>
            <div className="w-full bg-gray-200 rounded-full h-1.5 mt-1">
              <div 
                className={`h-1.5 rounded-full transition-all duration-300 ${isOvertime ? 'bg-red-600' : 'bg-blue-600'}`}
                style={{ width: `${Math.min(progressPercentage, 100)}%` }}
              ></div>
            </div>
            {isOvertime && (
              <div className="text-xs text-red-600 mt-1">Overtime</div>
            )}
          </div>
        );
      }
    },
    {
      header: 'Due Date',
      key: 'dueDate',
      render: (assignment) => {
        const dueDate = new Date(assignment.dueDate);
        const now = new Date();
        const isOverdue = dueDate < now && assignment.status !== 'Completed';
        const hoursUntilDue = Math.ceil((dueDate - now) / (1000 * 60 * 60));
        
        return (
          <div className="text-sm">
            <div className={`flex items-center ${isOverdue ? 'text-red-600' : 'text-gray-900'}`}>
              <Calendar className="h-4 w-4 mr-1" />
              {dueDate.toLocaleDateString()}
            </div>
            <div className={`text-xs ${isOverdue ? 'text-red-600' : 'text-gray-500'}`}>
              {dueDate.toLocaleTimeString()}
            </div>
            {isOverdue && assignment.status !== 'Completed' && (
              <div className="text-xs text-red-600 font-medium">Overdue</div>
            )}
          </div>
        );
      }
    },
    {
      header: 'Location',
      key: 'location',
      render: (assignment) => (
        <div className="text-sm text-gray-900">{assignment.location}</div>
      )
    }
  ];

  const handleAdd = () => {
    console.log('Add new task assignment');
  };

  const handleEdit = (assignment) => {
    console.log('Edit task assignment:', assignment);
  };

  const handleDelete = (assignment) => {
    console.log('Delete task assignment:', assignment);
  };

  const handleView = (assignment) => {
    console.log('View task assignment:', assignment);
  };

  const completedTasks = taskAssignments.filter(t => t.status === 'Completed').length;
  const inProgressTasks = taskAssignments.filter(t => t.status === 'In Progress').length;
  const overdueTasks = taskAssignments.filter(t => t.status === 'Overdue').length;
  const totalEstimatedHours = taskAssignments.reduce((sum, t) => sum + t.estimatedHours, 0);
  const totalActualHours = taskAssignments.reduce((sum, t) => sum + t.actualHours, 0);

  return (
    <EntityPageLayout
      title="Task Assignments Management"
      description="Manage task assignments, track progress, and monitor team productivity across all vessel operations."
      data={taskAssignments}
      columns={columns}
      onAdd={handleAdd}
      onEdit={handleEdit}
      onDelete={handleDelete}
      onView={handleView}
      loading={loading}
      searchPlaceholder="Search assignments by task, assignee, vessel..."
    >
      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Users className="h-8 w-8 text-indigo-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Total Assignments</p>
              <p className="text-2xl font-semibold text-gray-900">{taskAssignments.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Completed</p>
              <p className="text-2xl font-semibold text-gray-900">{completedTasks}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Clock className="h-8 w-8 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">In Progress</p>
              <p className="text-2xl font-semibold text-gray-900">{inProgressTasks}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <AlertCircle className="h-8 w-8 text-red-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Overdue</p>
              <p className="text-2xl font-semibold text-gray-900">{overdueTasks}</p>
            </div>
          </div>
        </div>
      </div>
    </EntityPageLayout>
  );
};

export default TaskAssignmentsPage;
