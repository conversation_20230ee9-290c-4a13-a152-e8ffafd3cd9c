﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;

namespace DraftSurvey.Infrastructure.Data
{
    public class DraftSurveyDbContextFactory : IDesignTimeDbContextFactory<DraftSurveyDbContext>
    {
        public DraftSurveyDbContext CreateDbContext(string[] args)
        {
            var optionsBuilder = new DbContextOptionsBuilder<DraftSurveyDbContext>();
            optionsBuilder.UseSqlServer("Server=DESKTOP-IQHHBBJ\\SQLEXPRESS;Database=DraftSurvey;Integrated Security=True;TrustServerCertificate=True");

            return new DraftSurveyDbContext(optionsBuilder.Options);
        }
    }
}