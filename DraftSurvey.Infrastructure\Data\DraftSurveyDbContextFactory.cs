﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;

namespace DraftSurvey.Infrastructure.Data
{
    public class DraftSurveyDbContextFactory : IDesignTimeDbContextFactory<DraftSurveyDbContext>
    {
        public DraftSurveyDbContext CreateDbContext(string[] args)
        {
            var optionsBuilder = new DbContextOptionsBuilder<DraftSurveyDbContext>();
            optionsBuilder.UseSqlite("Data Source=DraftSurvey.db");

            return new DraftSurveyDbContext(optionsBuilder.Options);
        }
    }
}