﻿// DraftSurvey.Application/DTOs/EscaleDto.cs
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace DraftSurvey.Application.DTOs
{
    // EscaleDto.cs
    public class EscaleDto
    {
        public Guid Id { get; set; }
        public string Reference { get; set; }
        public DateTime ArrivalDate { get; set; }
        public DateTime? DepartureDate { get; set; }
        public bool IsActive { get; set; } // Corresponds to Escale.Status
        public Guid VesselId { get; set; }
        public Guid PortId { get; set; }
    }

    // EscaleDetailsDto.cs
    public class EscaleDetailsDto : EscaleDto
    {
        public string VesselName { get; set; }
        public string PortName { get; set; }
        public int SurveyCount { get; set; }
        public List<AvisChargementDto> AvisChargements { get; set; } // NEW: Added for the AvisChargement collection
        public List<InspectionDto> Inspections { get; set; } // NEW: Added for Inspections linked to Escale
    }

    // EscaleCreateDto.cs
    public class EscaleCreateDto
    {
        [Required]
        public string Reference { get; set; }

        [Required]
        public Guid VesselId { get; set; }

        [Required]
        public Guid PortId { get; set; }

        public DateTime ArrivalDate { get; set; } = DateTime.UtcNow;
    }
}