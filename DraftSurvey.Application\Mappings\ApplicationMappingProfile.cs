using AutoMapper;
using DraftSurvey.Application.DTOs;
using DraftSurvey.Domain.Entities;

namespace DraftSurvey.Application.Mappings
{
    public class ApplicationMappingProfile : Profile
    {
        public ApplicationMappingProfile()
        {
            // User Mappings
            CreateMap<User, UserResponseDto>()
                .ForMember(dest => dest.RoleName, opt => opt.MapFrom(src => src.Role.Name))
                .ForMember(dest => dest.PortName, opt => opt.MapFrom(src => src.Port != null ? src.Port.Name : null));
            CreateMap<UserCreateDto, User>();

            // Port Mappings
            CreateMap<Port, PortDto>();
            CreateMap<Port, PortDetailsDto>();
            CreateMap<PortCreateDto, Port>();
            CreateMap<PortUpdateDto, Port>();

            // Vessel Mappings
            CreateMap<Vessel, VesselDto>();
            CreateMap<Vessel, VesselDetailsDto>();
            CreateMap<VesselCreateDto, Vessel>();
            CreateMap<VesselUpdateDto, Vessel>();

            // Hydrostatic data
            CreateMap<HydrostaticDataDto, Vessel>()
                .ForMember(dest => dest.DisplacementFactor, opt => opt.MapFrom(src => src.DisplacementFactor))
                .ForMember(dest => dest.TPC, opt => opt.MapFrom(src => src.TPC));

            // Draft Survey Mappings
            CreateMap<Domain.Entities.DraftSurvey, SurveyDto>();
            CreateMap<SurveyCreateDto, Domain.Entities.DraftSurvey>();

            // Escale Mapping
            CreateMap<Escale, EscaleDto>()
                .ForMember(dest => dest.IsActive, opt => opt.MapFrom(src => src.DepartureDate == null));

            CreateMap<Escale, EscaleDetailsDto>()
                .IncludeBase<Escale, EscaleDto>()
                .ForMember(dest => dest.VesselName, opt => opt.MapFrom(src => src.Vessel.Name))
                .ForMember(dest => dest.PortName, opt => opt.MapFrom(src => src.Port.Name))
                .ForMember(dest => dest.SurveyCount, opt => opt.MapFrom(src => src.DraftSurveys.Count));

            CreateMap<EscaleCreateDto, Escale>();

            // Ballast Mappings
            CreateMap<Ballast, BallastDto>();
            CreateMap<BallastCreateDto, Ballast>()
                .ForMember(dest => dest.Density, opt => opt.MapFrom(src => src.Density ?? 1.025));

            // Draught Mark Readings Mappings
            CreateMap<DraughtMarkReading, DraughtMarkReadingDto>();
            CreateMap<DraughtMarkReadingDto, DraughtMarkReading>()
                .ForMember(dest => dest.Id, opt => opt.Ignore())
                .AfterMap((src, dest) => dest.CalculateMeans());
            CreateMap<DraughtMarkReadingCreateDto, DraughtMarkReading>()
                .AfterMap((src, dest) => dest.CalculateMeans());

            // Roles Mappings
            CreateMap<Role, RoleDto>().ReverseMap();
            CreateMap<RoleCreateDto, Role>();
            CreateMap<RoleUpdateDto, Role>();

            // User Update Mappings
            CreateMap<UserUpdateDto, User>()
                .ForMember(dest => dest.Email, opt => opt.MapFrom(src => src.Email))
                .ForMember(dest => dest.FullName, opt => opt.MapFrom(src => src.FullName))
                .ForMember(dest => dest.RoleId, opt => opt.MapFrom(src => src.RoleId))
                .ForMember(dest => dest.PortId, opt => opt.MapFrom(src => src.PortId))
                .ForMember(dest => dest.IsActive, opt => opt.MapFrom(src => src.IsActive));
            CreateMap<User, UserUpdateDto>();

            // TaskAssignment Mappings (Nouvelle version)
            CreateMap<TaskAssignment, TaskAssignmentDto>()
                .ForMember(dest => dest.UserName, opt => opt.MapFrom(src => src.User.FullName))
                .ForMember(dest => dest.SurveyNumber, opt => opt.MapFrom(src => src.DraftSurvey.SurveyNumber))
                .ForMember(dest => dest.TaskTypeDisplay, opt => opt.MapFrom(src => src.TaskType.ToString()));

            CreateMap<TaskAssignmentCreateDto, TaskAssignment>()
                .ForMember(dest => dest.AssignmentDate, opt => opt.MapFrom(src => src.AssignmentDate));

            CreateMap<TaskAssignmentUpdateDto, TaskAssignment>()
                .ForMember(dest => dest.CompletionDate, opt => opt.MapFrom(src => src.IsCompleted == true ? DateTime.UtcNow : (DateTime?)null))
                .ForMember(dest => dest.IsCompleted, opt => opt.MapFrom(src => src.IsCompleted));

            // Mappings pour UpdateTaskCompletionDto
            CreateMap<UpdateTaskCompletionDto, TaskAssignment>()
                .ForMember(dest => dest.IsCompleted, opt => opt.MapFrom(src => src.IsCompleted))
                .ForMember(dest => dest.CompletionDate, opt => opt.MapFrom(src => src.IsCompleted ? DateTime.UtcNow : (DateTime?)null))
                .ForMember(dest => dest.Notes, opt => opt.MapFrom(src => src.CompletionNotes));

            // SeaWaterDensityStatement Mappings
            CreateMap<SeaWaterDensityStatement, SeaWaterDensityStatementDto>();
            CreateMap<SeaWaterDensityStatementCreateDto, SeaWaterDensityStatement>();
            CreateMap<SeaWaterDensityStatementUpdateDto, SeaWaterDensityStatement>();

            // DraughtMarksCorrection Mappings
            CreateMap<DraughtMarksCorrection, DraughtMarksCorrectionDto>();
            CreateMap<DraughtMarksCorrectionCreateDto, DraughtMarksCorrection>()
                .ForMember(dest => dest.CorrectionDate, opt => opt.MapFrom(src => src.CorrectionDate ?? DateTime.UtcNow));
            CreateMap<DraughtMarksCorrectionUpdateDto, DraughtMarksCorrection>()
                .ForMember(dest => dest.CorrectionDate, opt => opt.MapFrom(src => src.CorrectionDate ?? DateTime.UtcNow));

            // FreshWaterTank Mappings
            CreateMap<FreshWaterTank, FreshWaterTankDto>()
                .ForMember(dest => dest.TotalWeight, opt => opt.MapFrom(src => src.Volume * src.Density));
            CreateMap<FreshWaterTankCreateDto, FreshWaterTank>();
            CreateMap<FreshWaterTankUpdateDto, FreshWaterTank>();

            // Inspection Mappings
            CreateMap<Inspection, InspectionDto>();
            CreateMap<InspectionCreateDto, Inspection>();
            CreateMap<InspectionUpdateDto, Inspection>();

            // HoldInspection Mappings
            CreateMap<HoldInspection, HoldInspectionDto>()
                .ForMember(dest => dest.Comments, opt => opt.MapFrom(src => src.Notes))
                .ForMember(dest => dest.HoldName, opt => opt.MapFrom(src => $"Hold {src.HoldNumber}"));
            CreateMap<HoldInspectionCreateDto, HoldInspection>()
                .ForMember(dest => dest.Notes, opt => opt.MapFrom(src => src.Comments))
                .ForMember(dest => dest.Condition, opt => opt.MapFrom(src => string.IsNullOrEmpty(src.Condition) ? "Unknown" : src.Condition));

            // InspectionReport Mapping
            CreateMap<InspectionReport, InspectionReportDto>();

            // LiquidStatement Mappings
            CreateMap<LiquidStatement, LiquidStatementDto>();
            CreateMap<LiquidStatementCreateDto, LiquidStatement>();
            CreateMap<LiquidStatementUpdateDto, LiquidStatement>();
        }
    }
}