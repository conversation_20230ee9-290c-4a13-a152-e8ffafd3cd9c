﻿// Dans DraftSurvey.Infrastructure/Data/Configurations/DraughtSurveyGlobalCalculationConfiguration.cs
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using DraftSurvey.Domain.Entities;

namespace DraftSurvey.Infrastructure.Data.Configurations
{
    public class DraughtSurveyGlobalCalculationConfiguration : IEntityTypeConfiguration<DraughtSurveyGlobalCalculation>
    {
        public void Configure(EntityTypeBuilder<DraughtSurveyGlobalCalculation> builder)
        {
            builder.HasKey(dg => dg.Id);

            // Propriétés requises et longueurs maximales
            builder.Property(dg => dg.CalculationDate).IsRequired();
            builder.Property(dg => dg.NetCargoWeight).IsRequired();
            builder.Property(dg => dg.CalculationStatus).HasMaxLength(50).IsRequired();

            // Relations
            // Relation One-to-One avec DraftSurvey
            builder.HasOne(dg => dg.DraftSurvey)
                .WithOne(ds => ds.DraughtSurveyGlobalCalculation)
                .HasForeignKey<DraughtSurveyGlobalCalculation>(dg => dg.DraftSurveyId)
                .OnDelete(DeleteBehavior.Cascade); // Si le survey est supprimé, le calcul global l'est aussi

            // Relation avec User (VerifiedByUser)
            builder.HasOne(dg => dg.VerifiedByUser)
                .WithMany() // Pas de collection inverse explicite dans User si non nécessaire
                .HasForeignKey(dg => dg.VerifiedByUserId)
                .IsRequired(false) // VerifiedByUserId est nullable
                .OnDelete(DeleteBehavior.Restrict); // Ne pas supprimer l'utilisateur si des calculs y sont liés

            // Types de colonnes pour la précision
            builder.Property(dg => dg.InitialDisplacement).HasColumnType("float");
            builder.Property(dg => dg.FinalDisplacement).HasColumnType("float");
            builder.Property(dg => dg.TotalBallastWater).HasColumnType("float");
            builder.Property(dg => dg.TotalFreshWater).HasColumnType("float");
            builder.Property(dg => dg.TotalFuelOil).HasColumnType("float");
            builder.Property(dg => dg.TotalDieselOil).HasColumnType("float");
            builder.Property(dg => dg.TotalLubricatingOil).HasColumnType("float");
            builder.Property(dg => dg.TotalConstant).HasColumnType("float");
            builder.Property(dg => dg.NetCargoWeight).HasColumnType("float");
            builder.Property(dg => dg.Difference).HasColumnType("float");

            // NOUVELLES PROPRIÉTÉS AJOUTÉES
            builder.Property(dg => dg.FirstTrimCorrection).HasColumnType("float");
            builder.Property(dg => dg.SecondTrimCorrection).HasColumnType("float");
            builder.Property(dg => dg.IsComplete).IsRequired();
        }
    }
}