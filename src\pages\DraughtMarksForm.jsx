import React, { useState, useEffect } from 'react';
import { 
  Box, 
  TextField, 
  Button, 
  Typography, 
  Paper,
  Grid,
  Alert,
  CircularProgress
} from '@mui/material';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import { getReadingsBySurvey, createReading, updateReading } from '../api/draughtMarkReadingsApi';

const DraughtMarksForm = ({ surveyId, onComplete }) => {
  const [readings, setReadings] = useState({
    portForward: '',
    starboardForward: '',
    portMidship: '',
    starboardMidship: '',
    portAft: '',
    starboardAft: '',
    readingTime: new Date().toISOString().slice(0, 16)
  });
  const [existingReading, setExistingReading] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [completed, setCompleted] = useState(false);

  useEffect(() => {
    const fetchReadings = async () => {
      try {
        setLoading(true);
        const data = await getReadingsBySurvey(surveyId);
        if (data) {
          setExistingReading(data);
          setReadings({
            portForward: data.portForward,
            starboardForward: data.starboardForward,
            portMidship: data.portMidship,
            starboardMidship: data.starboardMidship,
            portAft: data.portAft,
            starboardAft: data.starboardAft,
            readingTime: data.readingTime
          });
        }
        setLoading(false);
      } catch (err) {
        setError('Erreur lors du chargement des lectures');
        setLoading(false);
      }
    };
    
    fetchReadings();
  }, [surveyId]);

  const handleChange = (e) => {
    setReadings({ ...readings, [e.target.name]: e.target.value });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      setLoading(true);
      const readingData = {
        ...readings,
        draftSurveyId: surveyId,
        portForward: parseFloat(readings.portForward),
        starboardForward: parseFloat(readings.starboardForward),
        portMidship: parseFloat(readings.portMidship),
        starboardMidship: parseFloat(readings.starboardMidship),
        portAft: parseFloat(readings.portAft),
        starboardAft: parseFloat(readings.starboardAft)
      };

      if (existingReading) {
        await updateReading(surveyId, readingData);
      } else {
        await createReading(readingData);
      }
      
      setError(null);
      setLoading(false);
    } catch (err) {
      setError('Erreur lors de la sauvegarde des lectures');
      setLoading(false);
    }
  };

  const handleMarkComplete = () => {
    setCompleted(true);
    onComplete();
  };

  if (loading) return <CircularProgress />;

  return (
    <Paper sx={{ p: 3, mb: 3 }}>
      <Typography variant="h6" gutterBottom>Lectures des Marques de Tirant d'Eau</Typography>
      
      {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>}

      <Box component="form" onSubmit={handleSubmit}>
        <Grid container spacing={2}>
          <Grid item xs={12} md={6}>
            <TextField
              name="portForward"
              label="Port Forward"
              type="number"
              value={readings.portForward}
              onChange={handleChange}
              fullWidth
              margin="normal"
              required
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField
              name="starboardForward"
              label="Starboard Forward"
              type="number"
              value={readings.starboardForward}
              onChange={handleChange}
              fullWidth
              margin="normal"
              required
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField
              name="portMidship"
              label="Port Midship"
              type="number"
              value={readings.portMidship}
              onChange={handleChange}
              fullWidth
              margin="normal"
              required
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField
              name="starboardMidship"
              label="Starboard Midship"
              type="number"
              value={readings.starboardMidship}
              onChange={handleChange}
              fullWidth
              margin="normal"
              required
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField
              name="portAft"
              label="Port Aft"
              type="number"
              value={readings.portAft}
              onChange={handleChange}
              fullWidth
              margin="normal"
              required
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField
              name="starboardAft"
              label="Starboard Aft"
              type="number"
              value={readings.starboardAft}
              onChange={handleChange}
              fullWidth
              margin="normal"
              required
            />
          </Grid>
          <Grid item xs={12}>
            <TextField
              name="readingTime"
              label="Date et Heure de Lecture"
              type="datetime-local"
              value={readings.readingTime}
              onChange={handleChange}
              fullWidth
              margin="normal"
              required
              InputLabelProps={{
                shrink: true,
              }}
            />
          </Grid>
        </Grid>
        
        <Box sx={{ mt: 2, display: 'flex', justifyContent: 'space-between' }}>
          <Button 
            type="submit" 
            variant="contained"
          >
            {existingReading ? 'Mettre à jour' : 'Enregistrer'}
          </Button>

          {!completed && (
            <Button
              variant="contained"
              color="success"
              startIcon={<CheckCircleIcon />}
              onClick={handleMarkComplete}
            >
              Marquer comme Terminé
            </Button>
          )}
        </Box>
      </Box>

      {completed && (
        <Alert severity="success" sx={{ mt: 2 }}>
          Cette sous-tâche a été marquée comme terminée
        </Alert>
      )}
    </Paper>
  );
};

export default DraughtMarksForm;