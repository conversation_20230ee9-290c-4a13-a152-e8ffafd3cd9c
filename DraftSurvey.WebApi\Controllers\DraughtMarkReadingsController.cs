﻿using AutoMapper;
using DraftSurvey.Application.DTOs;
using DraftSurvey.Domain.Entities;
using DraftSurvey.Domain.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading.Tasks;

namespace DraftSurvey.WebApi.Controllers
{
    [Authorize]
    [ApiController]
    [Route("api/[controller]")]
    public class DraughtMarkReadingsController : ControllerBase
    {
        private readonly IDraughtMarkReadingRepository _repository;
        private readonly IMapper _mapper;

        public DraughtMarkReadingsController(IDraughtMarkReadingRepository repository, IMapper mapper)
        {
            _repository = repository;
            _mapper = mapper;
        }

        [HttpGet("by-survey/{surveyId}")]
        public async Task<ActionResult<DraughtMarkReadingDto>> GetBySurvey(Guid surveyId)
        {
            var reading = await _repository.GetBySurveyIdAsync(surveyId);
            if (reading == null) return NotFound();
            return Ok(_mapper.Map<DraughtMarkReadingDto>(reading));
        }

        [HttpPost]
        [Authorize(Roles = "Admin,TeamLead")]
        public async Task<ActionResult<DraughtMarkReadingDto>> Create([FromBody] DraughtMarkReadingDto dto)
        {
            var reading = _mapper.Map<DraughtMarkReading>(dto);
            await _repository.AddAsync(reading);
            await _repository.SaveChangesAsync();
            return CreatedAtAction(nameof(GetBySurvey), new { surveyId = reading.DraftSurveyId }, _mapper.Map<DraughtMarkReadingDto>(reading));
        }

        [HttpPut("{surveyId}")]
        [Authorize(Roles = "Admin,TeamLead")]
        public async Task<IActionResult> Update(Guid surveyId, [FromBody] DraughtMarkReadingDto dto)
        {
            var reading = await _repository.GetBySurveyIdAsync(surveyId);
            if (reading == null) return NotFound();
            _mapper.Map(dto, reading);
            await _repository.UpdateAsync(reading);
            return NoContent();
        }
    }
}