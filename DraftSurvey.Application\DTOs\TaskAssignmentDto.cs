﻿using System;
using System.ComponentModel.DataAnnotations;

namespace DraftSurvey.Application.DTOs
{
    public class TaskAssignmentDto
    {
        public Guid Id { get; set; }
        public Guid DraftSurveyId { get; set; }
        public Guid UserId { get; set; }
        public string UserName { get; set; }
        public string SurveyNumber { get; set; }

        [Required]
        public string TaskType { get; set; } // Utilisation directe du type string

        public string TaskTypeDisplay => GetTaskTypeDisplayName(TaskType);

        public DateTime AssignmentDate { get; set; }
        public DateTime? CompletionDate { get; set; }
        public string Status { get; set; }
        public string Notes { get; set; }
        public bool IsCompleted { get; set; }

        public string TaskData { get; set; }

        private string GetTaskTypeDisplayName(string type)
        {
            return type switch
            {
                "Ballast" => "Ballast",
                "DraughtMarkReading" => "Draught Marks Reading",
                "DraughtMarksCorrection" => "Draught Marks Correction",
                "DraughtSurveyGlobalCalculation" => "Global Calculation",
                "FreshWaterTank" => "Fresh Water Tank",
                "GlobalCalculation" => "Global Calculation",
                "Inspection" => "Inspection",
                "LiquidStatement" => "Liquid Statement",
                "SeaWaterDensityStatement" => "Sea Water Density",
                _ => type
            };
        }
    }

    public class TaskSubmissionDto
    {
        [MaxLength(1000)]
        public string Notes { get; set; }

        public List<string> Attachments { get; set; } = new List<string>();
    }

    public class TaskRejectionDto
    {
        [Required]
        [MaxLength(1000)]
        public string Reason { get; set; }

        public bool RequiresResubmission { get; set; } = true;
    }

    public class TaskAssignmentWithRoleDto : TaskAssignmentDto
    {
        public string AssignedRole { get; set; }
        public bool IsPriorityTask { get; set; }
    }
}