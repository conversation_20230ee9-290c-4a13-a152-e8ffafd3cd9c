using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using DraftSurvey.Infrastructure.Data;

namespace DraftSurvey.WebApi.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class SeedController : ControllerBase
    {
        private readonly DraftSurveyDbContext _context;

        public SeedController(DraftSurveyDbContext context)
        {
            _context = context;
        }

        [HttpPost("database")]
        public async Task<IActionResult> SeedDatabase()
        {
            try
            {
                var seeder = new DatabaseSeeder(_context);
                await seeder.SeedAsync();
                
                return Ok(new { message = "Database seeded successfully with 10 elements for each table!" });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = "Error seeding database", error = ex.Message });
            }
        }

        [HttpGet("status")]
        public async Task<IActionResult> GetSeedStatus()
        {
            try
            {
                var counts = new
                {
                    Roles = await _context.Roles.CountAsync(),
                    Ports = await _context.Ports.CountAsync(),
                    Users = await _context.Users.CountAsync(),
                    Vessels = await _context.Vessels.CountAsync(),
                    Escales = await _context.Escales.CountAsync(),
                    DraftSurveys = await _context.DraftSurveys.CountAsync(),
                    DraughtMarkReadings = await _context.DraughtMarkReadings.CountAsync(),
                    Ballasts = await _context.Ballasts.CountAsync(),
                    FreshWaterTanks = await _context.FreshWaterTanks.CountAsync(),
                    Liquids = await _context.Liquids.CountAsync(),
                    LiquidStatements = await _context.LiquidStatements.CountAsync(),
                    SeaWaterDensityStatements = await _context.SeaWaterDensityStatements.CountAsync(),
                    Inspections = await _context.Inspections.CountAsync(),
                    TaskAssignments = await _context.TaskAssignments.CountAsync()
                };

                return Ok(counts);
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = "Error getting seed status", error = ex.Message });
            }
        }
    }
}
