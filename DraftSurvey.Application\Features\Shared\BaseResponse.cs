namespace DraftSurvey.Application.Features.Shared
{
    public class BaseResponse<T>
    {
        public bool Success { get; set; }
        public T Data { get; set; }
        public string ErrorMessage { get; set; }
        public string ErrorCode { get; set; }

        public static BaseResponse<T> Ok(T data) => new() { Success = true, Data = data };
        public static BaseResponse<T> Fail(string error, string code = "GENERIC_ERROR")
            => new() { Success = false, ErrorMessage = error, ErrorCode = code };
    }
}