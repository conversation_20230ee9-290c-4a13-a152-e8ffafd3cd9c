import React, { useState, useEffect } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  TextField,
  Button,
  MenuItem,
  Box,
  FormControl,
  InputLabel,
  Select,
  Switch,
  FormControlLabel,
  CircularProgress,
  Alert,
  useTheme // Import useTheme for consistent styling
} from "@mui/material";
import { updateUser } from "../../api/usersApi";
import { getRoles } from "../../api/rolesApi";
import { getPorts } from "../../api/portsApi";

const UserEditForm = ({ user, onSave, onClose }) => {
  const theme = useTheme(); // Access the theme

  const [formData, setFormData] = useState({
    username: user?.username || '',
    email: user?.email || '',
    fullName: user?.fullName || '',
    roleId: user?.role?.id || user?.roleId || '',
    portId: user?.port?.id || user?.portId || '',
    isActive: user?.isActive !== false // Ensure default is true
  });

  const [roles, setRoles] = useState([]);
  const [ports, setPorts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [rolesData, portsData] = await Promise.all([
          getRoles(),
          getPorts()
        ]);
        setRoles(rolesData);
        setPorts(portsData);
      } catch (err) {
        console.error("Erreur lors du chargement des données:", err);
        setError("Failed to load necessary data."); // More user-friendly message
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, []);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleToggleActive = (e) => {
    setFormData(prev => ({
      ...prev,
      isActive: e.target.checked
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setSubmitting(true);
    setError(null);

    try {
      const updatedData = {
        email: formData.email,
        fullName: formData.fullName,
        roleId: formData.roleId,
        portId: formData.portId || null, // Ensure null if no port selected
        isActive: formData.isActive
      };

      await updateUser(user.id, updatedData);
      onSave({ ...user, ...updatedData }); // Pass back updated user data
      onClose();
    } catch (err) {
      console.error("Erreur lors de la mise à jour:", err);
      // Use err.response?.data?.message for API error messages
      setError(err.response?.data?.message || "Error updating user.");
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <Dialog open onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle sx={{
        bgcolor: "black", // Use theme primary color
        color: 'white',
        textAlign: 'center',
        fontWeight: 'bold',
        py: 2,
        borderBottom: `1px solid ${theme.palette.divider}`, // Subtle border
        boxShadow: theme.shadows[1] // Light shadow for depth
      }}>
        Modifier l'utilisateur
      </DialogTitle>

      <DialogContent sx={{ p: 3, bgcolor: theme.palette.background.paper }}> {/* Use theme background.paper */}
        {loading ? (
          <Box display="flex" justifyContent="center" py={4}>
            <CircularProgress color="primary" /> {/* Themed spinner */}
          </Box>
        ) : (
          <form onSubmit={handleSubmit}>
            {error && (
              <Alert severity="error" sx={{ mb: 2 }}>
                {error}
              </Alert>
            )}

            <TextField
              label="Nom d'utilisateur"
              fullWidth
              name="username"
              value={formData.username}
              margin="normal"
              disabled // Username is usually not editable
              variant="outlined" // Explicitly use outlined variant
            />

            <TextField
              label="Email"
              fullWidth
              name="email"
              type="email"
              value={formData.email}
              onChange={handleChange}
              margin="normal"
              required
              disabled={submitting}
              variant="outlined"
            />

            <TextField
              label="Nom complet"
              fullWidth
              name="fullName"
              value={formData.fullName}
              onChange={handleChange}
              margin="normal"
              required
              disabled={submitting}
              variant="outlined"
            />

            <FormControl fullWidth margin="normal" required disabled={submitting}>
              <InputLabel>Rôle</InputLabel>
              <Select
                name="roleId"
                value={formData.roleId}
                onChange={handleChange}
                label="Rôle"
                variant="outlined"
              >
                {roles.map(role => (
                  <MenuItem key={role.id} value={role.id}>
                    {role.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <FormControl fullWidth margin="normal" disabled={submitting}>
              <InputLabel>Port</InputLabel>
              <Select
                name="portId"
                value={formData.portId || ''}
                onChange={handleChange}
                label="Port"
                variant="outlined"
              >
                <MenuItem value="">Aucun port</MenuItem>
                {ports.map(port => (
                  <MenuItem key={port.id} value={port.id}>
                    {port.name} ({port.code})
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <FormControlLabel
              control={
                <Switch
                  checked={formData.isActive}
                  onChange={handleToggleActive}
                  name="isActive"
                  color="primary"
                  disabled={submitting}
                />
              }
              label={formData.isActive ? "Actif" : "Inactif"}
              sx={{ mt: 2, mb: 2 }}
            />

            <Box display="flex" justifyContent="flex-end" gap={2} mt={3}>
              <Button
                onClick={onClose}
                variant="outlined"
                color="error"
                disabled={submitting}
                sx={{
                    '&:hover': {
                        borderColor: theme.palette.error.dark,
                        backgroundColor: theme.palette.error.light + '10' // Subtle hover effect
                    }
                }}
              >
                Annuler
              </Button>
              <Button
                type="submit"
                variant="contained"
                color="black"
                disabled={submitting}
                startIcon={submitting ? <CircularProgress size={20} color="inherit" /> : null} // Color inherit to match button
                sx={{
                    '&:hover': {
                        backgroundColor: theme.palette.primary.dark,
                    }
                }}
              >
                {submitting ? "Enregistrement..." : "Enregistrer"}
              </Button>
            </Box>
          </form>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default UserEditForm;