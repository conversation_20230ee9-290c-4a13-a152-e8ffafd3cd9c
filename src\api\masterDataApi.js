// src/api/masterDataApi.js
import api from './axiosConfig';

export const getVessels = async () => {
  try {
    const response = await api.get('/vessels');
    return response.data;
  } catch (error) {
    console.error('Error fetching vessels:', error);
    throw error;
  }
};

export const getEscales = async () => {
  try {
    const response = await api.get('/escales');
    return response.data;
  } catch (error) {
    console.error('Error fetching escales:', error);
    throw error;
  }
};