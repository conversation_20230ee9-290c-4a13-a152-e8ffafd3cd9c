import React, { useState, useEffect } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  IconButton,
  MenuItem,
  Typography,
  Grid,
  CircularProgress,
  Box,
  Alert
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import { getVessels, getEscales } from "../../api/masterDataApi"; // Chemin corrigé

const DraftEditPopup = ({ open, onClose, onSave, draft }) => {
  const [formData, setFormData] = useState({
    vesselId: '',
    escaleId: '',
    status: 'Initial'
  });
  const [vessels, setVessels] = useState([]);
  const [escales, setEscales] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Initialiser le formulaire avec les données du draft (si modification)
  useEffect(() => {
    if (draft) {
      setFormData({
        vesselId: draft.vesselId || '',
        escaleId: draft.escaleId || '',
        status: draft.status || 'Initial'
      });
    } else {
      setFormData({
        vesselId: '',
        escaleId: '',
        status: 'Initial'
      });
    }
  }, [draft]);

  // Charger les données quand le popup s'ouvre
  useEffect(() => {
    if (open) {
      fetchData();
    }
  }, [open]);

  const fetchData = async () => {
    setLoading(true);
    setError(null);
    try {
      const [vesselsData, escalesData] = await Promise.all([
        getVessels(),
        getEscales()
      ]);
      setVessels(vesselsData);
      setEscales(escalesData);
    } catch (err) {
      console.error("Error loading data:", err);
      setError("Failed to load required data. Please try again later.");
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSave = () => {
    if (!formData.vesselId || !formData.escaleId) {
      setError("Please select both a vessel and an escale");
      return;
    }
    onSave(formData);
    onClose();
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle sx={{ 
        display: "flex", 
        justifyContent: "space-between", 
        alignItems: "center",
        bgcolor: 'primary.main',
        color: 'white'
      }}>
        <Typography variant="h6" fontWeight="bold">
          {draft ? "Edit Draft Survey" : "Create New Draft Survey"}
        </Typography>
        <IconButton onClick={onClose} sx={{ color: 'white' }}>
          <CloseIcon />
        </IconButton>
      </DialogTitle>
      
      <DialogContent dividers sx={{ pt: 3 }}>
        {loading ? (
          <Box display="flex" justifyContent="center" py={4}>
            <CircularProgress />
          </Box>
        ) : error ? (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        ) : (
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <TextField
                select
                fullWidth
                label="Vessel"
                name="vesselId"
                value={formData.vesselId}
                onChange={handleChange}
                required
                disabled={loading}
                variant="outlined"
                size="small"
              >
                <MenuItem value="" disabled>
                  Select a vessel
                </MenuItem>
                {vessels.map((vessel) => (
                  <MenuItem key={vessel.id} value={vessel.id}>
                    {vessel.name}
                  </MenuItem>
                ))}
              </TextField>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <TextField
                select
                fullWidth
                label="Escale"
                name="escaleId"
                value={formData.escaleId}
                onChange={handleChange}
                required
                disabled={loading}
                variant="outlined"
                size="small"
              >
                <MenuItem value="" disabled>
                  Select an escale
                </MenuItem>
                {escales.map((escale) => (
                  <MenuItem key={escale.id} value={escale.id}>
                    {escale.reference} - {escale.port?.name}
                  </MenuItem>
                ))}
              </TextField>
            </Grid>
            
            {draft && (
              <Grid item xs={12} md={6}>
                <TextField
                  select
                  fullWidth
                  label="Status"
                  name="status"
                  value={formData.status}
                  onChange={handleChange}
                  disabled={loading}
                  variant="outlined"
                  size="small"
                >
                  {['Initial', 'InProgress', 'Finalized'].map((status) => (
                    <MenuItem key={status} value={status}>
                      {status}
                    </MenuItem>
                  ))}
                </TextField>
              </Grid>
            )}
          </Grid>
        )}
      </DialogContent>
      
      <DialogActions sx={{ p: 2 }}>
        <Button 
          onClick={onClose} 
          color="secondary"
          variant="outlined"
          disabled={loading}
        >
          Cancel
        </Button>
        <Button 
          onClick={handleSave} 
          color="primary"
          variant="contained"
          disabled={loading || !formData.vesselId || !formData.escaleId}
        >
          {draft ? "Update" : "Create"}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default DraftEditPopup;