import React, { useState, useEffect } from 'react';
import EntityPageLayout from '../../components/Shared/EntityPageLayout';
import { Package, TrendingUp, Clock, CheckCircle, AlertTriangle, Truck } from 'lucide-react';

const CargoLoadingPage = () => {
  const [cargoLoading, setCargoLoading] = useState([]);
  const [loading, setLoading] = useState(true);

  // Mock data - replace with actual API call
  useEffect(() => {
    const mockCargoLoading = [
      {
        id: 1,
        loadingId: "CL-2024-001",
        vesselName: "MSC OSCAR",
        cargoType: "Containers",
        holdNumber: "Hold #1",
        plannedQuantity: 2500.0,
        loadedQuantity: 2485.5,
        unit: "TEU",
        loadingRate: 125.5,
        status: "Completed",
        startTime: "2024-01-15T08:00:00Z",
        endTime: "2024-01-15T16:30:00Z",
        supervisor: "<PERSON>",
        stevedore: "Rotterdam Stevedoring Co.",
        notes: "Loading completed ahead of schedule",
        weatherConditions: "Clear",
        equipment: "Ship-to-Shore Cranes"
      },
      {
        id: 2,
        loadingId: "CL-2024-002",
        vesselName: "EVER GIVEN",
        cargoType: "Bulk Grain",
        holdNumber: "Hold #2",
        plannedQuantity: 15000.0,
        loadedQuantity: 8750.0,
        unit: "MT",
        loadingRate: 450.0,
        status: "In Progress",
        startTime: "2024-01-15T06:00:00Z",
        endTime: null,
        supervisor: "Sarah Mitchell",
        stevedore: "Global Cargo Services",
        notes: "Loading proceeding as planned",
        weatherConditions: "Partly Cloudy",
        equipment: "Pneumatic Conveyors"
      },
      {
        id: 3,
        loadingId: "CL-2024-003",
        vesselName: "MAERSK MADRID",
        cargoType: "Steel Coils",
        holdNumber: "Hold #3",
        plannedQuantity: 5000.0,
        loadedQuantity: 0.0,
        unit: "MT",
        loadingRate: 0.0,
        status: "Scheduled",
        startTime: "2024-01-16T10:00:00Z",
        endTime: null,
        supervisor: "Michael Chen",
        stevedore: "Heavy Lift Specialists",
        notes: "Awaiting cargo arrival",
        weatherConditions: "Forecast: Sunny",
        equipment: "Heavy Lift Cranes"
      },
      {
        id: 4,
        loadingId: "CL-2024-004",
        vesselName: "MSC OSCAR",
        cargoType: "Refrigerated Containers",
        holdNumber: "Hold #4",
        plannedQuantity: 800.0,
        loadedQuantity: 650.0,
        unit: "TEU",
        loadingRate: 85.0,
        status: "Delayed",
        startTime: "2024-01-14T14:00:00Z",
        endTime: null,
        supervisor: "Emma Rodriguez",
        stevedore: "Cold Chain Logistics",
        notes: "Reefer connection issues causing delays",
        weatherConditions: "Rain",
        equipment: "Reefer Cranes"
      },
      {
        id: 5,
        loadingId: "CL-2024-005",
        vesselName: "EVER GIVEN",
        cargoType: "Liquid Chemicals",
        holdNumber: "Tank #1",
        plannedQuantity: 3500.0,
        loadedQuantity: 3500.0,
        unit: "MT",
        loadingRate: 175.0,
        status: "Completed",
        startTime: "2024-01-13T09:00:00Z",
        endTime: "2024-01-13T17:00:00Z",
        supervisor: "David Kim",
        stevedore: "Chemical Handling Ltd",
        notes: "Hazardous cargo loaded with full safety protocols",
        weatherConditions: "Clear",
        equipment: "Shore Pipelines"
      }
    ];

    setTimeout(() => {
      setCargoLoading(mockCargoLoading);
      setLoading(false);
    }, 1000);
  }, []);

  const columns = [
    {
      header: 'Loading Details',
      key: 'loadingId',
      render: (loading) => (
        <div className="flex items-center">
          <div className="flex-shrink-0 h-10 w-10">
            <div className="h-10 w-10 rounded-full bg-orange-100 flex items-center justify-center">
              <Package className="h-5 w-5 text-orange-600" />
            </div>
          </div>
          <div className="ml-4">
            <div className="text-sm font-medium text-gray-900">{loading.loadingId}</div>
            <div className="text-sm text-gray-500">{loading.vesselName}</div>
          </div>
        </div>
      )
    },
    {
      header: 'Cargo & Hold',
      key: 'cargo',
      render: (loading) => (
        <div>
          <div className="text-sm text-gray-900">{loading.cargoType}</div>
          <div className="text-sm text-gray-500">{loading.holdNumber}</div>
        </div>
      )
    },
    {
      header: 'Quantity Progress',
      key: 'quantity',
      render: (loading) => {
        const progressPercentage = (loading.loadedQuantity / loading.plannedQuantity) * 100;
        const progressColor = progressPercentage >= 100 ? 'bg-green-600' : 
                             progressPercentage >= 75 ? 'bg-blue-600' : 
                             progressPercentage >= 50 ? 'bg-yellow-600' : 'bg-red-600';
        
        return (
          <div>
            <div className="text-sm text-gray-900">
              {loading.loadedQuantity.toLocaleString()} / {loading.plannedQuantity.toLocaleString()} {loading.unit}
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2 mt-1">
              <div 
                className={`${progressColor} h-2 rounded-full transition-all duration-300`}
                style={{ width: `${Math.min(progressPercentage, 100)}%` }}
              ></div>
            </div>
            <div className="text-xs text-gray-500 mt-1">{progressPercentage.toFixed(1)}% complete</div>
          </div>
        );
      }
    },
    {
      header: 'Loading Rate',
      key: 'loadingRate',
      render: (loading) => (
        <div className="text-sm">
          <div className="text-gray-900 flex items-center">
            <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
            {loading.loadingRate.toFixed(1)} {loading.unit}/hr
          </div>
        </div>
      )
    },
    {
      header: 'Status',
      key: 'status',
      render: (loading) => {
        const statusConfig = {
          'Completed': { color: 'bg-green-100 text-green-800', icon: CheckCircle },
          'In Progress': { color: 'bg-blue-100 text-blue-800', icon: Clock },
          'Scheduled': { color: 'bg-yellow-100 text-yellow-800', icon: Clock },
          'Delayed': { color: 'bg-red-100 text-red-800', icon: AlertTriangle },
          'On Hold': { color: 'bg-gray-100 text-gray-800', icon: AlertTriangle }
        };
        const config = statusConfig[loading.status] || statusConfig['Scheduled'];
        const IconComponent = config.icon;
        
        return (
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
            <IconComponent className="w-3 h-3 mr-1" />
            {loading.status}
          </span>
        );
      }
    },
    {
      header: 'Supervisor',
      key: 'supervisor',
      render: (loading) => (
        <div className="text-sm text-gray-900">{loading.supervisor}</div>
      )
    },
    {
      header: 'Stevedore',
      key: 'stevedore',
      render: (loading) => (
        <div className="text-sm">
          <div className="text-gray-900 flex items-center">
            <Truck className="h-4 w-4 text-gray-400 mr-1" />
            {loading.stevedore}
          </div>
        </div>
      )
    },
    {
      header: 'Equipment',
      key: 'equipment',
      render: (loading) => (
        <div className="text-sm text-gray-500">{loading.equipment}</div>
      )
    },
    {
      header: 'Duration',
      key: 'duration',
      render: (loading) => {
        const startTime = new Date(loading.startTime);
        const endTime = loading.endTime ? new Date(loading.endTime) : new Date();
        const durationHours = (endTime - startTime) / (1000 * 60 * 60);
        
        return (
          <div className="text-sm">
            <div className="text-gray-900">
              {durationHours.toFixed(1)} hours
            </div>
            <div className="text-gray-500">
              {startTime.toLocaleDateString()}
            </div>
          </div>
        );
      }
    }
  ];

  const handleAdd = () => {
    console.log('Add new cargo loading operation');
  };

  const handleEdit = (loading) => {
    console.log('Edit cargo loading:', loading);
  };

  const handleDelete = (loading) => {
    console.log('Delete cargo loading:', loading);
  };

  const handleView = (loading) => {
    console.log('View cargo loading:', loading);
  };

  const completedOperations = cargoLoading.filter(c => c.status === 'Completed').length;
  const inProgressOperations = cargoLoading.filter(c => c.status === 'In Progress').length;
  const delayedOperations = cargoLoading.filter(c => c.status === 'Delayed').length;
  const totalPlannedQuantity = cargoLoading.reduce((sum, c) => sum + c.plannedQuantity, 0);
  const totalLoadedQuantity = cargoLoading.reduce((sum, c) => sum + c.loadedQuantity, 0);
  const overallProgress = (totalLoadedQuantity / totalPlannedQuantity) * 100;

  return (
    <EntityPageLayout
      title="Cargo Loading Management"
      description="Monitor and manage cargo loading operations, track progress, and ensure efficient vessel turnaround times."
      data={cargoLoading}
      columns={columns}
      onAdd={handleAdd}
      onEdit={handleEdit}
      onDelete={handleDelete}
      onView={handleView}
      loading={loading}
      searchPlaceholder="Search cargo loading by vessel, cargo type, supervisor..."
    >
      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Package className="h-8 w-8 text-orange-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Total Operations</p>
              <p className="text-2xl font-semibold text-gray-900">{cargoLoading.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Completed</p>
              <p className="text-2xl font-semibold text-gray-900">{completedOperations}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Clock className="h-8 w-8 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">In Progress</p>
              <p className="text-2xl font-semibold text-gray-900">{inProgressOperations}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <TrendingUp className="h-8 w-8 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Overall Progress</p>
              <p className="text-2xl font-semibold text-gray-900">{overallProgress.toFixed(1)}%</p>
            </div>
          </div>
        </div>
      </div>
    </EntityPageLayout>
  );
};

export default CargoLoadingPage;
