import React, { useState, useEffect } from 'react';
import { 
  Box, 
  TextField, 
  Button, 
  Typography, 
  Paper,
  Grid,
  MenuItem,
  Alert,
  CircularProgress,
  Chip
} from '@mui/material';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import { getDensityBySurvey, createDensityStatement, updateDensityStatement } from '../api/seaWaterDensityApi';

const SeaWaterDensityForm = ({ surveyId, onComplete }) => {
  const [densityData, setDensityData] = useState({
    densityValue: '',
    measurementDate: new Date().toISOString().slice(0, 10),
    measurementTime: '12:00',
    temperature: '',
    measurementMethod: 'Hydrometer',
    location: 'Alongside',
    notes: ''
  });
  const [existingId, setExistingId] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [completed, setCompleted] = useState(false);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const data = await getDensityBySurvey(surveyId);
        if (data) {
          setExistingId(data.id);
          setDensityData({
            densityValue: data.densityValue,
            measurementDate: data.measurementDate.slice(0, 10),
            measurementTime: data.measurementTime,
            temperature: data.temperature,
            measurementMethod: data.measurementMethod,
            location: data.location,
            notes: data.notes
          });
        }
        setLoading(false);
      } catch (err) {
        setError('Erreur lors du chargement des données de densité');
        setLoading(false);
      }
    };
    
    fetchData();
  }, [surveyId]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      setLoading(true);
      const data = {
        ...densityData,
        draftSurveyId: surveyId,
        densityValue: parseFloat(densityData.densityValue),
        temperature: parseFloat(densityData.temperature)
      };

      if (existingId) {
        await updateDensityStatement(existingId, data);
      } else {
        await createDensityStatement(data);
      }
      setError(null);
      setLoading(false);
    } catch (err) {
      setError('Erreur lors de la sauvegarde des données');
      setLoading(false);
    }
  };

  const handleMarkComplete = () => {
    setCompleted(true);
    if (onComplete) onComplete();
  };

  if (loading) return <CircularProgress />;

  return (
    <Paper sx={{ p: 3, mb: 3 }}>
      <Typography variant="h6" gutterBottom>Densité de l'eau de mer</Typography>
      
      {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>}

      <Box component="form" onSubmit={handleSubmit}>
        <Grid container spacing={2}>
          <Grid item xs={12} md={4}>
            <TextField
              name="densityValue"
              label="Densité (t/m³)"
              type="number"
              value={densityData.densityValue}
              onChange={(e) => setDensityData({...densityData, densityValue: e.target.value})}
              fullWidth
              required
              inputProps={{ step: "0.001" }}
            />
          </Grid>
          <Grid item xs={12} md={4}>
            <TextField
              name="temperature"
              label="Température (°C)"
              type="number"
              value={densityData.temperature}
              onChange={(e) => setDensityData({...densityData, temperature: e.target.value})}
              fullWidth
              required
              inputProps={{ step: "0.1" }}
            />
          </Grid>
          <Grid item xs={12} md={4}>
            <TextField
              name="measurementMethod"
              label="Méthode"
              select
              value={densityData.measurementMethod}
              onChange={(e) => setDensityData({...densityData, measurementMethod: e.target.value})}
              fullWidth
              required
            >
              <MenuItem value="Hydrometer">Hydromètre</MenuItem>
              <MenuItem value="Refractometer">Réfractomètre</MenuItem>
              <MenuItem value="Digital">Appareil digital</MenuItem>
            </TextField>
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField
              name="measurementDate"
              label="Date de mesure"
              type="date"
              value={densityData.measurementDate}
              onChange={(e) => setDensityData({...densityData, measurementDate: e.target.value})}
              fullWidth
              required
              InputLabelProps={{
                shrink: true,
              }}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField
              name="measurementTime"
              label="Heure de mesure"
              type="time"
              value={densityData.measurementTime}
              onChange={(e) => setDensityData({...densityData, measurementTime: e.target.value})}
              fullWidth
              required
              InputLabelProps={{
                shrink: true,
              }}
            />
          </Grid>
          <Grid item xs={12}>
            <TextField
              name="location"
              label="Emplacement"
              value={densityData.location}
              onChange={(e) => setDensityData({...densityData, location: e.target.value})}
              fullWidth
              required
            />
          </Grid>
          <Grid item xs={12}>
            <TextField
              name="notes"
              label="Notes"
              multiline
              rows={3}
              value={densityData.notes}
              onChange={(e) => setDensityData({...densityData, notes: e.target.value})}
              fullWidth
            />
          </Grid>
        </Grid>
        
        <Box sx={{ mt: 2, display: 'flex', justifyContent: 'space-between' }}>
          <Button 
            type="submit" 
            variant="contained"
            disabled={loading}
          >
            {loading ? <CircularProgress size={24} /> : existingId ? 'Mettre à jour' : 'Enregistrer'}
          </Button>

          {!completed && (
            <Button
              variant="contained"
              color="success"
              startIcon={<CheckCircleIcon />}
              onClick={handleMarkComplete}
              disabled={!densityData.densityValue || !densityData.temperature}
            >
              Marquer comme Terminé
            </Button>
          )}
        </Box>
      </Box>

      {completed && (
        <Alert severity="success" sx={{ mt: 2 }}>
          Cette sous-tâche a été marquée comme terminée
        </Alert>
      )}
    </Paper>
  );
};

export default SeaWaterDensityForm;