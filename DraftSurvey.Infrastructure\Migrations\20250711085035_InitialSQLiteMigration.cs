﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace DraftSurvey.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class InitialSQLiteMigration : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "Liquids",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "TEXT", nullable: false),
                    Name = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    Type = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
                    StandardDensity = table.Column<double>(type: "float", nullable: false),
                    Unit = table.Column<string>(type: "TEXT", maxLength: 20, nullable: false),
                    Description = table.Column<string>(type: "TEXT", maxLength: 500, nullable: false),
                    IsHazardous = table.Column<bool>(type: "INTEGER", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Liquids", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Ports",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "TEXT", nullable: false),
                    Name = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    Code = table.Column<string>(type: "TEXT", maxLength: 10, nullable: false),
                    Country = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
                    StandardWaterDensity = table.Column<double>(type: "decimal(18,6)", nullable: false, defaultValue: 1.0249999999999999)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Ports", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Roles",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "TEXT", nullable: false),
                    Name = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
                    Description = table.Column<string>(type: "TEXT", maxLength: 255, nullable: false),
                    CanManageUsers = table.Column<bool>(type: "INTEGER", nullable: false, defaultValue: false),
                    CanManageAllPorts = table.Column<bool>(type: "INTEGER", nullable: false, defaultValue: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Roles", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Vessels",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "TEXT", nullable: false),
                    Name = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    IMONumber = table.Column<string>(type: "TEXT", maxLength: 15, nullable: false),
                    CallSign = table.Column<string>(type: "TEXT", nullable: false),
                    Flag = table.Column<string>(type: "TEXT", nullable: false),
                    Type = table.Column<string>(type: "TEXT", nullable: false),
                    LengthOverall = table.Column<double>(type: "REAL", nullable: false),
                    LengthBetweenPerpendiculars = table.Column<double>(type: "decimal(18,2)", nullable: false),
                    BreadthMoulded = table.Column<double>(type: "REAL", nullable: false),
                    DepthMoulded = table.Column<double>(type: "REAL", nullable: false),
                    LightDisplacement = table.Column<double>(type: "REAL", nullable: false),
                    SummerDeadweight = table.Column<double>(type: "decimal(18,2)", nullable: false),
                    TPC = table.Column<double>(type: "REAL", nullable: false),
                    LCF = table.Column<double>(type: "decimal(18,2)", nullable: false),
                    LCG = table.Column<double>(type: "REAL", nullable: false),
                    MTC = table.Column<double>(type: "REAL", nullable: false),
                    DisplacementAtDesignDraft = table.Column<double>(type: "REAL", nullable: false),
                    DisplacementFactor = table.Column<double>(type: "REAL", nullable: false),
                    Notes = table.Column<string>(type: "TEXT", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Vessels", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Users",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "TEXT", nullable: false),
                    Username = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
                    PasswordHash = table.Column<string>(type: "TEXT", maxLength: 255, nullable: false),
                    Email = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    FullName = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    IsActive = table.Column<bool>(type: "INTEGER", nullable: false, defaultValue: true),
                    RoleId = table.Column<Guid>(type: "TEXT", nullable: false),
                    PortId = table.Column<Guid>(type: "TEXT", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Users", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Users_Ports_PortId",
                        column: x => x.PortId,
                        principalTable: "Ports",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Users_Roles_RoleId",
                        column: x => x.RoleId,
                        principalTable: "Roles",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "Escales",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "TEXT", nullable: false),
                    Reference = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
                    ArrivalDate = table.Column<DateTime>(type: "TEXT", nullable: false),
                    DepartureDate = table.Column<DateTime>(type: "TEXT", nullable: true),
                    VesselId = table.Column<Guid>(type: "TEXT", nullable: false),
                    PortId = table.Column<Guid>(type: "TEXT", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Escales", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Escales_Ports_PortId",
                        column: x => x.PortId,
                        principalTable: "Ports",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Escales_Vessels_VesselId",
                        column: x => x.VesselId,
                        principalTable: "Vessels",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "Documents",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "TEXT", nullable: false),
                    FileName = table.Column<string>(type: "TEXT", maxLength: 255, nullable: false),
                    FilePath = table.Column<string>(type: "TEXT", maxLength: 500, nullable: false),
                    ContentType = table.Column<string>(type: "TEXT", nullable: false),
                    FileSize = table.Column<long>(type: "INTEGER", nullable: false),
                    UploadDate = table.Column<DateTime>(type: "TEXT", nullable: false),
                    UploadedByUserId = table.Column<Guid>(type: "TEXT", nullable: false),
                    DocumentType = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
                    Description = table.Column<string>(type: "TEXT", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Documents", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Documents_Users_UploadedByUserId",
                        column: x => x.UploadedByUserId,
                        principalTable: "Users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "AvisChargements",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "TEXT", nullable: false),
                    EscaleId = table.Column<Guid>(type: "TEXT", nullable: false),
                    CargoName = table.Column<string>(type: "TEXT", maxLength: 250, nullable: false),
                    QuantityExpected = table.Column<double>(type: "float", nullable: false),
                    Unit = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
                    ExpectedLoadingDate = table.Column<DateTime>(type: "TEXT", nullable: false),
                    ActualLoadingDate = table.Column<DateTime>(type: "TEXT", nullable: true),
                    LoadingPort = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    DischargingPort = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    Status = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
                    Notes = table.Column<string>(type: "TEXT", maxLength: 1000, nullable: false),
                    CreatedByUserId = table.Column<Guid>(type: "TEXT", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AvisChargements", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AvisChargements_Escales_EscaleId",
                        column: x => x.EscaleId,
                        principalTable: "Escales",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_AvisChargements_Users_CreatedByUserId",
                        column: x => x.CreatedByUserId,
                        principalTable: "Users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "DraftSurveys",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "TEXT", nullable: false),
                    SurveyNumber = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "TEXT", nullable: false),
                    Status = table.Column<string>(type: "TEXT", maxLength: 20, nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "TEXT", nullable: true),
                    VesselId = table.Column<Guid>(type: "TEXT", nullable: false),
                    EscaleId = table.Column<Guid>(type: "TEXT", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DraftSurveys", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DraftSurveys_Escales_EscaleId",
                        column: x => x.EscaleId,
                        principalTable: "Escales",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_DraftSurveys_Vessels_VesselId",
                        column: x => x.VesselId,
                        principalTable: "Vessels",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "Inspections",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "TEXT", nullable: false),
                    InspectionDate = table.Column<DateTime>(type: "TEXT", nullable: false),
                    InspectorName = table.Column<string>(type: "TEXT", nullable: false),
                    Notes = table.Column<string>(type: "TEXT", nullable: false),
                    EscaleId = table.Column<Guid>(type: "TEXT", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Inspections", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Inspections_Escales_EscaleId",
                        column: x => x.EscaleId,
                        principalTable: "Escales",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "Ballasts",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "TEXT", nullable: false),
                    TankName = table.Column<string>(type: "TEXT", nullable: false),
                    Volume = table.Column<double>(type: "REAL", nullable: false),
                    Density = table.Column<double>(type: "REAL", nullable: false),
                    DraftSurveyId = table.Column<Guid>(type: "TEXT", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Ballasts", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Ballasts_DraftSurveys_DraftSurveyId",
                        column: x => x.DraftSurveyId,
                        principalTable: "DraftSurveys",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "DraughtMarkReadings",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "TEXT", nullable: false),
                    ReadingTime = table.Column<DateTime>(type: "TEXT", nullable: false),
                    PortForward = table.Column<double>(type: "REAL", nullable: false),
                    StarboardForward = table.Column<double>(type: "REAL", nullable: false),
                    PortMidship = table.Column<double>(type: "REAL", nullable: false),
                    StarboardMidship = table.Column<double>(type: "REAL", nullable: false),
                    PortAft = table.Column<double>(type: "REAL", nullable: false),
                    StarboardAft = table.Column<double>(type: "REAL", nullable: false),
                    DraftSurveyId = table.Column<Guid>(type: "TEXT", nullable: false),
                    ForwardMean = table.Column<double>(type: "REAL", nullable: false),
                    MidshipMean = table.Column<double>(type: "REAL", nullable: false),
                    AftMean = table.Column<double>(type: "REAL", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DraughtMarkReadings", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DraughtMarkReadings_DraftSurveys_DraftSurveyId",
                        column: x => x.DraftSurveyId,
                        principalTable: "DraftSurveys",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "DraughtMarksCorrections",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "TEXT", nullable: false),
                    DraftSurveyId = table.Column<Guid>(type: "TEXT", nullable: false),
                    CorrectionType = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    Value = table.Column<double>(type: "float", nullable: false),
                    Unit = table.Column<string>(type: "TEXT", maxLength: 20, nullable: false),
                    AppliedToLocation = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
                    CorrectionDate = table.Column<DateTime>(type: "TEXT", nullable: false),
                    Notes = table.Column<string>(type: "TEXT", maxLength: 500, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DraughtMarksCorrections", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DraughtMarksCorrections_DraftSurveys_DraftSurveyId",
                        column: x => x.DraftSurveyId,
                        principalTable: "DraftSurveys",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "DraughtSurveyDocuments",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "TEXT", nullable: false),
                    DraftSurveyId = table.Column<Guid>(type: "TEXT", nullable: false),
                    DocumentId = table.Column<Guid>(type: "TEXT", nullable: false),
                    AttachedDate = table.Column<DateTime>(type: "TEXT", nullable: false),
                    UsageType = table.Column<string>(type: "TEXT", maxLength: 50, nullable: true),
                    Notes = table.Column<string>(type: "TEXT", maxLength: 500, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DraughtSurveyDocuments", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DraughtSurveyDocuments_Documents_DocumentId",
                        column: x => x.DocumentId,
                        principalTable: "Documents",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_DraughtSurveyDocuments_DraftSurveys_DraftSurveyId",
                        column: x => x.DraftSurveyId,
                        principalTable: "DraftSurveys",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "DraughtSurveyGlobalCalculations",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "TEXT", nullable: false),
                    DraftSurveyId = table.Column<Guid>(type: "TEXT", nullable: false),
                    CalculationDate = table.Column<DateTime>(type: "TEXT", nullable: false),
                    InitialDisplacement = table.Column<double>(type: "float", nullable: false),
                    FinalDisplacement = table.Column<double>(type: "float", nullable: false),
                    TotalBallastWater = table.Column<double>(type: "float", nullable: false),
                    TotalFreshWater = table.Column<double>(type: "float", nullable: false),
                    TotalFuelOil = table.Column<double>(type: "float", nullable: false),
                    TotalDieselOil = table.Column<double>(type: "float", nullable: false),
                    TotalLubricatingOil = table.Column<double>(type: "float", nullable: false),
                    TotalConstant = table.Column<double>(type: "float", nullable: false),
                    NetCargoWeight = table.Column<double>(type: "float", nullable: false),
                    Difference = table.Column<double>(type: "float", nullable: false),
                    CalculationStatus = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
                    VerifiedByUserId = table.Column<Guid>(type: "TEXT", nullable: true),
                    VerifiedDate = table.Column<DateTime>(type: "TEXT", nullable: true),
                    FirstTrimCorrection = table.Column<double>(type: "float", nullable: false),
                    SecondTrimCorrection = table.Column<double>(type: "float", nullable: false),
                    IsComplete = table.Column<bool>(type: "INTEGER", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DraughtSurveyGlobalCalculations", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DraughtSurveyGlobalCalculations_DraftSurveys_DraftSurveyId",
                        column: x => x.DraftSurveyId,
                        principalTable: "DraftSurveys",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_DraughtSurveyGlobalCalculations_Users_VerifiedByUserId",
                        column: x => x.VerifiedByUserId,
                        principalTable: "Users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "FreshWaterTanks",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "TEXT", nullable: false),
                    TankName = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    Volume = table.Column<double>(type: "float", nullable: false),
                    Density = table.Column<double>(type: "float", nullable: false),
                    DraftSurveyId = table.Column<Guid>(type: "TEXT", nullable: false),
                    MeasurementTime = table.Column<DateTime>(type: "TEXT", nullable: false),
                    Notes = table.Column<string>(type: "TEXT", maxLength: 500, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_FreshWaterTanks", x => x.Id);
                    table.ForeignKey(
                        name: "FK_FreshWaterTanks_DraftSurveys_DraftSurveyId",
                        column: x => x.DraftSurveyId,
                        principalTable: "DraftSurveys",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "LiquidStatements",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "TEXT", nullable: false),
                    DraftSurveyId = table.Column<Guid>(type: "TEXT", nullable: false),
                    LiquidId = table.Column<Guid>(type: "TEXT", nullable: false),
                    Volume = table.Column<double>(type: "float", nullable: false),
                    MeasuredDensity = table.Column<double>(type: "float", nullable: true),
                    MeasurementTime = table.Column<DateTime>(type: "TEXT", nullable: false),
                    TankName = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    Notes = table.Column<string>(type: "TEXT", maxLength: 500, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_LiquidStatements", x => x.Id);
                    table.ForeignKey(
                        name: "FK_LiquidStatements_DraftSurveys_DraftSurveyId",
                        column: x => x.DraftSurveyId,
                        principalTable: "DraftSurveys",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_LiquidStatements_Liquids_LiquidId",
                        column: x => x.LiquidId,
                        principalTable: "Liquids",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "SeaWaterDensityStatements",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "TEXT", nullable: false),
                    DraftSurveyId = table.Column<Guid>(type: "TEXT", nullable: false),
                    DensityValue = table.Column<double>(type: "float", nullable: false),
                    MeasurementDate = table.Column<DateTime>(type: "TEXT", nullable: false),
                    MeasurementTime = table.Column<DateTime>(type: "TEXT", nullable: false),
                    Density = table.Column<double>(type: "REAL", nullable: false),
                    Temperature = table.Column<double>(type: "REAL", nullable: false),
                    MeasurementMethod = table.Column<string>(type: "TEXT", nullable: false),
                    Location = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    Notes = table.Column<string>(type: "TEXT", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SeaWaterDensityStatements", x => x.Id);
                    table.ForeignKey(
                        name: "FK_SeaWaterDensityStatements_DraftSurveys_DraftSurveyId",
                        column: x => x.DraftSurveyId,
                        principalTable: "DraftSurveys",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "TaskAssignments",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "TEXT", nullable: false),
                    DraftSurveyId = table.Column<Guid>(type: "TEXT", nullable: false),
                    UserId = table.Column<Guid>(type: "TEXT", nullable: false),
                    AssignmentDate = table.Column<DateTime>(type: "TEXT", nullable: false, defaultValueSql: "datetime('now')"),
                    CompletionDate = table.Column<DateTime>(type: "TEXT", nullable: true),
                    Status = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false, defaultValue: "Assigned"),
                    Notes = table.Column<string>(type: "TEXT", maxLength: 1000, nullable: false),
                    TaskType = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
                    IsCompleted = table.Column<bool>(type: "INTEGER", nullable: false, defaultValue: false),
                    TaskData = table.Column<string>(type: "TEXT", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TaskAssignments", x => x.Id);
                    table.ForeignKey(
                        name: "FK_TaskAssignments_DraftSurveys_DraftSurveyId",
                        column: x => x.DraftSurveyId,
                        principalTable: "DraftSurveys",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_TaskAssignments_Users_UserId",
                        column: x => x.UserId,
                        principalTable: "Users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "HoldInspections",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "TEXT", nullable: false),
                    InspectionId = table.Column<Guid>(type: "TEXT", nullable: false),
                    HoldNumber = table.Column<int>(type: "INTEGER", nullable: false),
                    Condition = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    Notes = table.Column<string>(type: "TEXT", maxLength: 500, nullable: false),
                    IsApproved = table.Column<bool>(type: "INTEGER", nullable: false),
                    RejectionReason = table.Column<string>(type: "TEXT", maxLength: 500, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_HoldInspections", x => x.Id);
                    table.ForeignKey(
                        name: "FK_HoldInspections_Inspections_InspectionId",
                        column: x => x.InspectionId,
                        principalTable: "Inspections",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "InspectionReports",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "TEXT", nullable: false),
                    InspectionId = table.Column<Guid>(type: "TEXT", nullable: false),
                    ReportContent = table.Column<string>(type: "TEXT", nullable: false),
                    GeneratedDate = table.Column<DateTime>(type: "TEXT", nullable: false),
                    ReportType = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    Status = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
                    GeneratedByUserId = table.Column<Guid>(type: "TEXT", nullable: false),
                    ApprovedDate = table.Column<DateTime>(type: "TEXT", nullable: true),
                    ApprovedByUserId = table.Column<Guid>(type: "TEXT", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_InspectionReports", x => x.Id);
                    table.ForeignKey(
                        name: "FK_InspectionReports_Inspections_InspectionId",
                        column: x => x.InspectionId,
                        principalTable: "Inspections",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_InspectionReports_Users_ApprovedByUserId",
                        column: x => x.ApprovedByUserId,
                        principalTable: "Users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_InspectionReports_Users_GeneratedByUserId",
                        column: x => x.GeneratedByUserId,
                        principalTable: "Users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.InsertData(
                table: "Roles",
                columns: new[] { "Id", "CanManageAllPorts", "CanManageUsers", "Description", "Name" },
                values: new object[] { new Guid("00000000-0000-0000-0000-000000000001"), true, true, "Administrateur système avec tous les droits", "Admin" });

            migrationBuilder.InsertData(
                table: "Roles",
                columns: new[] { "Id", "CanManageAllPorts", "Description", "Name" },
                values: new object[] { new Guid("00000000-0000-0000-0000-000000000002"), true, "Chef d'équipe avec des droits étendus", "TeamLead" });

            migrationBuilder.InsertData(
                table: "Roles",
                columns: new[] { "Id", "Description", "Name" },
                values: new object[] { new Guid("00000000-0000-0000-0000-000000000003"), "Utilisateur standard avec droits limités", "Agent" });

            migrationBuilder.CreateIndex(
                name: "IX_AvisChargements_CreatedByUserId",
                table: "AvisChargements",
                column: "CreatedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_AvisChargements_EscaleId",
                table: "AvisChargements",
                column: "EscaleId");

            migrationBuilder.CreateIndex(
                name: "IX_Ballasts_DraftSurveyId",
                table: "Ballasts",
                column: "DraftSurveyId");

            migrationBuilder.CreateIndex(
                name: "IX_Documents_UploadedByUserId",
                table: "Documents",
                column: "UploadedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_DraftSurveys_EscaleId",
                table: "DraftSurveys",
                column: "EscaleId");

            migrationBuilder.CreateIndex(
                name: "IX_DraftSurveys_VesselId",
                table: "DraftSurveys",
                column: "VesselId");

            migrationBuilder.CreateIndex(
                name: "IX_DraughtMarkReadings_DraftSurveyId",
                table: "DraughtMarkReadings",
                column: "DraftSurveyId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_DraughtMarksCorrections_DraftSurveyId",
                table: "DraughtMarksCorrections",
                column: "DraftSurveyId");

            migrationBuilder.CreateIndex(
                name: "IX_DraughtSurveyDocuments_DocumentId",
                table: "DraughtSurveyDocuments",
                column: "DocumentId");

            migrationBuilder.CreateIndex(
                name: "IX_DraughtSurveyDocuments_DraftSurveyId",
                table: "DraughtSurveyDocuments",
                column: "DraftSurveyId");

            migrationBuilder.CreateIndex(
                name: "IX_DraughtSurveyGlobalCalculations_DraftSurveyId",
                table: "DraughtSurveyGlobalCalculations",
                column: "DraftSurveyId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_DraughtSurveyGlobalCalculations_VerifiedByUserId",
                table: "DraughtSurveyGlobalCalculations",
                column: "VerifiedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_Escales_PortId",
                table: "Escales",
                column: "PortId");

            migrationBuilder.CreateIndex(
                name: "IX_Escales_VesselId",
                table: "Escales",
                column: "VesselId");

            migrationBuilder.CreateIndex(
                name: "IX_FreshWaterTanks_DraftSurveyId",
                table: "FreshWaterTanks",
                column: "DraftSurveyId");

            migrationBuilder.CreateIndex(
                name: "IX_HoldInspections_InspectionId",
                table: "HoldInspections",
                column: "InspectionId");

            migrationBuilder.CreateIndex(
                name: "IX_InspectionReports_ApprovedByUserId",
                table: "InspectionReports",
                column: "ApprovedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_InspectionReports_GeneratedByUserId",
                table: "InspectionReports",
                column: "GeneratedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_InspectionReports_InspectionId",
                table: "InspectionReports",
                column: "InspectionId");

            migrationBuilder.CreateIndex(
                name: "IX_Inspections_EscaleId",
                table: "Inspections",
                column: "EscaleId");

            migrationBuilder.CreateIndex(
                name: "IX_Liquids_Name",
                table: "Liquids",
                column: "Name",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_LiquidStatements_DraftSurveyId",
                table: "LiquidStatements",
                column: "DraftSurveyId");

            migrationBuilder.CreateIndex(
                name: "IX_LiquidStatements_LiquidId",
                table: "LiquidStatements",
                column: "LiquidId");

            migrationBuilder.CreateIndex(
                name: "IX_Roles_Name",
                table: "Roles",
                column: "Name",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_SeaWaterDensityStatements_DraftSurveyId",
                table: "SeaWaterDensityStatements",
                column: "DraftSurveyId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_TaskAssignments_DraftSurveyId",
                table: "TaskAssignments",
                column: "DraftSurveyId");

            migrationBuilder.CreateIndex(
                name: "IX_TaskAssignments_IsCompleted",
                table: "TaskAssignments",
                column: "IsCompleted");

            migrationBuilder.CreateIndex(
                name: "IX_TaskAssignments_TaskType",
                table: "TaskAssignments",
                column: "TaskType");

            migrationBuilder.CreateIndex(
                name: "IX_TaskAssignments_UserId",
                table: "TaskAssignments",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_Users_PortId",
                table: "Users",
                column: "PortId");

            migrationBuilder.CreateIndex(
                name: "IX_Users_RoleId",
                table: "Users",
                column: "RoleId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "AvisChargements");

            migrationBuilder.DropTable(
                name: "Ballasts");

            migrationBuilder.DropTable(
                name: "DraughtMarkReadings");

            migrationBuilder.DropTable(
                name: "DraughtMarksCorrections");

            migrationBuilder.DropTable(
                name: "DraughtSurveyDocuments");

            migrationBuilder.DropTable(
                name: "DraughtSurveyGlobalCalculations");

            migrationBuilder.DropTable(
                name: "FreshWaterTanks");

            migrationBuilder.DropTable(
                name: "HoldInspections");

            migrationBuilder.DropTable(
                name: "InspectionReports");

            migrationBuilder.DropTable(
                name: "LiquidStatements");

            migrationBuilder.DropTable(
                name: "SeaWaterDensityStatements");

            migrationBuilder.DropTable(
                name: "TaskAssignments");

            migrationBuilder.DropTable(
                name: "Documents");

            migrationBuilder.DropTable(
                name: "Inspections");

            migrationBuilder.DropTable(
                name: "Liquids");

            migrationBuilder.DropTable(
                name: "DraftSurveys");

            migrationBuilder.DropTable(
                name: "Users");

            migrationBuilder.DropTable(
                name: "Escales");

            migrationBuilder.DropTable(
                name: "Roles");

            migrationBuilder.DropTable(
                name: "Ports");

            migrationBuilder.DropTable(
                name: "Vessels");
        }
    }
}
