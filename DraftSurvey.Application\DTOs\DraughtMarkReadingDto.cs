using System;
using System.ComponentModel.DataAnnotations;

namespace DraftSurvey.Application.DTOs
{
    // DTO for GET/PUT
    public class DraughtMarkReadingDto
    {
        public Guid Id { get; set; }

        public double PortForward { get; set; }
        public double StarboardForward { get; set; }
        public double PortMidship { get; set; }
        public double StarboardMidship { get; set; }
        public double PortAft { get; set; }
        public double StarboardAft { get; set; }

        public DateTime ReadingTime { get; set; }

        // Important: Match the entity name exactly
        public Guid DraftSurveyId { get; set; }
    }

    // DTO for POST
    public class DraughtMarkReadingCreateDto
    {
        [Required]
        public double PortForward { get; set; }

        [Required]
        public double StarboardForward { get; set; }

        [Required]
        public double PortMidship { get; set; }

        [Required]
        public double StarboardMidship { get; set; }

        [Required]
        public double PortAft { get; set; }

        [Required]
        public double StarboardAft { get; set; }

        public DateTime ReadingTime { get; set; } = DateTime.UtcNow;

        [Required]
        public Guid DraftSurveyId { get; set; } // renamed from SurveyId
    }

    // DTO for PUT (update)
    public class DraughtMarkReadingUpdateDto
    {
        [Required]
        public double PortForward { get; set; }

        [Required]
        public double StarboardForward { get; set; }

        [Required]
        public double PortMidship { get; set; }

        [Required]
        public double StarboardMidship { get; set; }

        [Required]
        public double PortAft { get; set; }

        [Required]
        public double StarboardAft { get; set; }

        public DateTime ReadingTime { get; set; }
    }
}
