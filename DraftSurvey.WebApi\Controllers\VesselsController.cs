using AutoMapper;
using DraftSurvey.Application.DTOs;
using DraftSurvey.Domain.Entities;
using DraftSurvey.Domain.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace DraftSurvey.WebApi.Controllers
{
    [Authorize]
    [ApiController]
    [Route("api/vessels")]
    public class VesselsController : ControllerBase
    {
        private readonly IVesselRepository _vesselRepository;
        private readonly IMapper _mapper;
        private readonly ILogger<VesselsController> _logger;

        public VesselsController(
            IVesselRepository vesselRepository,
            IMapper mapper,
            ILogger<VesselsController> logger)
        {
            _vesselRepository = vesselRepository;
            _mapper = mapper;
            _logger = logger;
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<VesselDto>>> GetAll()
        {
            var vessels = await _vesselRepository.GetAllAsync();
            return Ok(_mapper.Map<IEnumerable<VesselDto>>(vessels));
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<VesselDetailsDto>> GetById(Guid id)
        {
            var vessel = await _vesselRepository.GetByIdAsync(id);
            if (vessel == null) return NotFound();
            return Ok(_mapper.Map<VesselDetailsDto>(vessel));
        }

        [HttpGet("search")]
        public async Task<ActionResult<IEnumerable<VesselDto>>> Search([FromQuery] string name)
        {
            var vessels = await _vesselRepository.SearchByNameAsync(name);
            return Ok(_mapper.Map<IEnumerable<VesselDto>>(vessels));
        }

        [HttpGet("imo/{imoNumber}")]
        public async Task<ActionResult<VesselDto>> GetByImo(string imoNumber)
        {
            var vessel = await _vesselRepository.GetByIMONumberAsync(imoNumber);
            if (vessel == null) return NotFound();
            return Ok(_mapper.Map<VesselDto>(vessel));
        }

        [HttpPost]
        [Authorize(Roles = "Admin,TeamLead")]
        public async Task<ActionResult<VesselDto>> Create([FromBody] VesselCreateDto dto)
        {
            var vessel = _mapper.Map<Vessel>(dto);
            await _vesselRepository.AddAsync(vessel);
            return CreatedAtAction(nameof(GetById), new { id = vessel.Id }, _mapper.Map<VesselDto>(vessel));
        }

        [HttpPut("{id}")]
        [Authorize(Roles = "Admin,TeamLead")]
        public async Task<IActionResult> Update(Guid id, [FromBody] VesselUpdateDto dto)
        {
            var vessel = await _vesselRepository.GetByIdAsync(id);
            if (vessel == null) return NotFound();

            _mapper.Map(dto, vessel);
            await _vesselRepository.UpdateAsync(vessel);
            return NoContent();
        }

        [HttpPut("{id}/hydrostatic")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> UpdateHydrostaticData(Guid id, [FromBody] HydrostaticDataDto data)
        {
            await _vesselRepository.UpdateHydrostaticDataAsync(id, data.DisplacementFactor, data.TPC);
            return NoContent();
        }

        [HttpDelete("{id}")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> Delete(Guid id)
        {
            await _vesselRepository.DeleteAsync(id);
            return NoContent();
        }
    }
}