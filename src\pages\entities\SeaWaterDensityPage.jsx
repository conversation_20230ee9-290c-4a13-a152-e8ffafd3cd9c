import React, { useState, useEffect } from 'react';
import EntityPageLayout from '../../components/Shared/EntityPageLayout';
import { Waves, Thermometer, Activity, MapPin, Calendar } from 'lucide-react';

const SeaWaterDensityPage = () => {
  const [seaWaterDensity, setSeaWaterDensity] = useState([]);
  const [loading, setLoading] = useState(true);

  // Mock data - replace with actual API call
  useEffect(() => {
    const mockSeaWaterDensity = [
      {
        id: 1,
        measurementId: "SWD-2024-001",
        vesselName: "MSC OSCAR",
        location: "Port of Rotterdam",
        coordinates: "51.9244° N, 4.4777° E",
        density: 1.025,
        temperature: 12.5,
        salinity: 35.2,
        measurementDate: "2024-01-15T10:30:00Z",
        measurementMethod: "Hydrometer",
        depth: 5.0,
        weather: "Clear",
        seaState: "Calm",
        inspector: "<PERSON>",
        notes: "Standard port water density measurement"
      },
      {
        id: 2,
        measurementId: "SWD-2024-002",
        vesselName: "EVER GIVEN",
        location: "Suez Canal",
        coordinates: "30.0444° N, 32.3497° E",
        density: 1.028,
        temperature: 24.8,
        salinity: 40.1,
        measurementDate: "2024-01-14T14:15:00Z",
        measurementMethod: "Digital Densimeter",
        depth: 8.0,
        weather: "Sunny",
        seaState: "Slight",
        inspector: "Ahmed Hassan",
        notes: "Canal transit measurement - higher salinity"
      },
      {
        id: 3,
        measurementId: "SWD-2024-003",
        vesselName: "MAERSK MADRID",
        location: "Singapore Strait",
        coordinates: "1.2966° N, 103.8558° E",
        density: 1.024,
        temperature: 28.2,
        salinity: 34.8,
        measurementDate: "2024-01-13T09:45:00Z",
        measurementMethod: "Pycnometer",
        depth: 12.0,
        weather: "Partly Cloudy",
        seaState: "Moderate",
        inspector: "Li Wei",
        notes: "Tropical waters measurement"
      },
      {
        id: 4,
        measurementId: "SWD-2024-004",
        vesselName: "MSC OSCAR",
        location: "North Sea",
        coordinates: "54.5973° N, 5.9301° E",
        density: 1.026,
        temperature: 8.1,
        salinity: 35.0,
        measurementDate: "2024-01-12T16:20:00Z",
        measurementMethod: "Hydrometer",
        depth: 15.0,
        weather: "Overcast",
        seaState: "Rough",
        inspector: "Erik Larsen",
        notes: "Open sea measurement in rough conditions"
      },
      {
        id: 5,
        measurementId: "SWD-2024-005",
        vesselName: "EVER GIVEN",
        location: "Mediterranean Sea",
        coordinates: "35.1264° N, 33.4299° E",
        density: 1.029,
        temperature: 18.7,
        salinity: 38.5,
        measurementDate: "2024-01-11T11:30:00Z",
        measurementMethod: "Digital Densimeter",
        depth: 20.0,
        weather: "Clear",
        seaState: "Calm",
        inspector: "Maria Rossi",
        notes: "Mediterranean high salinity waters"
      }
    ];

    setTimeout(() => {
      setSeaWaterDensity(mockSeaWaterDensity);
      setLoading(false);
    }, 1000);
  }, []);

  const columns = [
    {
      header: 'Measurement Details',
      key: 'measurementId',
      render: (measurement) => (
        <div className="flex items-center">
          <div className="flex-shrink-0 h-10 w-10">
            <div className="h-10 w-10 rounded-full bg-cyan-100 flex items-center justify-center">
              <Waves className="h-5 w-5 text-cyan-600" />
            </div>
          </div>
          <div className="ml-4">
            <div className="text-sm font-medium text-gray-900">{measurement.measurementId}</div>
            <div className="text-sm text-gray-500">{measurement.vesselName}</div>
          </div>
        </div>
      )
    },
    {
      header: 'Location',
      key: 'location',
      render: (measurement) => (
        <div>
          <div className="text-sm text-gray-900 flex items-center">
            <MapPin className="h-4 w-4 text-gray-400 mr-1" />
            {measurement.location}
          </div>
          <div className="text-xs text-gray-500">{measurement.coordinates}</div>
        </div>
      )
    },
    {
      header: 'Density',
      key: 'density',
      render: (measurement) => {
        const densityColor = measurement.density >= 1.027 ? 'text-blue-600' : 
                            measurement.density >= 1.024 ? 'text-green-600' : 'text-yellow-600';
        
        return (
          <div className="text-sm">
            <div className={`font-semibold ${densityColor}`}>
              {measurement.density.toFixed(3)} kg/L
            </div>
            <div className="text-gray-500">
              Depth: {measurement.depth.toFixed(1)}m
            </div>
          </div>
        );
      }
    },
    {
      header: 'Temperature & Salinity',
      key: 'temperature',
      render: (measurement) => (
        <div className="text-sm">
          <div className="text-gray-900 flex items-center">
            <Thermometer className="h-4 w-4 text-red-500 mr-1" />
            {measurement.temperature.toFixed(1)}°C
          </div>
          <div className="text-gray-500">
            Salinity: {measurement.salinity.toFixed(1)}‰
          </div>
        </div>
      )
    },
    {
      header: 'Method',
      key: 'measurementMethod',
      render: (measurement) => {
        const methodColors = {
          'Hydrometer': 'bg-blue-100 text-blue-800',
          'Digital Densimeter': 'bg-green-100 text-green-800',
          'Pycnometer': 'bg-purple-100 text-purple-800',
          'Refractometer': 'bg-orange-100 text-orange-800'
        };
        
        return (
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${methodColors[measurement.measurementMethod] || 'bg-gray-100 text-gray-800'}`}>
            {measurement.measurementMethod}
          </span>
        );
      }
    },
    {
      header: 'Conditions',
      key: 'conditions',
      render: (measurement) => (
        <div className="text-sm">
          <div className="text-gray-900">{measurement.weather}</div>
          <div className="text-gray-500">Sea: {measurement.seaState}</div>
        </div>
      )
    },
    {
      header: 'Inspector',
      key: 'inspector',
      render: (measurement) => (
        <div className="text-sm text-gray-900">{measurement.inspector}</div>
      )
    },
    {
      header: 'Date & Time',
      key: 'measurementDate',
      render: (measurement) => (
        <div className="text-sm">
          <div className="text-gray-900 flex items-center">
            <Calendar className="h-4 w-4 text-gray-400 mr-1" />
            {new Date(measurement.measurementDate).toLocaleDateString()}
          </div>
          <div className="text-gray-500">
            {new Date(measurement.measurementDate).toLocaleTimeString()}
          </div>
        </div>
      )
    }
  ];

  const handleAdd = () => {
    console.log('Add new sea water density measurement');
  };

  const handleEdit = (measurement) => {
    console.log('Edit measurement:', measurement);
  };

  const handleDelete = (measurement) => {
    console.log('Delete measurement:', measurement);
  };

  const handleView = (measurement) => {
    console.log('View measurement:', measurement);
  };

  const averageDensity = seaWaterDensity.reduce((sum, m) => sum + m.density, 0) / seaWaterDensity.length || 0;
  const averageTemperature = seaWaterDensity.reduce((sum, m) => sum + m.temperature, 0) / seaWaterDensity.length || 0;
  const averageSalinity = seaWaterDensity.reduce((sum, m) => sum + m.salinity, 0) / seaWaterDensity.length || 0;
  const uniqueLocations = new Set(seaWaterDensity.map(m => m.location)).size;

  return (
    <EntityPageLayout
      title="Sea Water Density Management"
      description="Monitor sea water density measurements for accurate draft survey calculations and vessel stability assessments."
      data={seaWaterDensity}
      columns={columns}
      onAdd={handleAdd}
      onEdit={handleEdit}
      onDelete={handleDelete}
      onView={handleView}
      loading={loading}
      searchPlaceholder="Search measurements by location, vessel, inspector..."
    >
      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Waves className="h-8 w-8 text-cyan-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Total Measurements</p>
              <p className="text-2xl font-semibold text-gray-900">{seaWaterDensity.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Activity className="h-8 w-8 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Avg Density</p>
              <p className="text-2xl font-semibold text-gray-900">{averageDensity.toFixed(3)}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Thermometer className="h-8 w-8 text-red-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Avg Temperature</p>
              <p className="text-2xl font-semibold text-gray-900">{averageTemperature.toFixed(1)}°C</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <MapPin className="h-8 w-8 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Locations</p>
              <p className="text-2xl font-semibold text-gray-900">{uniqueLocations}</p>
            </div>
          </div>
        </div>
      </div>
    </EntityPageLayout>
  );
};

export default SeaWaterDensityPage;
