import React, { useState } from 'react';
import {
  Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper,
  Chip, Avatar, Button, MenuItem, Select, FormControl, InputLabel, IconButton,
  Dialog, DialogTitle, DialogContent, DialogActions, TextField, Box, Typography,
  CircularProgress, Alert
} from "@mui/material";
import {
  Check, Close, Send, Done, Delete, Visibility
} from "@mui/icons-material";
import { completeTask, submitTask, approveTask, rejectTask, deleteTaskAssignment } from "../../api/taskAssignmentsApi";

const statusColors = {
  Assigned: 'default',
  InProgress: 'primary',
  Completed: 'success',
  PendingValidation: 'warning',
  Approved: 'success',
  Rejected: 'error'
};

const TaskList = ({ tasks, surveys, onTaskUpdate, currentUserRole, onViewDetails }) => {
  const [selectedTask, setSelectedTask] = useState(null);
  const [completeDialogOpen, setCompleteDialogOpen] = useState(false);
  const [submitDialogOpen, setSubmitDialogOpen] = useState(false);
  const [approveDialogOpen, setApproveDialogOpen] = useState(false);
  const [rejectDialogOpen, setRejectDialogOpen] = useState(false);
  const [notes, setNotes] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const handleComplete = async () => {
    try {
      setLoading(true);
      await completeTask(selectedTask.id, { notes });
      onTaskUpdate();
      setCompleteDialogOpen(false);
      setNotes('');
      setError(null);
    } catch (err) {
      setError('Erreur lors de la complétion de la tâche');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async () => {
    try {
      setLoading(true);
      await submitTask(selectedTask.id, { notes });
      onTaskUpdate();
      setSubmitDialogOpen(false);
      setNotes('');
      setError(null);
    } catch (err) {
      setError('Erreur lors de la soumission de la tâche');
    } finally {
      setLoading(false);
    }
  };

  const handleApprove = async () => {
    try {
      setLoading(true);
      await approveTask(selectedTask.id, { comment: notes });
      onTaskUpdate();
      setApproveDialogOpen(false);
      setNotes('');
      setError(null);
    } catch (err) {
      setError('Erreur lors de l\'approbation de la tâche');
    } finally {
      setLoading(false);
    }
  };

  const handleReject = async () => {
    try {
      setLoading(true);
      await rejectTask(selectedTask.id, { reason: notes, requiresResubmission: true });
      onTaskUpdate();
      setRejectDialogOpen(false);
      setNotes('');
      setError(null);
    } catch (err) {
      setError('Erreur lors du rejet de la tâche');
    } finally {
      setLoading(false);
    }
  };

  const renderActionButtons = (task) => {
    switch (task.status) {
      case 'Assigned':
      case 'InProgress':
        return (
          <Button
            startIcon={<Done />}
            onClick={() => {
              setSelectedTask(task);
              setCompleteDialogOpen(true);
            }}
            variant="contained"
            size="small"
          >
            Compléter
          </Button>
        );
      case 'Completed':
        return (
          <Button
            startIcon={<Send />}
            onClick={() => {
              setSelectedTask(task);
              setSubmitDialogOpen(true);
            }}
            variant="contained"
            size="small"
            color="secondary"
          >
            Soumettre
          </Button>
        );
      case 'PendingValidation':
        if (currentUserRole === 'TeamLead') {
          return (
            <Box display="flex" gap={1}>
              <Button
                startIcon={<Check />}
                onClick={() => {
                  setSelectedTask(task);
                  setApproveDialogOpen(true);
                }}
                variant="contained"
                size="small"
                color="success"
              >
                Approuver
              </Button>
              <Button
                startIcon={<Close />}
                onClick={() => {
                  setSelectedTask(task);
                  setRejectDialogOpen(true);
                }}
                variant="outlined"
                size="small"
                color="error"
              >
                Rejeter
              </Button>
            </Box>
          );
        }
        return null;
      default:
        return null;
    }
  };

  return (
    <>
      {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>}

      <TableContainer component={Paper} sx={{ mt: 2 }}>
        <Table>
          <TableHead>
            <TableRow sx={{ backgroundColor: '#f5f5f5' }}>
              <TableCell>Survey</TableCell>
              <TableCell>Type</TableCell>
              <TableCell>Assigné à</TableCell>
              <TableCell>Statut</TableCell>
              <TableCell>Actions</TableCell>
              {currentUserRole === 'TeamLead' && <TableCell>Admin</TableCell>}
            </TableRow>
          </TableHead>
          <TableBody>
            {tasks.length > 0 ? (
              tasks.map((task) => (
                <TableRow key={task.id}>
                  <TableCell>
                    <Typography fontWeight="bold">{task.surveyNumber}</Typography>
                    {task.vesselName && (
                      <Typography variant="body2" color="textSecondary">
                        {task.vesselName}
                      </Typography>
                    )}
                  </TableCell>
                  <TableCell>{task.taskType}</TableCell>
                  <TableCell>
                    {task.user ? (
                      <Box display="flex" alignItems="center" gap={1}>
                        <Avatar sx={{ width: 32, height: 32, bgcolor: 'primary.main' }}>
                          {task.user.fullName?.charAt(0) || 'U'}
                        </Avatar>
                        <Typography>{task.user.fullName}</Typography>
                      </Box>
                    ) : (
                      <Typography color="textSecondary">Non assigné</Typography>
                    )}
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={task.status}
                      color={statusColors[task.status] || 'default'}
                      variant="outlined"
                    />
                  </TableCell>
                  <TableCell>
                    <Box display="flex" gap={1}>
                      {renderActionButtons(task)}
                      <Button 
                        variant="outlined" 
                        size="small"
                        startIcon={<Visibility />}
                        onClick={() => onViewDetails(task)}
                      >
                        Détails
                      </Button>
                    </Box>
                  </TableCell>
                  {currentUserRole === 'TeamLead' && (
                    <TableCell>
                      <IconButton 
                        onClick={() => deleteTaskAssignment(task.id).then(onTaskUpdate)}
                        color="error"
                      >
                        <Delete />
                      </IconButton>
                    </TableCell>
                  )}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={6} align="center" sx={{ py: 4 }}>
                  <Typography color="textSecondary">
                    Aucune tâche trouvée
                  </Typography>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Complete Task Dialog */}
      <Dialog open={completeDialogOpen} onClose={() => setCompleteDialogOpen(false)}>
        <DialogTitle>Marquer la tâche comme complète</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Notes (optionnel)"
            fullWidth
            multiline
            rows={4}
            value={notes}
            onChange={(e) => setNotes(e.target.value)}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCompleteDialogOpen(false)}>Annuler</Button>
          <Button onClick={handleComplete} color="primary" variant="contained" disabled={loading}>
            {loading ? <CircularProgress size={24} /> : "Confirmer"}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Submit Task Dialog */}
      <Dialog open={submitDialogOpen} onClose={() => setSubmitDialogOpen(false)}>
        <DialogTitle>Soumettre la tâche pour validation</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Notes pour le validateur"
            fullWidth
            multiline
            rows={4}
            value={notes}
            onChange={(e) => setNotes(e.target.value)}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setSubmitDialogOpen(false)}>Annuler</Button>
          <Button onClick={handleSubmit} color="primary" variant="contained" disabled={loading}>
            {loading ? <CircularProgress size={24} /> : "Soumettre"}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Approve Task Dialog */}
      <Dialog open={approveDialogOpen} onClose={() => setApproveDialogOpen(false)}>
        <DialogTitle>Approuver la tâche</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Commentaire (optionnel)"
            fullWidth
            multiline
            rows={4}
            value={notes}
            onChange={(e) => setNotes(e.target.value)}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setApproveDialogOpen(false)}>Annuler</Button>
          <Button onClick={handleApprove} color="success" variant="contained" disabled={loading}>
            {loading ? <CircularProgress size={24} /> : "Approuver"}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Reject Task Dialog */}
      <Dialog open={rejectDialogOpen} onClose={() => setRejectDialogOpen(false)}>
        <DialogTitle>Rejeter la tâche</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Raison du rejet"
            fullWidth
            multiline
            rows={4}
            value={notes}
            onChange={(e) => setNotes(e.target.value)}
            required
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setRejectDialogOpen(false)}>Annuler</Button>
          <Button onClick={handleReject} color="error" variant="contained" disabled={loading}>
            {loading ? <CircularProgress size={24} /> : "Rejeter"}
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default TaskList;