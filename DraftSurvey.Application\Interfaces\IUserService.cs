using DraftSurvey.Application.DTOs;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace DraftSurvey.Application.Interfaces
{
    public interface IUserService
    {
        Task<Guid> CreateUserAsync(UserCreateDto userDto);
        Task UpdateUserAsync(Guid userId, UserUpdateDto userDto);
        Task<AuthResponseDto> AuthenticateAsync(string username, string password);
        Task<UserResponseDto> GetUserByIdAsync(Guid id);
        Task<List<UserResponseDto>> GetUsersByPortAsync(Guid portId);
        Task AssignRoleAsync(Guid userId, Guid roleId);
    }
}