using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using DraftSurvey.Domain.Entities;

namespace DraftSurvey.Infrastructure.Data.Configurations
{
    public class VesselConfiguration : IEntityTypeConfiguration<Vessel>
    {
        public void Configure(EntityTypeBuilder<Vessel> builder)
        {
            builder.HasKey(v => v.Id);

            builder.Property(v => v.Name)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(v => v.IMONumber)
                .IsRequired()
                .HasMaxLength(15);

            builder.Property(v => v.SummerDeadweight)
                .HasColumnType("decimal(18,2)");

            builder.Property(v => v.LengthBetweenPerpendiculars)
                .HasColumnType("decimal(18,2)");

            builder.Property(v => v.LCF)
                .HasColumnType("decimal(18,2)");
        }
    }
}