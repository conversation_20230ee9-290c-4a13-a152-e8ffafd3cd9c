﻿// Dans DraftSurvey.Infrastructure/Data/Configurations/LiquidConfiguration.cs
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using DraftSurvey.Domain.Entities;

namespace DraftSurvey.Infrastructure.Data.Configurations
{
    public class LiquidConfiguration : IEntityTypeConfiguration<Liquid>
    {
        public void Configure(EntityTypeBuilder<Liquid> builder)
        {
            builder.HasKey(l => l.Id);

            builder.Property(l => l.Name)
                .IsRequired()
                .HasMaxLength(100);
            builder.HasIndex(l => l.Name).IsUnique(); // Les noms de liquides devraient être uniques

            builder.Property(l => l.Type)
                .IsRequired()
                .HasMaxLength(50);

            builder.Property(l => l.Unit)
                .HasMaxLength(20);

            builder.Property(l => l.Description)
                .HasMaxLength(500);

            // Types de colonnes
            builder.Property(l => l.StandardDensity).HasColumnType("float");
        }
    }
}