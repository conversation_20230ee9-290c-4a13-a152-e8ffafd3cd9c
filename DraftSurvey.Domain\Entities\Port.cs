using System;
using System.Collections.Generic;

namespace DraftSurvey.Domain.Entities
{
    public class Port
    {
        public Guid Id { get; set; }
        public string Name { get; set; }
        public string Code { get; set; }
        public string Country { get; set; }
        public double StandardWaterDensity { get; set; } = 1.025;

        // Navigation properties
        public virtual ICollection<Escale> Escales { get; set; } = new List<Escale>();
        public virtual ICollection<User> Users { get; set; } = new List<User>();
    }
}