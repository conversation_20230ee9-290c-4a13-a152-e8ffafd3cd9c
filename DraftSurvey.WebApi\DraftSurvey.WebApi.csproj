<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<Nullable>enable</Nullable>
		<ImplicitUsings>enable</ImplicitUsings>
		<TreatWarningsAsErrors>false</TreatWarningsAsErrors>
	</PropertyGroup>

	<ItemGroup>
		<!-- Packages principaux -->
		<PackageReference Include="AutoMapper" Version="12.0.1" />
		<PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="12.0.1" />
		<PackageReference Include="MediatR" Version="12.5.0" />
		<PackageReference Include="MediatR.Extensions.Microsoft.DependencyInjection" Version="11.1.0" />
		<PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="8.0.4" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Relational" Version="9.0.5" />
		<PackageReference Include="Swashbuckle.AspNetCore" Version="8.1.1" />

		<!-- EF Core - Mise à jour vers 9.0.4 pour cohérence -->
		<PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.5" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.5" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.4">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>

		<!-- Packages JWT -->
		<PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.4" />
		<PackageReference Include="Swashbuckle.AspNetCore.Annotations" Version="8.1.1" />
		<PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="7.1.2" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\DraftSurvey.Application\DraftSurvey.Application.csproj" />
		<ProjectReference Include="..\DraftSurvey.Infrastructure\DraftSurvey.Infrastructure.csproj" />
		<ProjectReference Include="..\DraftSurvey.Domain\DraftSurvey.Domain.csproj" />
	</ItemGroup>

	<!-- AutoMapper avec versions compatibles -->

</Project>