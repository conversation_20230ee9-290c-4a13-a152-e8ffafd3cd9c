﻿// In DraftSurvey.Infrastructure/Data/Configurations/HoldInspectionConfiguration.cs

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using DraftSurvey.Domain.Entities;

namespace DraftSurvey.Infrastructure.Data.Configurations
{
    public class HoldInspectionConfiguration : IEntityTypeConfiguration<HoldInspection>
    {
        public void Configure(EntityTypeBuilder<HoldInspection> builder)
        {
            builder.HasKey(hi => hi.Id);

            builder.Property(hi => hi.HoldNumber)
                .IsRequired();

            builder.Property(hi => hi.Condition)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(hi => hi.Notes)
                .HasMaxLength(500);

            // NOUVELLE CONFIGURATION DES PROPRIÉTÉS
            builder.Property(hi => hi.IsApproved)
                .IsRequired(); // This property should always have a value

            // CORRECTED: Changed ReasonForRejection to RejectionReason
            builder.Property(hi => hi.RejectionReason)
                .HasMaxLength(500); // Optional reason, max length 500 characters

            // Relation Many-to-One avec Inspection
            builder.HasOne(hi => hi.Inspection)
                .WithMany(i => i.HoldInspections)
                .HasForeignKey(hi => hi.InspectionId)
                .OnDelete(DeleteBehavior.Cascade); // If an Inspection is deleted, its HoldInspections are also deleted.
        }
    }
}