﻿// In DraftSurvey.Infrastructure/Repositories/FreshWaterTankRepository.cs
using DraftSurvey.Domain.Entities;
using DraftSurvey.Domain.Interfaces;
using DraftSurvey.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace DraftSurvey.Infrastructure.Repositories
{
    public class FreshWaterTankRepository : IFreshWaterTankRepository
    {
        private readonly DraftSurveyDbContext _context;

        public FreshWaterTankRepository(DraftSurveyDbContext context)
        {
            _context = context;
        }

        public async Task<FreshWaterTank> GetByIdAsync(Guid id)
        {
            return await _context.FreshWaterTanks.FindAsync(id);
        }

        public async Task<IEnumerable<FreshWaterTank>> GetBySurveyIdAsync(Guid surveyId)
        {
            return await _context.FreshWaterTanks
                .Where(fwt => fwt.DraftSurveyId == surveyId)
                .ToListAsync();
        }

        public async Task AddAsync(FreshWaterTank tank)
        {
            await _context.FreshWaterTanks.AddAsync(tank);
            // No SaveChangesAsync() here
        }

        public async Task UpdateAsync(FreshWaterTank tank)
        {
            _context.FreshWaterTanks.Update(tank);
            // No SaveChangesAsync() here
        }

        public async Task DeleteAsync(Guid id)
        {
            var tank = await GetByIdAsync(id);
            if (tank != null)
            {
                _context.FreshWaterTanks.Remove(tank);
                // No SaveChangesAsync() here
            }
        }

        public async Task SaveChangesAsync()
        {
            await _context.SaveChangesAsync();
        }
    }
}