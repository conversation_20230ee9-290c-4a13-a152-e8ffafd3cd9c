using AutoMapper;
using DraftSurvey.Application.DTOs;
using DraftSurvey.Domain.Entities;
using DraftSurvey.Domain.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace DraftSurvey.WebApi.Controllers
{
    [Authorize(Roles = "Admin,TeamLead")]
    [ApiController]
    [Route("api/users")]
    public class UsersController : ControllerBase
    {
        private readonly IUserRepository _userRepository;
        private readonly IMapper _mapper;
        private readonly ILogger<UsersController> _logger;

        public UsersController(
            IUserRepository userRepository,
            IMapper mapper,
            ILogger<UsersController> logger)
        {
            _userRepository = userRepository;
            _mapper = mapper;
            _logger = logger;
        }

        [HttpGet]
        public async Task<IActionResult> GetAll()
        {
            var users = await _userRepository.GetAllAsync();
            return Ok(_mapper.Map<IEnumerable<UserResponseDto>>(users));
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetById(Guid id)
        {
            var user = await _userRepository.GetByIdAsync(id);
            if (user == null) return NotFound();
            return Ok(_mapper.Map<UserResponseDto>(user));
        }

        [HttpPost]
        public async Task<IActionResult> Create([FromBody] UserCreateDto dto)
        {
            var user = _mapper.Map<User>(dto);
            var createdUser = await _userRepository.CreateUserAsync(user, dto.Password);
            return CreatedAtAction(nameof(GetById),
                new { id = createdUser.Id },
                _mapper.Map<UserResponseDto>(createdUser));
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> Update(Guid id, [FromBody] UserUpdateDto dto)
        {
            var user = await _userRepository.GetByIdAsync(id);
            if (user == null) return NotFound();

            _mapper.Map(dto, user);
            await _userRepository.UpdateUserAsync(user);
            return NoContent();
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> Delete(Guid id)
        {
            await _userRepository.DeleteUserAsync(id);
            return NoContent();
        }

        [HttpPost("{id}/change-password")]
        public async Task<IActionResult> ChangePassword(Guid id, [FromBody] ChangePasswordDto dto)
        {
            await _userRepository.ChangePasswordAsync(id, dto.NewPassword);
            return NoContent();
        }

        [HttpGet("by-role/{roleName}")]
        public async Task<IActionResult> GetByRole(string roleName)
        {
            var users = await _userRepository.GetUsersByRoleAsync(roleName);
            return Ok(_mapper.Map<IEnumerable<UserResponseDto>>(users));
        }

        [HttpGet("by-port/{portId}")]
        public async Task<IActionResult> GetByPort(Guid portId)
        {
            var users = await _userRepository.GetUsersByPortAsync(portId);
            return Ok(_mapper.Map<IEnumerable<UserResponseDto>>(users));
        }
    }
}