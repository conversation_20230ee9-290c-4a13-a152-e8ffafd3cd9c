﻿using AutoMapper;
using DraftSurvey.Application.DTOs;
using DraftSurvey.Domain.Entities;
using DraftSurvey.Domain.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace DraftSurvey.WebApi.Controllers
{
    [Authorize]
    [ApiController]
    [Route("api/[controller]")]
    public class BallastsController : ControllerBase
    {
        private readonly IBallastRepository _repository;
        private readonly IMapper _mapper;

        public BallastsController(IBallastRepository repository, IMapper mapper)
        {
            _repository = repository;
            _mapper = mapper;
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<BallastDto>> GetById(Guid id)
        {
            var ballast = await _repository.GetByIdAsync(id);
            if (ballast == null) return NotFound();
            return Ok(_mapper.Map<BallastDto>(ballast));
        }

        [HttpGet("by-survey/{surveyId}")]
        public async Task<ActionResult<IEnumerable<BallastDto>>> GetBySurvey(Guid surveyId)
        {
            var ballasts = await _repository.GetBySurveyIdAsync(surveyId);
            return Ok(_mapper.Map<IEnumerable<BallastDto>>(ballasts));
        }

        [HttpPost]
        [Authorize(Roles = "Admin,TeamLead")]
        public async Task<ActionResult<BallastDto>> Create([FromBody] BallastDto dto)
        {
            var ballast = _mapper.Map<Ballast>(dto);
            await _repository.AddAsync(ballast);
            await _repository.SaveChangesAsync();
            return CreatedAtAction(nameof(GetById), new { id = ballast.Id }, _mapper.Map<BallastDto>(ballast));
        }

        [HttpPut("{id}")]
        [Authorize(Roles = "Admin,TeamLead")]
        public async Task<IActionResult> Update(Guid id, [FromBody] BallastDto dto)
        {
            var ballast = await _repository.GetByIdAsync(id);
            if (ballast == null) return NotFound();
            _mapper.Map(dto, ballast);
            await _repository.UpdateAsync(ballast);
            return NoContent();
        }

        [HttpDelete("{id}")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> Delete(Guid id)
        {
            await _repository.DeleteAsync(id);
            return NoContent();
        }
    }
}