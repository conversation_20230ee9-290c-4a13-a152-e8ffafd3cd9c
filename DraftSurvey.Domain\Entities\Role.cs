using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace DraftSurvey.Domain.Entities
{
    public class Role
    {
        public Guid Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public bool CanManageUsers { get; set; }
        public bool CanManageAllPorts { get; set; }

        [JsonIgnore]
        public ICollection<User> Users { get; set; } = new List<User>();
    }
}